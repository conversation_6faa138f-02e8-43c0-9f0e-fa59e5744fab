{"apiVersion": "v1", "kind": "Namespace", "metadata": {"creationTimestamp": "2025-06-17T07:56:37Z", "deletionTimestamp": "2025-07-04T01:17:57Z", "labels": {"kubernetes.io/metadata.name": "argo"}, "name": "argo", "resourceVersion": "247793", "uid": "4c4a6a0e-744c-44af-ae16-a8a1b20feb1e"}, "spec": {}, "status": {"conditions": [{"lastTransitionTime": "2025-07-04T01:18:02Z", "message": "Discovery failed for some groups, 1 failing: unable to retrieve the complete list of server APIs: metrics.k8s.io/v1beta1: stale GroupVersion discovery: metrics.k8s.io/v1beta1", "reason": "DiscoveryFailed", "status": "True", "type": "NamespaceDeletionDiscoveryFailure"}, {"lastTransitionTime": "2025-07-04T01:18:02Z", "message": "All legacy kube types successfully parsed", "reason": "ParsedGroupVersions", "status": "False", "type": "NamespaceDeletionGroupVersionParsingFailure"}, {"lastTransitionTime": "2025-07-04T01:18:45Z", "message": "Failed to delete all resource types, 1 remaining: unexpected items still remain in namespace: argo for gvr: /v1, Resource=pods", "reason": "ContentDeletionFailed", "status": "True", "type": "NamespaceDeletionContentFailure"}, {"lastTransitionTime": "2025-07-04T01:18:02Z", "message": "Some resources are remaining: pods. has 1 resource instances", "reason": "SomeResourcesRemain", "status": "True", "type": "NamespaceContentRemaining"}, {"lastTransitionTime": "2025-07-04T01:18:02Z", "message": "All content-preserving finalizers finished", "reason": "ContentHasNoFinalizers", "status": "False", "type": "NamespaceFinalizersRemaining"}], "phase": "Terminating"}}