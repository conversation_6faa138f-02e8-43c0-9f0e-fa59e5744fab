package leaderelection

import (
	"context"
	"fmt"
	"os"
	"time"

	"github.com/google/uuid"
	"go.uber.org/zap"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/tools/leaderelection"
	"k8s.io/client-go/tools/leaderelection/resourcelock"
)

// LeaderComponent 表示需要进行领导者选举的组件接口
type LeaderComponent interface {
	// Run 在成为领导者后执行的方法
	Run(ctx context.Context)
	// GetName 返回组件名称
	GetName() string
}

// ElectionConfig 领导者选举配置
type ElectionConfig struct {
	// 资源锁的名称前缀
	LockNamePrefix string
	// 命名空间
	Namespace string
	// 租约时长
	LeaseDuration time.Duration
	// 续约截止时间
	RenewDeadline time.Duration
	// 重试间隔
	RetryPeriod time.Duration
}

// DefaultElectionConfig 创建默认的选举配置
func DefaultElectionConfig(namespace string) *ElectionConfig {
	return &ElectionConfig{
		LockNamePrefix: "redis-console",
		Namespace:      namespace,
		LeaseDuration:  15 * time.Second,
		RenewDeadline:  10 * time.Second,
		RetryPeriod:    2 * time.Second,
	}
}

// LeaderElectionManager 领导者选举管理器
type LeaderElectionManager struct {
	logger    *zap.Logger
	k8sClient kubernetes.Interface
	config    *ElectionConfig
}

// NewLeaderElectionManager 创建一个新的领导者选举管理器
func NewLeaderElectionManager(
	logger *zap.Logger,
	k8sClient kubernetes.Interface,
	config *ElectionConfig,
) *LeaderElectionManager {
	return &LeaderElectionManager{
		logger:    logger,
		k8sClient: k8sClient,
		config:    config,
	}
}

// StartLeaderElection 启动指定组件的领导者选举
func (m *LeaderElectionManager) StartLeaderElection(ctx context.Context, component LeaderComponent) error {
	componentName := component.GetName()
	m.logger.Info("启动领导者选举", zap.String("component", componentName))

	// 获取主机名作为唯一标识
	hostname, err := os.Hostname()
	if err != nil {
		// 生成随机UUID作为备选唯一标识
		randomUUID := uuid.New().String()
		hostname = fmt.Sprintf("unknown-host-%s", randomUUID)
		m.logger.Warn("获取主机名失败，使用随机UUID作为标识",
			zap.Error(err),
			zap.String("uuid", randomUUID))
	}

	// 创建一个LeaseLock
	lockName := fmt.Sprintf("%s-%s", m.config.LockNamePrefix, componentName)
	lock := &resourcelock.LeaseLock{
		LeaseMeta: metav1.ObjectMeta{
			Name:      lockName,
			Namespace: m.config.Namespace,
		},
		Client: m.k8sClient.CoordinationV1(),
		LockConfig: resourcelock.ResourceLockConfig{
			Identity: fmt.Sprintf("%s-%s", lockName, hostname),
		},
	}

	// 启动领导者选举
	go leaderelection.RunOrDie(ctx, leaderelection.LeaderElectionConfig{
		Lock:            lock,
		ReleaseOnCancel: true,
		LeaseDuration:   m.config.LeaseDuration,
		RenewDeadline:   m.config.RenewDeadline,
		RetryPeriod:     m.config.RetryPeriod,
		Callbacks: leaderelection.LeaderCallbacks{
			OnStartedLeading: component.Run,
			OnStoppedLeading: func() {
				m.logger.Info("失去领导者身份",
					zap.String("component", componentName),
					zap.String("identity", lock.Identity()),
				)
			},
		},
	})

	return nil
}
