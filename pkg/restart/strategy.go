package restart

import "time"

// RestartStrategy 重启策略
type RestartStrategy string

const (
	// RestartStrategyGraceful 优雅重启
	RestartStrategyGraceful RestartStrategy = "graceful"
	// RestartStrategyForce 强制重启
	RestartStrategyForce RestartStrategy = "force"
)

// RestartConfig 重启配置
type RestartConfig struct {
	Strategy           RestartStrategy `json:"strategy"`                       // 重启策略
	GracePeriodSeconds *int64          `json:"grace_period_seconds,omitempty"` // 优雅停止时间（秒）
	Timeout            time.Duration   `json:"timeout"`                        // 超时时间
	MaxConcurrent      int             `json:"max_concurrent"`                 // 最大并发重启数
}

// DefaultConfig 默认配置
func DefaultConfig() *RestartConfig {
	gracePeriod := int64(30)
	return &RestartConfig{
		Strategy:           RestartStrategyGraceful,
		GracePeriodSeconds: &gracePeriod,
		Timeout:            5 * time.Minute,
		MaxConcurrent:      3,
	}
}

// Validate 验证配置
func (c *RestartConfig) Validate() error {
	if c.Strategy != RestartStrategyGraceful && c.Strategy != RestartStrategyForce {
		return ErrInvalidStrategy
	}
	if c.Timeout <= 0 {
		return ErrInvalidTimeout
	}
	if c.MaxConcurrent <= 0 {
		c.MaxConcurrent = 1
	}
	return nil
}
