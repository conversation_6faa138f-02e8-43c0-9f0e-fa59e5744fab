package restart

import "time"

// RestartEvent 重启事件
type RestartEvent struct {
	Namespace string          `json:"namespace"`
	PodName   string          `json:"pod_name"`
	Strategy  RestartStrategy `json:"strategy"`
	StartTime time.Time       `json:"start_time"`
	EndTime   time.Time       `json:"end_time,omitempty"`
	Duration  time.Duration   `json:"duration"`
	Success   bool            `json:"success"`
	Error     string          `json:"error,omitempty"`
	Reason    string          `json:"reason,omitempty"`
}

// RestartMetrics 重启指标
type RestartMetrics struct {
	TotalAttempts     int64         `json:"total_attempts"`
	SuccessfulRestart int64         `json:"successful_restart"`
	FailedRestart     int64         `json:"failed_restart"`
	AverageDuration   time.Duration `json:"average_duration"`
	LastRestartTime   time.Time     `json:"last_restart_time"`
}

// RestartResult 重启结果
type RestartResult struct {
	TotalPods      int              `json:"total_pods"`
	SuccessfulPods int              `json:"successful_pods"`
	FailedPods     []RestartFailure `json:"failed_pods"`
	Events         []RestartEvent   `json:"events,omitempty"`
	Message        string           `json:"message"`
	StartTime      time.Time        `json:"start_time"`
	EndTime        time.Time        `json:"end_time"`
	Duration       time.Duration    `json:"duration"`
}

// RestartFailure 重启失败信息
type RestartFailure struct {
	PodName string `json:"pod_name"`
	Reason  string `json:"reason"`
	Error   string `json:"error"`
}

// NewRestartResult 创建新的重启结果
func NewRestartResult() *RestartResult {
	return &RestartResult{
		FailedPods: make([]RestartFailure, 0),
		Events:     make([]RestartEvent, 0),
		StartTime:  time.Now(),
	}
}

// AddSuccess 添加成功的重启
func (r *RestartResult) AddSuccess(podName string) {
	r.SuccessfulPods++
	r.TotalPods++
}

// AddFailure 添加失败的重启
func (r *RestartResult) AddFailure(podName, reason, error string) {
	r.FailedPods = append(r.FailedPods, RestartFailure{
		PodName: podName,
		Reason:  reason,
		Error:   error,
	})
	r.TotalPods++
}

// Complete 完成重启操作
func (r *RestartResult) Complete() {
	r.EndTime = time.Now()
	r.Duration = r.EndTime.Sub(r.StartTime)

	if len(r.FailedPods) == 0 {
		r.Message = "所有Pod重启成功"
	} else if r.SuccessfulPods == 0 {
		r.Message = "所有Pod重启失败"
	} else {
		r.Message = "部分Pod重启成功"
	}
}
