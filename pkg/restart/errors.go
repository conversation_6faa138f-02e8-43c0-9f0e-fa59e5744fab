package restart

import "errors"

// 重启相关错误定义
var (
	ErrPodNotFound       = errors.New("pod not found")
	ErrRestartTimeout    = errors.New("restart timeout")
	ErrInsufficientPerms = errors.New("insufficient permissions")
	ErrInvalidStrategy   = errors.New("invalid restart strategy")
	ErrInvalidTimeout    = errors.New("invalid timeout value")
	ErrInvalidNamespace  = errors.New("invalid namespace")
	ErrInvalidPodName    = errors.New("invalid pod name")
	ErrRestartInProgress = errors.New("restart already in progress")
	ErrTooManyFailures   = errors.New("too many restart failures")
)

// RestartError 重启错误结构
type RestartError struct {
	PodName   string
	Namespace string
	Operation string
	Err       error
}

func (e *RestartError) Error() string {
	return e.Err.Error()
}

func (e *RestartError) Unwrap() error {
	return e.Err
}

// NewRestartError 创建重启错误
func NewRestartError(podName, namespace, operation string, err error) *RestartError {
	return &RestartError{
		PodName:   podName,
		Namespace: namespace,
		Operation: operation,
		Err:       err,
	}
}
