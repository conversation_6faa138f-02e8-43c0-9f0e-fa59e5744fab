package http

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"go.uber.org/zap"
)

// PostRequest 发送 POST 请求并返回原始响应体
func PostRequest(ctx context.Context, url string, payload io.Reader, logger *zap.Logger) ([]byte, error) {
	req, err := http.NewRequestWithContext(ctx, "POST", url, payload)
	if err != nil {
		logger.Error("Failed to create request", zap.Error(err))
		return nil, fmt.Errorf("failed to create request: %v", err)
	}

	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		logger.Error("Failed to send request", zap.Error(err))
		return nil, fmt.Errorf("failed to send request: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		logger.Error("Unexpected status code from request", zap.Int("statusCode", resp.StatusCode))
		return nil, fmt.Errorf("unexpected status code from request: %d", resp.StatusCode)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		logger.Error("Failed to read response body", zap.Error(err))
		return nil, fmt.Errorf("failed to read response body: %v", err)
	}

	return body, nil
}

// JsonRequest 发送 JSON 请求并将响应解析为指定类型
func JsonRequest[T any](ctx context.Context, url string, requestBody interface{}, logger *zap.Logger) (*T, error) {
	var payload io.Reader

	if requestBody != nil {
		jsonData, err := json.Marshal(requestBody)
		if err != nil {
			logger.Error("序列化请求体失败", zap.Error(err))
			return nil, fmt.Errorf("序列化请求体失败: %w", err)
		}
		payload = bytes.NewBuffer(jsonData)
	} else {
		payload = bytes.NewReader([]byte("{}"))
	}

	responseBody, err := PostRequest(ctx, url, payload, logger)
	if err != nil {
		return nil, err
	}

	// 解析响应
	var result T
	err = json.Unmarshal(responseBody, &result)
	if err != nil {
		logger.Error("解析响应体失败",
			zap.Error(err),
			zap.String("response", string(responseBody)))
		return nil, fmt.Errorf("解析响应体失败: %w", err)
	}

	return &result, nil
}
