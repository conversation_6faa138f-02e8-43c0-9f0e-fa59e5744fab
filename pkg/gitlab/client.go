package gitlab

import (
	"fmt"
	"sync"

	"github.com/spf13/viper"
	gitlab "gitlab.com/gitlab-org/api/client-go"
	"go.uber.org/zap"
)

// Client GitLab客户端
type Client struct {
	baseURL string
	token   string
	logger  *zap.Logger
	client  *gitlab.Client
}

var (
	instance *Client
	once     sync.Once
)

// GetClient 获取GitLab客户端实例（单例模式）
func GetClient(config *viper.Viper, logger *zap.Logger) *Client {
	once.Do(func() {
		instance = NewClient(config, logger)
	})
	return instance
}

// NewClient 创建GitLab客户端
func NewClient(config *viper.Viper, logger *zap.Logger) *Client {
	baseURL := config.GetString("gitlab.base_url")
	token := config.GetString("gitlab.token")

	client := &Client{
		baseURL: baseURL,
		token:   token,
		logger:  logger,
	}

	// 初始化GitLab API客户端
	err := client.init()
	if err != nil {
		logger.Error("初始化GitLab客户端失败", zap.Error(err))
	}

	return client
}

// init 初始化GitLab API客户端
func (c *Client) init() error {
	if c.baseURL == "" || c.token == "" {
		return fmt.Errorf("GitLab配置不完整, baseURL或token为空")
	}

	gitlabClient, err := gitlab.NewClient(c.token, gitlab.WithBaseURL(c.baseURL))
	if err != nil {
		return fmt.Errorf("创建GitLab客户端失败: %w", err)
	}

	c.client = gitlabClient
	c.logger.Info("GitLab客户端初始化成功", zap.String("base_url", c.baseURL))

	return nil
}

// GetGitLabAPI 获取GitLab API客户端
func (c *Client) GetGitLabAPI() *gitlab.Client {
	return c.client
}
