package podexecutor

import (
	"bytes"
	"ci-gateway/pkg/k8sclient"
	"context"
	"strings"

	"go.uber.org/zap"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/client-go/kubernetes/scheme"
	"k8s.io/client-go/tools/remotecommand"
)

type PodContext struct {
	Namespace     string
	PodName       string
	ContainerName string
}

// PodExec handles Kubernetes operations
type Executor struct {
	Logger     *zap.Logger
	PodContext PodContext
	Executor   PodExecutor // 用于测试的mock执行器
}

// PodExecutor 定义了执行命令的接口
type PodExecutor interface {
	Execute(ctx context.Context, command []string) (string, string, error)
}

// SetExecutor 设置mock执行器（仅用于测试）
func (e *Executor) SetExecutor(executor PodExecutor) {
	e.Executor = executor
}

// NewPodExec creates a new PodExec
func NewPodExec(logger *zap.Logger, podContext PodContext) *Executor {
	return &Executor{
		Logger:     logger,
		PodContext: podContext,
	}
}

// prepareCommand prepares the command by adding "sh -c" prefix if necessary
func (km *Executor) prepareCommand(command []string) []string {
	// 如果命令已经以 "sh", "-c" 开头，则不需要再添加
	if len(command) >= 2 && command[0] == "sh" && command[1] == "-c" {
		return command
	}

	// 如果命令只有一个元素，直接添加 "sh", "-c" 前缀
	if len(command) == 1 {
		return []string{"sh", "-c", command[0]}
	}

	// 对于多个元素的命令，将其合并为一个字符串，并添加 "sh", "-c" 前缀
	joinedCommand := strings.Join(command, " ")
	return []string{"sh", "-c", joinedCommand}
}

// Execute executes a command in a pod using SPDY
func (e *Executor) Execute(ctx context.Context, command []string) (string, string, error) {
	if e.Executor != nil {
		return e.Executor.Execute(ctx, command)
	}

	shellCommand := e.prepareCommand(command)

	req := k8sclient.GetClientSet().CoreV1().RESTClient().Post().
		Resource("pods").
		Name(e.PodContext.PodName).
		Namespace(e.PodContext.Namespace).
		SubResource("exec").
		Param("container", e.PodContext.ContainerName)

	req.VersionedParams(&corev1.PodExecOptions{
		Command: shellCommand,
		Stdout:  true,
		Stderr:  true,
	}, scheme.ParameterCodec)

	config, err := k8sclient.GenerateK8sConfig()()
	if err != nil {
		e.Logger.Error("生成k8s配置时出错", zap.Error(err), zap.Strings("命令", shellCommand))
		return "", "", err
	}

	exec, err := remotecommand.NewSPDYExecutor(config, "POST", req.URL())
	if err != nil {
		e.Logger.Error("创建SPDY执行器时出错", zap.Error(err), zap.Strings("命令", shellCommand))
		return "", "", err
	}

	var stdout, stderr bytes.Buffer
	err = exec.StreamWithContext(ctx, remotecommand.StreamOptions{
		Stdout: &stdout,
		Stderr: &stderr,
	})

	stdoutStr, stderrStr := stdout.String(), stderr.String()

	if err != nil {
		e.Logger.Error("执行过程中出错",
			zap.Error(err),
			zap.String("标准输出", stdoutStr),
			zap.String("标准错误", stderrStr),
			zap.Strings("命令", shellCommand),
		)

		return stdoutStr, stderrStr, err
	}

	e.Logger.Info("执行命令", zap.String("Pod名称", e.PodContext.PodName), zap.Strings("命令", shellCommand), zap.String("输出", stdoutStr))

	return stdoutStr, stderrStr, nil
}
