package utils

import (
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// GetUserIDFromContext 从上下文中获取当前用户ID
func GetUserIDFromContext(c *gin.Context) int64 {
	if c == nil {
		return 0
	}

	// 从上下文中获取用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		return 0
	}

	// 转换为int64
	switch v := userID.(type) {
	case int64:
		return v
	case int:
		return int64(v)
	case float64:
		return int64(v)
	default:
		return 0
	}
}

// GenerateUUID 生成UUID
func GenerateUUID() string {
	return uuid.New().String()
}

// GenerateShortUUID 生成短UUID
func GenerateShortUUID() string {
	id := uuid.New().String()
	return strings.Replace(id, "-", "", -1)[:8]
}
