package k8sclient

import (
	"fmt"
	"sync"

	"k8s.io/client-go/dynamic"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"
)

// ClientSet 是全局的 Kubernetes Clientset
var (
	ClientSet     kubernetes.Interface
	DynamicClient dynamic.Interface
	once          sync.Once
	initErr       error
)

// K8sConfigProvider 定义获取 Kubernetes 配置的方法类型
type K8sConfigProvider func() (*rest.Config, error)

// Init 初始化全局的 Kubernetes Clientset 和 Dynamic Client
func Init() error {
	once.Do(func() {
		configProvider := GenerateK8sConfig()
		config, err := configProvider()
		if err != nil {
			initErr = err
			return
		}
		ClientSet, err = kubernetes.NewForConfig(config)
		if err != nil {
			initErr = fmt.Errorf("failed to create Kubernetes clientset: %w", err)
			return
		}

		// 创建 Dynamic Client
		DynamicClient, err = dynamic.NewForConfig(config)
		if err != nil {
			initErr = fmt.Errorf("failed to create Kubernetes dynamic client: %w", err)
			return
		}
	})
	return initErr
}

// GetClientSet 返回全局的 Kubernetes Clientset，如果尚未初始化则进行初始化
func GetClientSet() kubernetes.Interface {
	if ClientSet == nil {
		if err := Init(); err != nil {
			return nil
		}
	}
	return ClientSet
}

// GetDynamicClient 返回全局的 Kubernetes Dynamic Client，如果尚未初始化则进行初始化
func GetDynamicClient() dynamic.Interface {
	if DynamicClient == nil {
		if err := Init(); err != nil {
			return nil
		}
	}
	return DynamicClient
}

// GenerateK8sClient 创建一个新的 Kubernetes Clientset
func GenerateK8sClient(configProvider K8sConfigProvider) (kubernetes.Interface, error) {
	config, err := configProvider()
	if err != nil {
		return nil, err
	}
	return kubernetes.NewForConfig(config)
}

// GenerateK8sDynamicClient 创建一个新的 Kubernetes Dynamic Client
func GenerateK8sDynamicClient(configProvider K8sConfigProvider) (dynamic.Interface, error) {
	config, err := configProvider()
	if err != nil {
		return nil, err
	}
	return dynamic.NewForConfig(config)
}

// GenerateK8sConfig 加载 kubeconfig 文件并返回配置提供者
func GenerateK8sConfig() K8sConfigProvider {
	return func() (*rest.Config, error) {
		loadingRules := clientcmd.NewDefaultClientConfigLoadingRules()
		configOverrides := &clientcmd.ConfigOverrides{}
		kubeConfig := clientcmd.NewNonInteractiveDeferredLoadingClientConfig(loadingRules, configOverrides)
		return kubeConfig.ClientConfig()
	}
}
