package config

import (
	"os"

	"github.com/spf13/viper"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"gopkg.in/natefinch/lumberjack.v2"
)

// NewLogger 创建 zap logger（带文件滚动）
func NewLogger(conf *viper.Viper) *zap.Logger {
	// 获取环境配置，默认为开发模式
	env := conf.GetString("env")
	if env == "" {
		env = "development"
	}

	// 获取日志配置
	logLevel := conf.GetString("log.level")
	if logLevel == "" {
		logLevel = "info"
	}

	encoding := conf.GetString("log.encoding")
	if encoding == "" {
		encoding = conf.GetString("log.format")
	}
	if encoding == "" {
		encoding = "console"
	}

	// 解析日志级别
	var level zapcore.Level
	switch logLevel {
	case "debug":
		level = zapcore.DebugLevel
	case "info":
		level = zapcore.InfoLevel
	case "warn":
		level = zapcore.WarnLevel
	case "error":
		level = zapcore.ErrorLevel
	default:
		level = zapcore.InfoLevel
	}

	var config zap.Config
	if env == "prod" || env == "stage" {
		config = zap.NewProductionConfig()
	} else {
		config = zap.NewDevelopmentConfig()
	}

	// 设置日志级别
	config.Level = zap.NewAtomicLevelAt(level)

	// 设置编码格式
	config.Encoding = encoding

	// 设置时间格式
	config.EncoderConfig.TimeKey = "timestamp"
	config.EncoderConfig.EncodeTime = zapcore.ISO8601TimeEncoder

	if encoding == "console" {
		config.EncoderConfig.EncodeLevel = zapcore.CapitalColorLevelEncoder
	}

	// 检查是否需要输出到文件
	logFileName := conf.GetString("log.log_file_name")
	if logFileName != "" {
		// 配置文件输出
		lumberJackLogger := &lumberjack.Logger{
			Filename:   logFileName,
			MaxSize:    conf.GetInt("log.max_size"),    // MB
			MaxBackups: conf.GetInt("log.max_backups"), // 备份数量
			MaxAge:     conf.GetInt("log.max_age"),     // 天数
			Compress:   conf.GetBool("log.compress"),   // 是否压缩
		}

		// 创建多输出：控制台 + 文件
		consoleEncoder := zapcore.NewConsoleEncoder(config.EncoderConfig)
		fileEncoder := zapcore.NewJSONEncoder(config.EncoderConfig)

		core := zapcore.NewTee(
			zapcore.NewCore(consoleEncoder, zapcore.AddSync(os.Stdout), level),
			zapcore.NewCore(fileEncoder, zapcore.AddSync(lumberJackLogger), level),
		)

		logger := zap.New(core, zap.AddCaller(), zap.AddStacktrace(zapcore.ErrorLevel))
		return logger
	}

	// 只输出到控制台
	logger, err := config.Build()
	if err != nil {
		panic(err)
	}

	return logger
}

// NewSimpleFileLogger 仅使用 zap 的文件输出（无滚动功能）
func NewSimpleFileLogger(conf *viper.Viper) *zap.Logger {
	logFileName := conf.GetString("log.log_file_name")
	if logFileName == "" {
		logFileName = "app.log"
	}

	config := zap.NewProductionConfig()
	config.OutputPaths = []string{
		"stdout",    // 控制台
		logFileName, // 文件
	}

	logger, err := config.Build()
	if err != nil {
		panic(err)
	}

	return logger
}

// NewSimpleLogger 创建简单的 zap logger（用于快速开始）
func NewSimpleLogger() *zap.Logger {
	logger, err := zap.NewDevelopment()
	if err != nil {
		panic(err)
	}
	return logger
}

// NewProductionLogger 创建生产环境 logger
func NewProductionLogger() *zap.Logger {
	logger, err := zap.NewProduction()
	if err != nil {
		panic(err)
	}
	return logger
}
