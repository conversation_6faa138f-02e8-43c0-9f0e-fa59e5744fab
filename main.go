package main

import (
	"ci-gateway/pkg/config"
	"ci-gateway/pkg/k8sclient"
	"ci-gateway/wire"
	"context"
	"os"
	"os/signal"
	"syscall"

	"go.uber.org/zap"
)

func main() {
	conf := config.NewConfig()
	logger := config.NewLogger(conf)
	defer logger.Sync()

	logger.Info("server start",
		zap.String("host", "http://127.0.0.1:"+conf.GetString("http.port")),
		zap.String("env", conf.GetString("env")))

	// 初始化 Kubernetes 客户端
	if err := k8sclient.Init(); err != nil {
		logger.Error("初始化Kubernetes客户端失败", zap.Error(err))
		os.Exit(1)
	}

	app, cleanup, err := wire.NewWire(conf, logger)
	if err != nil {
		logger.Fatal("初始化app失败", zap.Error(err))
	}
	defer cleanup()

	ctx, stop := signal.NotifyContext(context.Background(), os.Interrupt, syscall.SIGTERM)
	defer stop()

	// electionConfig := leaderelection.DefaultElectionConfig(conf.GetString("namespace"))
	// electionManager := leaderelection.NewLeaderElectionManager(
	// 	logger,
	// 	k8sclient.GetClientSet(),
	// 	electionConfig,
	// )

	// // 注册并启动
	// if err := electionManager.StartLeaderElection(ctx, app.WorkflowService.AsLeaderComponent()); err != nil {
	// 	logger.Error("启动WorkflowService领导者选举失败", zap.Error(err))
	// }

	logger.Info("启动HTTP服务器")
	if err := app.Server.Start(ctx); err != nil {
		logger.Fatal("启动HTTP服务器失败", zap.Error(err))
	}
}
