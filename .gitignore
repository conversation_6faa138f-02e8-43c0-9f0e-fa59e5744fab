/vendor/
/bin/
release/
vendor/
__debug_bin*
doc/env
scripts/*
# IDE相关
.idea/
.vscode/
*.swp
*.swo
.DS_Store
*.sublime*
.history/
.atom/
.buildpath
.project
*.tmproj
*.esproj

*.log
logs/


# 构建输出
/webapp/build/
/webapp/dist/
/webapp/.next/
/webapp/out/

# UmiJS相关
/webapp/.umi/
/webapp/.umi-production/
/webapp/.env.local
/webapp/.umirc.local.ts
/webapp/config/config.local.ts
/webapp/src/.umi/
/webapp/src/.umi-production/
/webapp/node_modules/.cache/
/webapp/.mfsu/
/webapp/.mfsu-production/
/webapp/src/.umi-test/
/webapp/.dumi/tmp
/webapp/.dumi/tmp-production

# 添加新的行
/webapp/node_modules/