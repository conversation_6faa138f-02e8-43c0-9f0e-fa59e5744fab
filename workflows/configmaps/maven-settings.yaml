apiVersion: v1
kind: ConfigMap
metadata:
  name: maven-settings-xml
data:
  settings.xml: |
    <?xml version="1.0" encoding="UTF-8"?>
    <settings xmlns="http://maven.apache.org/SETTINGS/1.0.0"
              xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
              xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0 http://maven.apache.org/xsd/settings-1.0.0.xsd">
      <localRepository>${user.home}/.m2/repository</localRepository>
      <interactiveMode>true</interactiveMode>
      <offline>false</offline>
      <pluginGroups>
      </pluginGroups>
      <proxies>
      </proxies>
      <servers>
          <server>
            <id>releases</id>
            <username>maqiang</username>
            <password>maqiang111</password>
        </server>
        <server>
          <id>snapshots</id>
          <username>develop</username>
          <password>000000</password>
        </server>
        <server>
          <id>API</id>
          <username>develop</username>
          <password>000000</password>
        </server>
      </servers>
      <mirrors>
        <mirror>
          <id>nexus</id>
          <name>sprucetec nexus</name>
          <url>https://mvn.sprucetec.com/nexus/content/groups/public</url>
          <mirrorOf>*</mirrorOf>
        </mirror>
      </mirrors>
      <profiles>
        <profile>
          <id>nexus</id>
          <repositories>
            <repository>
              <id>central</id>
              <url>https://mvn.sprucetec.com/nexus/content/groups/public</url>
              <releases>
                <enabled>true</enabled>
              </releases>
              <snapshots>
                <enabled>true</enabled>
              </snapshots>
            </repository>
          </repositories>
          <pluginRepositories>
            <pluginRepository>
              <id>central</id>
              <url>https://mvn.sprucetec.com/nexus/content/groups/public</url>
              <releases>
                <enabled>true</enabled>
              </releases>
              <snapshots>
                <enabled>true</enabled>
              </snapshots>
            </pluginRepository>
          </pluginRepositories>
        </profile>
      </profiles>
      <activeProfiles>
        <activeProfile>nexus</activeProfile>
      </activeProfiles>
    </settings> 