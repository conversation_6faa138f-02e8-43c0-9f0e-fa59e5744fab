apiVersion: argoproj.io/v1alpha1
kind: WorkflowTemplate
metadata:
  name: argo-rollouts-deployment
  namespace: argo
spec:
  entrypoint: rollout-deployment-pipeline
  serviceAccountName: argo-workflow
  
  # 模板参数定义
  arguments:
    parameters:
      - name: app_id
        description: "应用ID"
      - name: env_id
        description: "环境ID"
      - name: deploy_task_id
        description: "部署任务ID"
      - name: image_registry
        description: "镜像仓库地址"
      - name: image_repo
        description: "镜像仓库名"
      - name: image_tag
        description: "镜像标签"
      - name: deployment_config
        description: "部署配置JSON（从API动态获取）"

  templates:
    # 主部署流水线
    - name: rollout-deployment-pipeline
      steps:
      # 第一步：解析部署配置
      - - name: parse-deployment-config
          template: parse-config
          arguments:
            parameters:
              - name: app_id
                value: "{{workflow.parameters.app_id}}"
              - name: env_id
                value: "{{workflow.parameters.env_id}}"
              - name: deployment_config
                value: "{{workflow.parameters.deployment_config}}"

      # 第二步：执行部署
      - - name: execute-deployment
          template: execute-rollout-deployment
          arguments:
            parameters:
              - name: app_id
                value: "{{workflow.parameters.app_id}}"
              - name: env_id
                value: "{{workflow.parameters.env_id}}"
              - name: deploy_task_id
                value: "{{workflow.parameters.deploy_task_id}}"
              - name: image_registry
                value: "{{workflow.parameters.image_registry}}"
              - name: image_repo
                value: "{{workflow.parameters.image_repo}}"
              - name: image_tag
                value: "{{workflow.parameters.image_tag}}"
              - name: rollout_name
                value: "{{steps.parse-deployment-config.outputs.parameters.rollout_name}}"
              - name: namespace
                value: "{{steps.parse-deployment-config.outputs.parameters.namespace}}"
              - name: replica_count
                value: "{{steps.parse-deployment-config.outputs.parameters.replica_count}}"
              - name: service_port
                value: "{{steps.parse-deployment-config.outputs.parameters.service_port}}"
              - name: container_port
                value: "{{steps.parse-deployment-config.outputs.parameters.container_port}}"
              - name: rollout_strategy
                value: "{{steps.parse-deployment-config.outputs.parameters.rollout_strategy}}"
              - name: max_surge
                value: "{{steps.parse-deployment-config.outputs.parameters.max_surge}}"
              - name: max_unavailable
                value: "{{steps.parse-deployment-config.outputs.parameters.max_unavailable}}"

      # 第三步：等待部署完成
      - - name: wait-deployment-complete
          template: wait-rollout-ready
          arguments:
            parameters:
              - name: rollout_name
                value: "{{steps.parse-deployment-config.outputs.parameters.rollout_name}}"
              - name: namespace
                value: "{{steps.parse-deployment-config.outputs.parameters.namespace}}"
              - name: timeout
                value: "900"  # 15分钟超时

      # 第四步：更新部署状态
      - - name: update-deployment-status
          templateRef:
            name: common-templates
            template: update-task-detail-status
          arguments:
            parameters:
              - name: deploy_task_id
                value: "{{workflow.parameters.deploy_task_id}}"
              - name: group_id
                value: "{{workflow.parameters.app_id}}"
              - name: status
                value: "completed"
              - name: message
                value: "部署完成 - 策略: {{steps.parse-deployment-config.outputs.parameters.rollout_strategy}}, 镜像: {{workflow.parameters.image_registry}}/{{workflow.parameters.image_repo}}:{{workflow.parameters.image_tag}}"

    # 解析部署配置模板
    - name: parse-config
      inputs:
        parameters:
          - name: app_id
          - name: env_id
          - name: deployment_config
      outputs:
        parameters:
          - name: rollout_name
            valueFrom:
              path: /tmp/rollout_name
          - name: namespace
            valueFrom:
              path: /tmp/namespace
          - name: replica_count
            valueFrom:
              path: /tmp/replica_count
          - name: service_port
            valueFrom:
              path: /tmp/service_port
          - name: container_port
            valueFrom:
              path: /tmp/container_port
          - name: rollout_strategy
            valueFrom:
              path: /tmp/rollout_strategy
          - name: max_surge
            valueFrom:
              path: /tmp/max_surge
          - name: max_unavailable
            valueFrom:
              path: /tmp/max_unavailable
      container:
        image: alpine:3.18
        command: [sh, -c]
        args:
          - |
            set -e
            
            # 安装 jq 用于JSON解析
            apk add --no-cache jq
            
            echo "=========================================="
            echo "📋 解析部署配置"
            echo "=========================================="
            echo "应用ID: {{inputs.parameters.app_id}}"
            echo "环境ID: {{inputs.parameters.env_id}}"
            echo "原始配置: {{inputs.parameters.deployment_config}}"
            echo "=========================================="
            
            # 将配置JSON写入文件
            echo '{{inputs.parameters.deployment_config}}' > /tmp/config.json
            
            # 验证JSON格式
            if ! jq . /tmp/config.json > /dev/null 2>&1; then
              echo "❌ 错误: 部署配置不是有效的JSON格式"
              exit 1
            fi
            
            # 解析各个配置项，如果不存在则使用默认值
            echo "📋 解析配置项..."
            
            # rollout_name（默认为 app_id + "-rollout"）
            rollout_name=$(jq -r '.rollout_name // "{{inputs.parameters.app_id}}-rollout"' /tmp/config.json)
            echo "$rollout_name" > /tmp/rollout_name
            echo "Rollout名称: $rollout_name"
            
            # namespace（默认为 default）
            namespace=$(jq -r '.namespace // "default"' /tmp/config.json)
            echo "$namespace" > /tmp/namespace
            echo "命名空间: $namespace"
            
            # replica_count（默认为 3）
            replica_count=$(jq -r '.replica_count // 3' /tmp/config.json)
            echo "$replica_count" > /tmp/replica_count
            echo "副本数量: $replica_count"
            
            # service_port（默认为 8080）
            service_port=$(jq -r '.service_port // 8080' /tmp/config.json)
            echo "$service_port" > /tmp/service_port
            echo "服务端口: $service_port"
            
            # container_port（默认为 8080）
            container_port=$(jq -r '.container_port // 8080' /tmp/config.json)
            echo "$container_port" > /tmp/container_port
            echo "容器端口: $container_port"
            
            # rollout_strategy（默认为 blueGreen）
            rollout_strategy=$(jq -r '.rollout_strategy // "blueGreen"' /tmp/config.json)
            echo "$rollout_strategy" > /tmp/rollout_strategy
            echo "部署策略: $rollout_strategy"
            
            # max_surge（默认为 25%）
            max_surge=$(jq -r '.max_surge // "25%"' /tmp/config.json)
            echo "$max_surge" > /tmp/max_surge
            echo "最大增加: $max_surge"
            
            # max_unavailable（默认为 25%）
            max_unavailable=$(jq -r '.max_unavailable // "25%"' /tmp/config.json)
            echo "$max_unavailable" > /tmp/max_unavailable
            echo "最大不可用: $max_unavailable"
            
            echo "=========================================="
            echo "✅ 配置解析完成"
            echo "=========================================="

    # 执行 Rollout 部署
    - name: execute-rollout-deployment
      inputs:
        parameters:
          - name: app_id
          - name: env_id
          - name: deploy_task_id
          - name: image_registry
          - name: image_repo
          - name: image_tag
          - name: rollout_name
          - name: namespace
          - name: replica_count
          - name: service_port
          - name: container_port
          - name: rollout_strategy
          - name: max_surge
          - name: max_unavailable
      steps:
      - - name: apply-rollout-by-strategy
          template: apply-rollout-by-strategy
          arguments:
            parameters:
              - name: app_id
                value: "{{inputs.parameters.app_id}}"
              - name: image_registry
                value: "{{inputs.parameters.image_registry}}"
              - name: image_repo
                value: "{{inputs.parameters.image_repo}}"
              - name: image_tag
                value: "{{inputs.parameters.image_tag}}"
              - name: rollout_name
                value: "{{inputs.parameters.rollout_name}}"
              - name: namespace
                value: "{{inputs.parameters.namespace}}"
              - name: replica_count
                value: "{{inputs.parameters.replica_count}}"
              - name: service_port
                value: "{{inputs.parameters.service_port}}"
              - name: container_port
                value: "{{inputs.parameters.container_port}}"
              - name: rollout_strategy
                value: "{{inputs.parameters.rollout_strategy}}"
              - name: max_surge
                value: "{{inputs.parameters.max_surge}}"
              - name: max_unavailable
                value: "{{inputs.parameters.max_unavailable}}"

    # 根据策略应用不同的 Rollout 配置
    - name: apply-rollout-by-strategy
      inputs:
        parameters:
          - name: app_id
          - name: image_registry
          - name: image_repo
          - name: image_tag
          - name: rollout_name
          - name: namespace
          - name: replica_count
          - name: service_port
          - name: container_port
          - name: rollout_strategy
          - name: max_surge
          - name: max_unavailable
      steps:
      - - name: apply-bluegreen-rollout
          template: apply-bluegreen-rollout
          when: "{{inputs.parameters.rollout_strategy}} == blueGreen"
          arguments:
            parameters:
              - name: app_id
                value: "{{inputs.parameters.app_id}}"
              - name: image_registry
                value: "{{inputs.parameters.image_registry}}"
              - name: image_repo
                value: "{{inputs.parameters.image_repo}}"
              - name: image_tag
                value: "{{inputs.parameters.image_tag}}"
              - name: rollout_name
                value: "{{inputs.parameters.rollout_name}}"
              - name: namespace
                value: "{{inputs.parameters.namespace}}"
              - name: replica_count
                value: "{{inputs.parameters.replica_count}}"
              - name: service_port
                value: "{{inputs.parameters.service_port}}"
              - name: container_port
                value: "{{inputs.parameters.container_port}}"
        - name: apply-canary-rollout
          template: apply-canary-rollout
          when: "{{inputs.parameters.rollout_strategy}} == canary"
          arguments:
            parameters:
              - name: app_id
                value: "{{inputs.parameters.app_id}}"
              - name: image_registry
                value: "{{inputs.parameters.image_registry}}"
              - name: image_repo
                value: "{{inputs.parameters.image_repo}}"
              - name: image_tag
                value: "{{inputs.parameters.image_tag}}"
              - name: rollout_name
                value: "{{inputs.parameters.rollout_name}}"
              - name: namespace
                value: "{{inputs.parameters.namespace}}"
              - name: replica_count
                value: "{{inputs.parameters.replica_count}}"
              - name: service_port
                value: "{{inputs.parameters.service_port}}"
              - name: container_port
                value: "{{inputs.parameters.container_port}}"
              - name: max_surge
                value: "{{inputs.parameters.max_surge}}"
              - name: max_unavailable
                value: "{{inputs.parameters.max_unavailable}}"

    # Blue-Green 策略的 Rollout
    - name: apply-bluegreen-rollout
      inputs:
        parameters:
          - name: app_id
          - name: image_registry
          - name: image_repo
          - name: image_tag
          - name: rollout_name
          - name: namespace
          - name: replica_count
          - name: service_port
          - name: container_port
      resource:
        action: apply
        manifest: |
          apiVersion: argoproj.io/v1alpha1
          kind: Rollout
          metadata:
            name: {{inputs.parameters.rollout_name}}
            namespace: {{inputs.parameters.namespace}}
            labels:
              app: {{inputs.parameters.app_id}}
              version: {{inputs.parameters.image_tag}}
              managed-by: argo-workflows
          spec:
            replicas: {{inputs.parameters.replica_count}}
            strategy:
              blueGreen:
                activeService: {{inputs.parameters.rollout_name}}-active
                previewService: {{inputs.parameters.rollout_name}}-preview
                autoPromotionEnabled: false  # 需要手动确认才能从预览切换到生产
                scaleDownDelaySeconds: 30
                prePromotionAnalysis:
                  templates:
                  - templateName: success-rate
                  args:
                  - name: service-name
                    value: {{inputs.parameters.rollout_name}}-preview
            selector:
              matchLabels:
                app: {{inputs.parameters.app_id}}
            template:
              metadata:
                labels:
                  app: {{inputs.parameters.app_id}}
                  version: {{inputs.parameters.image_tag}}
              spec:
                containers:
                - name: {{inputs.parameters.app_id}}
                  image: {{inputs.parameters.image_registry}}/{{inputs.parameters.image_repo}}:{{inputs.parameters.image_tag}}
                  ports:
                  - containerPort: {{inputs.parameters.container_port}}
                    protocol: TCP
                  env:
                  - name: JAVA_OPTS
                    value: "-XX:+UseContainerSupport -XX:MaxRAMPercentage=80 -Xshare:auto"
                  - name: APP_VERSION
                    value: "{{inputs.parameters.image_tag}}"
                  - name: DEPLOYMENT_TIME
                    value: "{{workflow.creationTimestamp}}"
                  readinessProbe:
                    httpGet:
                      path: /actuator/health/readiness
                      port: {{inputs.parameters.container_port}}
                    initialDelaySeconds: 20
                    periodSeconds: 10
                    timeoutSeconds: 5
                    failureThreshold: 3
                  livenessProbe:
                    httpGet:
                      path: /actuator/health/liveness
                      port: {{inputs.parameters.container_port}}
                    initialDelaySeconds: 60
                    periodSeconds: 30
                    timeoutSeconds: 5
                    failureThreshold: 3
                  resources:
                    requests:
                      memory: "512Mi"
                      cpu: "500m"
                    limits:
                      memory: "1Gi"
                      cpu: "1000m"
          ---
          apiVersion: v1
          kind: Service
          metadata:
            name: {{inputs.parameters.rollout_name}}-active
            namespace: {{inputs.parameters.namespace}}
            labels:
              app: {{inputs.parameters.app_id}}
              service-type: active
          spec:
            ports:
            - port: {{inputs.parameters.service_port}}
              targetPort: {{inputs.parameters.container_port}}
              protocol: TCP
              name: http
            selector:
              app: {{inputs.parameters.app_id}}
          ---
          apiVersion: v1
          kind: Service
          metadata:
            name: {{inputs.parameters.rollout_name}}-preview
            namespace: {{inputs.parameters.namespace}}
            labels:
              app: {{inputs.parameters.app_id}}
              service-type: preview
          spec:
            ports:
            - port: {{inputs.parameters.service_port}}
              targetPort: {{inputs.parameters.container_port}}
              protocol: TCP
              name: http
            selector:
              app: {{inputs.parameters.app_id}}

    # Canary 策略的 Rollout
    - name: apply-canary-rollout
      inputs:
        parameters:
          - name: app_id
          - name: image_registry
          - name: image_repo
          - name: image_tag
          - name: rollout_name
          - name: namespace
          - name: replica_count
          - name: service_port
          - name: container_port
          - name: max_surge
          - name: max_unavailable
      resource:
        action: apply
        manifest: |
          apiVersion: argoproj.io/v1alpha1
          kind: Rollout
          metadata:
            name: {{inputs.parameters.rollout_name}}
            namespace: {{inputs.parameters.namespace}}
            labels:
              app: {{inputs.parameters.app_id}}
              version: {{inputs.parameters.image_tag}}
              managed-by: argo-workflows
          spec:
            replicas: {{inputs.parameters.replica_count}}
            strategy:
              canary:
                steps:
                - setWeight: 20
                - pause: {}  # 手动确认：20% 流量完成后暂停，需要手动 promote
                - setWeight: 40
                - pause: {}  # 手动确认：40% 流量完成后暂停，需要手动 promote
                - setWeight: 60
                - pause: {}  # 手动确认：60% 流量完成后暂停，需要手动 promote
                - setWeight: 80
                - pause: {}  # 手动确认：80% 流量完成后暂停，需要手动 promote
                - setWeight: 100
                rollingUpdate:
                  maxSurge: {{inputs.parameters.max_surge}}
                  maxUnavailable: {{inputs.parameters.max_unavailable}}
            selector:
              matchLabels:
                app: {{inputs.parameters.app_id}}
            template:
              metadata:
                labels:
                  app: {{inputs.parameters.app_id}}
                  version: {{inputs.parameters.image_tag}}
              spec:
                containers:
                - name: {{inputs.parameters.app_id}}
                  image: {{inputs.parameters.image_registry}}/{{inputs.parameters.image_repo}}:{{inputs.parameters.image_tag}}
                  ports:
                  - containerPort: {{inputs.parameters.container_port}}
                    protocol: TCP
                  env:
                  - name: JAVA_OPTS
                    value: "-XX:+UseContainerSupport -XX:MaxRAMPercentage=80 -Xshare:auto"
                  - name: APP_VERSION
                    value: "{{inputs.parameters.image_tag}}"
                  - name: DEPLOYMENT_TIME
                    value: "{{workflow.creationTimestamp}}"
                  readinessProbe:
                    httpGet:
                      path: /actuator/health/readiness
                      port: {{inputs.parameters.container_port}}
                    initialDelaySeconds: 20
                    periodSeconds: 10
                    timeoutSeconds: 5
                    failureThreshold: 3
                  livenessProbe:
                    httpGet:
                      path: /actuator/health/liveness
                      port: {{inputs.parameters.container_port}}
                    initialDelaySeconds: 60
                    periodSeconds: 30
                    timeoutSeconds: 5
                    failureThreshold: 3
                  resources:
                    requests:
                      memory: "512Mi"
                      cpu: "500m"
                    limits:
                      memory: "1Gi"
                      cpu: "1000m"
          ---
          apiVersion: v1
          kind: Service
          metadata:
            name: {{inputs.parameters.rollout_name}}-active
            namespace: {{inputs.parameters.namespace}}
            labels:
              app: {{inputs.parameters.app_id}}
              service-type: active
          spec:
            ports:
            - port: {{inputs.parameters.service_port}}
              targetPort: {{inputs.parameters.container_port}}
              protocol: TCP
              name: http
            selector:
              app: {{inputs.parameters.app_id}}

    # 等待 Rollout 完成
    - name: wait-rollout-ready
      inputs:
        parameters:
          - name: rollout_name
          - name: namespace
          - name: timeout
      container:
        image: argoproj/kubectl-argo-rollouts:v1.7.1
        command: [sh, -c]
        args:
          - |
            echo "=========================================="
            echo "⏳ 等待 Rollout 部署完成"
            echo "=========================================="
            echo "Rollout名称: {{inputs.parameters.rollout_name}}"
            echo "命名空间: {{inputs.parameters.namespace}}"
            echo "超时时间: {{inputs.parameters.timeout}} 秒"
            echo "开始时间: $(date)"
            echo "=========================================="
            echo "📋 手动确认指令："
            echo "Blue-Green部署确认:"
            echo "  kubectl argo rollouts promote {{inputs.parameters.rollout_name}} -n {{inputs.parameters.namespace}}"
            echo "Canary部署确认:"
            echo "  kubectl argo rollouts promote {{inputs.parameters.rollout_name}} -n {{inputs.parameters.namespace}}"
            echo "中止部署:"
            echo "  kubectl argo rollouts abort {{inputs.parameters.rollout_name}} -n {{inputs.parameters.namespace}}"
            echo "回滚部署:"
            echo "  kubectl argo rollouts undo {{inputs.parameters.rollout_name}} -n {{inputs.parameters.namespace}}"
            echo "=========================================="
            
            # 等待 Rollout 就绪
            kubectl argo rollouts get rollout {{inputs.parameters.rollout_name}} \
              --namespace {{inputs.parameters.namespace}} \
              --watch \
              --timeout {{inputs.parameters.timeout}}s
            
            # 检查 Rollout 状态
            STATUS=$(kubectl argo rollouts status {{inputs.parameters.rollout_name}} \
              --namespace {{inputs.parameters.namespace}} \
              --timeout {{inputs.parameters.timeout}}s)
            
            echo "=========================================="
            echo "📊 部署状态: $STATUS"
            echo "完成时间: $(date)"
            echo "=========================================="
            
            if echo "$STATUS" | grep -q "Healthy"; then
              echo "✅ Rollout 部署成功"
              
              # 显示部署信息
              echo "=========================================="
              echo "📋 部署信息摘要"
              echo "=========================================="
              kubectl get rollout {{inputs.parameters.rollout_name}} -n {{inputs.parameters.namespace}} -o wide
              echo ""
              kubectl get pods -l app={{inputs.parameters.rollout_name}} -n {{inputs.parameters.namespace}}
              echo "=========================================="
            else
              echo "❌ Rollout 部署失败"
              kubectl describe rollout {{inputs.parameters.rollout_name}} -n {{inputs.parameters.namespace}}
              exit 1
            fi 