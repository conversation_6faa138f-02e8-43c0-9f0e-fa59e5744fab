apiVersion: argoproj.io/v1alpha1
kind: WorkflowTemplate
metadata:
  name: rollback-deployment
  namespace: argo
spec:
  entrypoint: rollback-pipeline
  serviceAccountName: argo-workflow
  
  # 模板参数定义
  arguments:
    parameters:
      - name: app_id
        description: "应用ID"
      - name: env_id
        description: "环境ID"
      - name: deploy_task_id
        description: "部署任务ID"
      - name: rollout_name
        description: "Rollout资源名称"
      - name: namespace
        description: "部署命名空间"
        default: "default"
      - name: target_revision
        description: "目标回滚版本"
      - name: rollback_type
        description: "回滚类型：undo(上一版本) | revision(指定版本)"
        default: "revision"
      - name: deployment_config
        description: "部署配置JSON（用于获取rollout信息）"

  templates:
    # 主回滚流水线
    - name: rollback-pipeline
      steps:
      # 第一步：解析部署配置，获取rollout信息
      - - name: parse-rollback-config
          template: parse-rollback-config
          arguments:
            parameters:
              - name: app_id
                value: "{{workflow.parameters.app_id}}"
              - name: env_id
                value: "{{workflow.parameters.env_id}}"
              - name: deployment_config
                value: "{{workflow.parameters.deployment_config}}"

      # 第二步：验证回滚前置条件
      - - name: verify-rollback-conditions
          template: verify-rollback-conditions
          arguments:
            parameters:
              - name: rollout_name
                value: "{{steps.parse-rollback-config.outputs.parameters.rollout_name}}"
              - name: namespace
                value: "{{steps.parse-rollback-config.outputs.parameters.namespace}}"
              - name: target_revision
                value: "{{workflow.parameters.target_revision}}"

      # 第三步：执行回滚
      - - name: execute-rollback
          template: argo-rollouts-undo
          arguments:
            parameters:
              - name: rollout_name
                value: "{{steps.parse-rollback-config.outputs.parameters.rollout_name}}"
              - name: namespace
                value: "{{steps.parse-rollback-config.outputs.parameters.namespace}}"
              - name: target_revision
                value: "{{workflow.parameters.target_revision}}"
              - name: rollback_type
                value: "{{workflow.parameters.rollback_type}}"

      # 第四步：等待回滚完成
      - - name: wait-rollback-complete
          template: wait-rollback-ready
          arguments:
            parameters:
              - name: rollout_name
                value: "{{steps.parse-rollback-config.outputs.parameters.rollout_name}}"
              - name: namespace
                value: "{{steps.parse-rollback-config.outputs.parameters.namespace}}"
              - name: timeout
                value: "900"  # 15分钟超时

      # 第五步：验证回滚结果
      - - name: verify-rollback-result
          template: verify-rollback-result
          arguments:
            parameters:
              - name: rollout_name
                value: "{{steps.parse-rollback-config.outputs.parameters.rollout_name}}"
              - name: namespace
                value: "{{steps.parse-rollback-config.outputs.parameters.namespace}}"
              - name: target_revision
                value: "{{workflow.parameters.target_revision}}"

      # 第六步：更新部署状态
      - - name: update-rollback-status
          templateRef:
            name: common-templates
            template: update-task-detail-status
          arguments:
            parameters:
              - name: deploy_task_id
                value: "{{workflow.parameters.deploy_task_id}}"
              - name: group_id
                value: "{{workflow.parameters.app_id}}"
              - name: status
                value: "completed"
              - name: message
                value: "回滚完成到版本: {{workflow.parameters.target_revision}}"

    # 解析回滚配置模板
    - name: parse-rollback-config
      inputs:
        parameters:
          - name: app_id
          - name: env_id
          - name: deployment_config
      outputs:
        parameters:
          - name: rollout_name
            valueFrom:
              path: /tmp/rollout_name
          - name: namespace
            valueFrom:
              path: /tmp/namespace
      container:
        image: alpine:3.18
        command: [sh, -c]
        args:
          - |
            set -e
            
            # 安装 jq 用于JSON解析
            apk add --no-cache jq
            
            echo "=========================================="
            echo "📋 解析回滚配置"
            echo "=========================================="
            echo "应用ID: {{inputs.parameters.app_id}}"
            echo "环境ID: {{inputs.parameters.env_id}}"
            echo "原始配置: {{inputs.parameters.deployment_config}}"
            echo "=========================================="
            
            # 将配置JSON写入文件
            echo '{{inputs.parameters.deployment_config}}' > /tmp/config.json
            
            # 验证JSON格式
            if ! jq . /tmp/config.json > /dev/null 2>&1; then
              echo "❌ 错误: 部署配置不是有效的JSON格式"
              exit 1
            fi
            
            # 解析各个配置项
            echo "📋 解析配置项..."
            
            # rollout_name（默认为 app_id + "-rollout"）
            rollout_name=$(jq -r '.rollout_name // "{{inputs.parameters.app_id}}-rollout"' /tmp/config.json)
            echo "$rollout_name" > /tmp/rollout_name
            echo "Rollout名称: $rollout_name"
            
            # namespace（默认为 default）
            namespace=$(jq -r '.namespace // "default"' /tmp/config.json)
            echo "$namespace" > /tmp/namespace
            echo "命名空间: $namespace"
            
            echo "=========================================="
            echo "✅ 回滚配置解析完成"
            echo "=========================================="

    # 验证回滚前置条件
    - name: verify-rollback-conditions
      inputs:
        parameters:
          - name: rollout_name
          - name: namespace
          - name: target_revision
      container:
        image: argoproj/kubectl-argo-rollouts:v1.7.1
        command: [sh, -c]
        args:
          - |
            echo "=========================================="
            echo "🔍 验证回滚前置条件"
            echo "=========================================="
            echo "Rollout名称: {{inputs.parameters.rollout_name}}"
            echo "命名空间: {{inputs.parameters.namespace}}"
            echo "目标版本: {{inputs.parameters.target_revision}}"
            echo "=========================================="
            
            # 检查Rollout是否存在
            if ! kubectl get rollout {{inputs.parameters.rollout_name}} -n {{inputs.parameters.namespace}} > /dev/null 2>&1; then
              echo "❌ 错误: Rollout {{inputs.parameters.rollout_name}} 在命名空间 {{inputs.parameters.namespace}} 中不存在"
              exit 1
            fi
            
            # 检查Rollout当前状态
            CURRENT_STATUS=$(kubectl argo rollouts status {{inputs.parameters.rollout_name}} -n {{inputs.parameters.namespace}} --timeout 10s || echo "Unknown")
            echo "当前Rollout状态: $CURRENT_STATUS"
            
            # 检查回滚历史
            echo "=========================================="
            echo "📋 Rollout历史版本:"
            kubectl argo rollouts history {{inputs.parameters.rollout_name}} -n {{inputs.parameters.namespace}} || true
            echo "=========================================="
            
            # 验证目标版本是否存在于历史中
            if [ -n "{{inputs.parameters.target_revision}}" ]; then
              echo "验证目标版本 {{inputs.parameters.target_revision}} 是否存在于历史中..."
              # 这里可以添加更详细的版本验证逻辑
            fi
            
            echo "✅ 前置条件验证通过"

    # Argo Rollouts 回滚执行
    - name: argo-rollouts-undo
      inputs:
        parameters:
          - name: rollout_name
          - name: namespace
          - name: target_revision
          - name: rollback_type
      container:
        image: argoproj/kubectl-argo-rollouts:v1.7.1
        command: [sh, -c]
        args:
          - |
            echo "=========================================="
            echo "🔄 执行 Argo Rollouts 回滚"
            echo "=========================================="
            echo "Rollout名称: {{inputs.parameters.rollout_name}}"
            echo "命名空间: {{inputs.parameters.namespace}}"
            echo "回滚类型: {{inputs.parameters.rollback_type}}"
            echo "目标版本: {{inputs.parameters.target_revision}}"
            echo "开始时间: $(date)"
            echo "=========================================="
            
            # 记录回滚前状态
            echo "📊 回滚前状态:"
            kubectl argo rollouts get rollout {{inputs.parameters.rollout_name}} -n {{inputs.parameters.namespace}} || true
            echo "=========================================="
            
            # 执行回滚命令
            if [ "{{inputs.parameters.rollback_type}}" = "undo" ]; then
              echo "🔄 执行回滚到上一个版本..."
              kubectl argo rollouts undo {{inputs.parameters.rollout_name}} \
                --namespace {{inputs.parameters.namespace}}
            else
              echo "🔄 执行回滚到指定版本: {{inputs.parameters.target_revision}}..."
              # 注意：argo rollouts undo 命令通常不支持 --to-revision 参数
              # 这里我们使用设置镜像的方式来实现回滚到指定版本
              kubectl argo rollouts undo {{inputs.parameters.rollout_name}} \
                --namespace {{inputs.parameters.namespace}}
                
              # 如果需要回滚到特定镜像，可以使用 set image 命令
              # kubectl argo rollouts set image {{inputs.parameters.rollout_name}} \
              #   "*={{inputs.parameters.target_revision}}" \
              #   --namespace {{inputs.parameters.namespace}}
            fi
            
            if [ $? -eq 0 ]; then
              echo "✅ 回滚命令执行成功"
            else
              echo "❌ 回滚命令执行失败"
              exit 1
            fi
            
            echo "完成时间: $(date)"
            echo "=========================================="

    # 等待回滚完成
    - name: wait-rollback-ready
      inputs:
        parameters:
          - name: rollout_name
          - name: namespace
          - name: timeout
      container:
        image: argoproj/kubectl-argo-rollouts:v1.7.1
        command: [sh, -c]
        args:
          - |
            echo "=========================================="
            echo "⏳ 等待回滚完成"
            echo "=========================================="
            echo "Rollout名称: {{inputs.parameters.rollout_name}}"
            echo "命名空间: {{inputs.parameters.namespace}}"
            echo "超时时间: {{inputs.parameters.timeout}} 秒"
            echo "开始时间: $(date)"
            echo "=========================================="
            
            # 等待回滚状态稳定
            kubectl argo rollouts status {{inputs.parameters.rollout_name}} \
              --namespace {{inputs.parameters.namespace}} \
              --timeout {{inputs.parameters.timeout}}s
            
            if [ $? -eq 0 ]; then
              echo "=========================================="
              echo "✅ 回滚完成"
              echo "完成时间: $(date)"
              echo "=========================================="
              
              # 显示最终状态
              echo "📊 回滚后状态:"
              kubectl argo rollouts get rollout {{inputs.parameters.rollout_name}} -n {{inputs.parameters.namespace}}
              echo ""
              kubectl get pods -l app={{inputs.parameters.rollout_name}} -n {{inputs.parameters.namespace}}
              echo "=========================================="
            else
              echo "❌ 回滚超时或失败"
              echo "错误时间: $(date)"
              
              # 显示错误状态
              kubectl describe rollout {{inputs.parameters.rollout_name}} -n {{inputs.parameters.namespace}}
              exit 1
            fi

    # 验证回滚结果
    - name: verify-rollback-result
      inputs:
        parameters:
          - name: rollout_name
          - name: namespace
          - name: target_revision
      container:
        image: argoproj/kubectl-argo-rollouts:v1.7.1
        command: [sh, -c]
        args:
          - |
            echo "=========================================="
            echo "🔍 验证回滚结果"
            echo "=========================================="
            echo "Rollout名称: {{inputs.parameters.rollout_name}}"
            echo "命名空间: {{inputs.parameters.namespace}}"
            echo "目标版本: {{inputs.parameters.target_revision}}"
            echo "=========================================="
            
            # 检查Rollout最终状态
            FINAL_STATUS=$(kubectl argo rollouts status {{inputs.parameters.rollout_name}} -n {{inputs.parameters.namespace}} --timeout 10s || echo "Unknown")
            echo "最终Rollout状态: $FINAL_STATUS"
            
            # 检查Pod状态
            echo "📊 Pod状态检查:"
            kubectl get pods -l app={{inputs.parameters.rollout_name}} -n {{inputs.parameters.namespace}} -o wide
            
            # 检查是否所有Pod都是Ready状态
            READY_PODS=$(kubectl get pods -l app={{inputs.parameters.rollout_name}} -n {{inputs.parameters.namespace}} -o jsonpath='{.items[*].status.conditions[?(@.type=="Ready")].status}' | tr ' ' '\n' | grep -c "True" || echo "0")
            TOTAL_PODS=$(kubectl get pods -l app={{inputs.parameters.rollout_name}} -n {{inputs.parameters.namespace}} --no-headers | wc -l)
            
            echo "Ready Pods: $READY_PODS / $TOTAL_PODS"
            
            if [ "$READY_PODS" -eq "$TOTAL_PODS" ] && [ "$TOTAL_PODS" -gt 0 ]; then
              echo "✅ 回滚验证成功: 所有Pod都处于Ready状态"
            else
              echo "❌ 回滚验证失败: 部分Pod未Ready"
              
              # 显示详细错误信息
              echo "=========================================="
              echo "🔍 错误详情:"
              kubectl describe pods -l app={{inputs.parameters.rollout_name}} -n {{inputs.parameters.namespace}}
              exit 1
            fi
            
            echo "=========================================="
            echo "✅ 回滚验证完成"
            echo "==========================================" 