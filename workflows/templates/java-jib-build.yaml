apiVersion: argoproj.io/v1alpha1
kind: WorkflowTemplate
metadata:
  name: java-jib-build
spec:
  entrypoint: java-build-pipeline
  serviceAccountName: argo-workflow
  # 模板参数定义
  arguments:
    parameters:
      - name: app_id
        description: "应用ID"
      - name: env_id
        description: "环境ID"
      - name: git_repo
        description: "Git 仓库地址"
      - name: git_branch
        description: "Git 分支或标签"
        default: "main"
      - name: commit_id
        description: "Git 提交ID"
      - name: git_username
        description: "Git 用户名（私有仓库需要）"
        default: ""
      - name: git_token
        description: "Git 访问令牌（私有仓库需要）"
        default: ""
      - name: deploy_task_id
        description: "部署任务ID"
      - name: image_registry
        description: "镜像仓库地址"
      - name: image_repo
        description: "镜像仓库名"
      - name: image_tag
        description: "镜像标签"
        default: "{{workflow.parameters.commit_id}}"
      - name: maven_opts
        description: "Maven 构建参数"
        default: "-DskipTests"
      - name: java_version
        description: "Java版本"
        default: "17"
      - name: harbor_registry
        description: "Harbor镜像仓库地址"
        default: "{{workflow.parameters.image_registry}}"
      - name: harbor_user
        description: "Harbor用户名"
      - name: harbor_password
        description: "Harbor密码"
      # 部署控制参数
      - name: enable_deployment
        description: "是否启用部署步骤"
        default: "false"
      - name: deployment_api_url
        description: "获取部署配置的API地址"
        default: "http://deploy-service.default.svc.cluster.local:8080/api/v1/deployment/config"

  # 定义工作流持久卷
  volumes:
    - name: maven-cache
      emptyDir: {}
    - name: gradle-cache
      emptyDir: {}
    - name: maven-settings
      configMap:
        name: maven-settings-xml

  # 工作流模板
  templates:
    # 主工作流定义
    - name: java-build-pipeline
      steps:
      - - name: build-and-push-image
          template: jib-skopeo-build
          arguments:
            parameters:
              - name: java_version
                value: "{{workflow.parameters.java_version}}"
              - name: maven_opts
                value: "{{workflow.parameters.maven_opts}}"
              - name: image_registry
                value: "{{workflow.parameters.image_registry}}"
              - name: image_repo
                value: "{{workflow.parameters.image_repo}}"
              - name: image_tag
                value: "{{workflow.parameters.image_tag}}"
              - name: git_repo
                value: "{{workflow.parameters.git_repo}}"
              - name: git_branch
                value: "{{workflow.parameters.git_branch}}"
              - name: commit_id
                value: "{{workflow.parameters.commit_id}}"
              - name: git_username
                value: "{{workflow.parameters.git_username}}"
              - name: git_token
                value: "{{workflow.parameters.git_token}}"
              - name: harbor_registry
                value: "{{workflow.parameters.harbor_registry}}"
              - name: harbor_user
                value: "{{workflow.parameters.harbor_user}}"
              - name: harbor_password
                value: "{{workflow.parameters.harbor_password}}"

      - - name: update-build-status
          templateRef:
            name: common-templates
            template: update-task-detail-status
          arguments:
            parameters:
              - name: deploy_task_id
                value: "{{workflow.parameters.deploy_task_id}}"
              - name: group_id
                value: "{{workflow.parameters.app_id}}"  # 使用app_id作为group_id
              - name: status
                value: "completed"
              - name: message
                value: "构建完成，镜像: {{workflow.parameters.harbor_registry}}/{{workflow.parameters.image_repo}}:{{workflow.parameters.image_tag}}"

      # 获取部署配置步骤
      - - name: get-deployment-config
          templateRef:
            name: common-templates
            template: fetch-deployment-config
          when: "{{workflow.parameters.enable_deployment}} == true"
          arguments:
            parameters:
              - name: app_id
                value: "{{workflow.parameters.app_id}}"
              - name: env_id
                value: "{{workflow.parameters.env_id}}"
              - name: deployment_api_url
                value: "{{workflow.parameters.deployment_api_url}}"

      # 部署步骤 - 使用动态获取的配置
      - - name: deploy-with-rollouts
          templateRef:
            name: argo-rollouts-deployment
            template: rollout-deployment-pipeline
          when: "{{workflow.parameters.enable_deployment}} == true"
          arguments:
            parameters:
              - name: app_id
                value: "{{workflow.parameters.app_id}}"
              - name: env_id
                value: "{{workflow.parameters.env_id}}"
              - name: deploy_task_id
                value: "{{workflow.parameters.deploy_task_id}}"
              - name: image_registry
                value: "{{workflow.parameters.harbor_registry}}"
              - name: image_repo
                value: "{{workflow.parameters.image_repo}}"
              - name: image_tag
                value: "{{workflow.parameters.image_tag}}"
              # 使用动态获取的部署配置
              - name: deployment_config
                value: "{{steps.get-deployment-config.outputs.result}}"

      - - name: update-final-status
          templateRef:
            name: common-templates
            template: update-task-detail-status
          when: "{{workflow.parameters.enable_deployment}} == true"
          arguments:
            parameters:
              - name: deploy_task_id
                value: "{{workflow.parameters.deploy_task_id}}"
              - name: group_id
                value: "{{workflow.parameters.app_id}}"
              - name: status
                value: "completed"
              - name: message
                value: "构建和部署完成，镜像: {{workflow.parameters.harbor_registry}}/{{workflow.parameters.image_repo}}:{{workflow.parameters.image_tag}}"

    # Jib+Skopeo构建模板 - 无需Docker
    - name: jib-skopeo-build
      inputs:
        parameters:
          - name: java_version
          - name: maven_opts
          - name: image_registry
          - name: image_repo
          - name: image_tag
          - name: git_repo
          - name: git_branch
          - name: commit_id
          - name: git_username
          - name: git_token
          - name: harbor_registry
          - name: harbor_user
          - name: harbor_password
      volumes:
        - name: maven-cache
          emptyDir: {}
        - name: gradle-cache
          emptyDir: {}
        - name: workspace
          emptyDir: {}
        - name: jib-cache
          emptyDir: {}
        - name: maven-settings
          configMap:
            name: maven-settings-xml
      containerSet:
        containers:
          # Git 检出容器
          - name: git-checkout
            image: alpine/git:2.49.0
            command: [sh, -c]
            args:
              - |
                # 设置Git凭据（如果提供）
                GIT_REPO="{{inputs.parameters.git_repo}}"
                if [ -n "{{inputs.parameters.git_username}}" ] && [ -n "{{inputs.parameters.git_token}}" ]; then
                  # 从原始URL中提取域名和路径
                  REPO_URL=$(echo $GIT_REPO | sed -E 's/https?:\/\///')
                  REPO_DOMAIN=$(echo $REPO_URL | cut -d'/' -f1)
                  REPO_PATH=$(echo $REPO_URL | cut -d'/' -f2-)
                  
                  # 重新构建带凭据的URL
                  GIT_REPO="https://{{inputs.parameters.git_username}}:{{inputs.parameters.git_token}}@$REPO_DOMAIN/$REPO_PATH"
                  echo "使用认证信息访问Git仓库"
                else
                  echo "使用无认证方式访问Git仓库"
                fi
                
                # 如果有 commit_id，则浅克隆后切换到该 commit
                if [ -n "{{inputs.parameters.commit_id}}" ]; then
                  git clone --depth 1 $GIT_REPO /workspace
                  cd /workspace
                  git fetch --depth 1 origin {{inputs.parameters.commit_id}}
                  git checkout {{inputs.parameters.commit_id}}
                else
                  # 浅克隆指定分支
                  git clone --depth 1 --single-branch --branch {{inputs.parameters.git_branch}} $GIT_REPO /workspace
                  cd /workspace
                fi
                
                echo "代码检出完成，当前提交ID: $(git rev-parse HEAD)"
                ls -la
                
                # 创建标记文件表示检出完成
                touch /workspace/checkout-completed
            volumeMounts:
              - name: workspace
                mountPath: /workspace

          # Maven/Gradle构建容器 - 使用Jib构建镜像到本地tar文件
          - name: jib-builder
            image: "maven:3.9.10-eclipse-temurin-{{inputs.parameters.java_version}}"
            dependencies: ["git-checkout"]
            command: [sh, -c]
            args:
              - |
                set -e  # 遇到错误立即退出
                
                # 等待代码检出完成
                echo "等待代码检出完成..."
                while [ ! -f /workspace/checkout-completed ]; do
                  sleep 2
                done
                echo "✓ 代码检出完成"
                
                cd /workspace
                
                # 显示环境信息
                echo "=== 环境信息 ==="
                echo "Java版本: $(java -version 2>&1 | head -1)"
                echo "Maven版本: $(mvn -version | head -1)"
                echo "可用内存: $(free -h | grep Mem)"
                echo "磁盘空间: $(df -h /workspace | tail -1)"
                
                # 优化Maven配置 - 兼容老版本Java
                export MAVEN_OPTS="-Xmx4g -XX:+UseG1GC -XX:+UseContainerSupport"
                
                # 创建必要的目录
                mkdir -p /jib-cache
                
                # 检测并构建项目
                echo "=== 开始构建 ==="
                if [ -f "pom.xml" ]; then
                  echo "检测到Maven项目"
                  
                  # 一次性执行清理、编译、打包和Jib构建
                  mvn -T1C -s /maven-settings/settings.xml \
                    clean compile package \
                    com.google.cloud.tools:jib-maven-plugin:3.4.0:buildTar \
                    -DskipTests \
                    -Djib.outputPaths.tar=/jib-cache/app-image.tar \
                    -Djib.to.image={{inputs.parameters.harbor_registry}}/{{inputs.parameters.image_repo}}:{{inputs.parameters.image_tag}} \
                    -Djib.from.image={{inputs.parameters.harbor_registry}}/library/eclipse-temurin:{{inputs.parameters.java_version}}-jre \
                    -Djib.from.auth.username={{inputs.parameters.harbor_user}} \
                    -Djib.from.auth.password={{inputs.parameters.harbor_password}} \
                    -Djib.container.mainClass=auto \
                    -Djib.container.jvmFlags="-XX:+UseContainerSupport,-XX:InitialRAMPercentage=50,-XX:MaxRAMPercentage=80,-XX:+UseG1GC" \
                    -Djib.container.environment=JAVA_TOOL_OPTIONS="-XX:+UseContainerSupport -XX:InitialRAMPercentage=50 -XX:MaxRAMPercentage=80 -XX:+UseG1GC" \
                    -Djib.containerizingMode=packaged \
                    -Djib.allowInsecureRegistries=true \
                    -Djib.dockerClient.executable= \
                    -Djib.dockerClient.environment=false \
                    {{inputs.parameters.maven_opts}} \
                    --batch-mode --no-transfer-progress
                    
                elif [ -f "build.gradle" ] || [ -f "build.gradle.kts" ]; then
                  echo "检测到Gradle项目"
                  
                  # 确保gradle wrapper可执行
                  [ -f "gradlew" ] && chmod +x gradlew
                  
                  # 使用适当的Gradle命令
                  GRADLE_CMD="gradle"
                  [ -f "gradlew" ] && GRADLE_CMD="./gradlew"
                  
                  $GRADLE_CMD build jibBuildTar \
                    -x test \
                    -Djib.outputPaths.tar=/jib-cache/app-image.tar \
                    -Djib.to.image={{inputs.parameters.harbor_registry}}/{{inputs.parameters.image_repo}}:{{inputs.parameters.image_tag}} \
                    -Djib.from.image={{inputs.parameters.harbor_registry}}/library/eclipse-temurin:{{inputs.parameters.java_version}}-jre \
                    -Djib.from.auth.username={{inputs.parameters.harbor_user}} \
                    -Djib.from.auth.password={{inputs.parameters.harbor_password}} \
                    -Djib.container.jvmFlags="-XX:+UseContainerSupport,-XX:InitialRAMPercentage=50,-XX:MaxRAMPercentage=80,-XX:+UseG1GC" \
                    -Djib.container.environment=JAVA_TOOL_OPTIONS="-XX:+UseContainerSupport -XX:InitialRAMPercentage=50 -XX:MaxRAMPercentage=80 -XX:+UseG1GC" \
                    -Djib.dockerClient.executable= \
                    -Djib.dockerClient.environment=false \
                    --info --no-daemon
                else
                  echo "❌ 错误: 未检测到Maven(pom.xml)或Gradle(build.gradle)构建文件"
                  exit 1
                fi
                
                # 验证构建结果
                if [ ! -f "/jib-cache/app-image.tar" ]; then
                  echo "❌ 错误: 镜像文件未生成"
                  exit 1
                fi
                
                echo "✓ Jib构建完成，镜像大小: $(du -h /jib-cache/app-image.tar | cut -f1)"
                touch /workspace/build-completed
            volumeMounts:
              - name: maven-cache
                mountPath: /root/.m2
              - name: gradle-cache
                mountPath: /root/.gradle
              - name: workspace
                mountPath: /workspace
              - name: jib-cache
                mountPath: /jib-cache
              - name: maven-settings
                mountPath: /maven-settings

          # Skopeo容器 - 从本地tar文件推送到Harbor
          - name: skopeo-push
            image: quay.io/skopeo/stable:latest
            dependencies: ["jib-builder"]
            command: [sh, -c]
            args:
              - |
                # 等待Jib构建完成
                while [ ! -f /workspace/build-completed ]; do
                  echo "等待Jib构建完成..."
                  sleep 2
                done
                
                echo "使用Skopeo推送镜像到Harbor..."
                
                # 登录Harbor仓库
                skopeo login --username {{inputs.parameters.harbor_user}} --password {{inputs.parameters.harbor_password}} {{inputs.parameters.harbor_registry}}
                
                # 从tar文件复制镜像到Harbor
                skopeo copy docker-archive:/jib-cache/app-image.tar docker://{{inputs.parameters.harbor_registry}}/{{inputs.parameters.image_repo}}:{{inputs.parameters.image_tag}}
                
                echo "镜像已成功推送到Harbor: {{inputs.parameters.harbor_registry}}/{{inputs.parameters.image_repo}}:{{inputs.parameters.image_tag}}"
                
                # 创建标记文件表示推送完成
                touch /workspace/push-completed
            volumeMounts:
              - name: jib-cache
                mountPath: /jib-cache
              - name: workspace
                mountPath: /workspace 

