apiVersion: argoproj.io/v1alpha1
kind: WorkflowTemplate
metadata:
  name: common-templates
  namespace: argo
spec:
  templates:
  # Git检出模板
  - name: git-checkout
    inputs:
      parameters:
      - name: git_repo
      - name: commit_id
      - name: git_branch
        default: "main"
    outputs:
      artifacts:
      - name: source-code
        path: /workspace
    container:
      image: alpine/git:latest
      command: [sh, -c]
      args:
      - |
        git clone {{inputs.parameters.git_repo}} /workspace
        cd /workspace
        if [ -n "{{inputs.parameters.commit_id}}" ]; then
          git checkout {{inputs.parameters.commit_id}}
        else
          git checkout {{inputs.parameters.git_branch}}
        fi
        echo "代码检出完成: $(git rev-parse HEAD)"
        ls -la

  # 更新任务状态模板 (HTTP版本)
  - name: update-task-detail-status
    inputs:
      parameters:
      - name: deploy_task_id
      - name: group_id
      - name: status
      - name: message
    http:
      url: http://deploy-gateway-service:8080/api/v1/deploy-tasks/{{inputs.parameters.deploy_task_id}}/detail-status
      method: PUT
      headers:
        - name: Content-Type
          value: application/json
      body: >-
        {
          "group_id": {{inputs.parameters.group_id}},
          "status": {{=intVal(inputs.parameters.status)}},
          "message": "{{inputs.parameters.message}}"
        }

  # 将状态字符串转换为整数的辅助函数
  - name: intVal
    inputs:
      parameters:
      - name: status
    script:
      image: alpine:3.16
      command: [sh, -c]
      source: |
        case "{{inputs.parameters.status}}" in
          "running")
            echo "1"
            ;;
          "completed")
            echo "2"
            ;;
          "failed")
            echo "3"
            ;;
          *)
            echo "2"  # 默认为完成状态
            ;;
        esac 

  # 获取部署配置模板 (HTTP版本)
  - name: fetch-deployment-config
    inputs:
      parameters:
        - name: app_id
        - name: env_id
        - name: deployment_api_url
    http:
      url: "{{inputs.parameters.deployment_api_url}}?app_id={{inputs.parameters.app_id}}&env_id={{inputs.parameters.env_id}}"
      method: GET
      headers:
        - name: Content-Type
          value: application/json
        - name: Accept
          value: application/json
      successCondition: "response.statusCode >= 200 && response.statusCode < 300"