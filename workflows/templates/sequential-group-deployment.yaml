apiVersion: argoproj.io/v1alpha1
kind: WorkflowTemplate
metadata:
  name: sequential-group-deployment
  namespace: argo
spec:
  workflowMetadata:
    labels:
      app-id: "{{workflow.parameters.app_id}}"
      deploy-task-id: "{{workflow.parameters.deploy_task_id}}"
      env-id: "{{workflow.parameters.env_id}}"
  entrypoint: main
  arguments:
    parameters:
    - name: app_id
      description: "应用ID"
    - name: env_id
      description: "环境ID"
    - name: deploy_task_id
      description: "部署任务ID"
    - name: groups_order
      description: "JSON格式的有序组ID列表，按照执行顺序排列"
    - name: commit_id
      description: "Git提交ID"
    - name: git_repo
      description: "Git仓库地址"
    - name: image_registry
      description: "镜像仓库地址"
    - name: image_tag
      description: "镜像标签，默认使用Git提交ID"
      default: "{{workflow.parameters.commit_id}}"

  templates:
  - name: main
    dag:
      tasks:
      - name: generate-deployment-plan
        template: generate-deployment-plan
        arguments:
          parameters:
          - name: groups_order
            value: "{{workflow.parameters.groups_order}}"
          - name: deploy_task_id
            value: "{{workflow.parameters.deploy_task_id}}"

      # 部署第一个组（关键组）
      - name: deploy-first-group
        template: deploy-group
        dependencies:
        - generate-deployment-plan
        arguments:
          parameters:
          - name: group_id
            value: "{{tasks.generate-deployment-plan.outputs.parameters.first_group_id}}"
          - name: deploy_task_id
            value: "{{workflow.parameters.deploy_task_id}}"
          - name: app_id
            value: "{{workflow.parameters.app_id}}"
          - name: env_id
            value: "{{workflow.parameters.env_id}}"
          - name: commit_id
            value: "{{workflow.parameters.commit_id}}"
          - name: git_repo
            value: "{{workflow.parameters.git_repo}}"
          - name: image_registry
            value: "{{workflow.parameters.image_registry}}"
          - name: image_tag
            value: "{{workflow.parameters.image_tag}}"

      # 部署其余组（按顺序）
      - name: deploy-remaining-groups
        template: deploy-remaining-groups
        dependencies:
        - deploy-first-group  # 关键依赖：必须在第一个组成功后执行
        arguments:
          parameters:
          - name: remaining_groups
            value: "{{tasks.generate-deployment-plan.outputs.parameters.remaining_groups}}"
          - name: deploy_task_id
            value: "{{workflow.parameters.deploy_task_id}}"
          - name: app_id
            value: "{{workflow.parameters.app_id}}"
          - name: env_id
            value: "{{workflow.parameters.env_id}}"
          - name: commit_id
            value: "{{workflow.parameters.commit_id}}"
          - name: git_repo
            value: "{{workflow.parameters.git_repo}}"
          - name: image_registry
            value: "{{workflow.parameters.image_registry}}"
          - name: image_tag
            value: "{{workflow.parameters.image_tag}}"

  # 生成部署计划
  - name: generate-deployment-plan
    inputs:
      parameters:
      - name: groups_order
      - name: deploy_task_id
    script:
      image: python:3.9-alpine
      command: [python]
      source: |
        import json
        import sys
        
        # 解析有序组列表
        groups = json.loads("{{inputs.parameters.groups_order}}")
        
        if not groups:
            print("错误：没有指定部署组", file=sys.stderr)
            exit(1)
            
        # 第一个组ID（关键组）
        first_group = str(groups[0])
        
        # 其余组ID（按顺序）
        remaining_groups = json.dumps(groups[1:]) if len(groups) > 1 else "[]"
        
        # 输出参数
        with open("/tmp/first_group_id", "w") as f:
            f.write(first_group)
            
        with open("/tmp/remaining_groups", "w") as f:
            f.write(remaining_groups)
    outputs:
      parameters:
      - name: first_group_id
        valueFrom:
          path: /tmp/first_group_id
      - name: remaining_groups
        valueFrom:
          path: /tmp/remaining_groups

  # 部署单个组
  - name: deploy-group
    inputs:
      parameters:
      - name: group_id
      - name: deploy_task_id
      - name: app_id
      - name: env_id
      - name: commit_id
      - name: git_repo
      - name: image_registry
      - name: image_tag
    dag:
      tasks:
      - name: build-and-deploy
        template: build-by-type
        arguments:
          parameters:
          - name: group_id
            value: "{{inputs.parameters.group_id}}"
          - name: deploy_task_id
            value: "{{inputs.parameters.deploy_task_id}}"
          - name: app_id
            value: "{{inputs.parameters.app_id}}"
          - name: env_id
            value: "{{inputs.parameters.env_id}}"
          - name: commit_id
            value: "{{inputs.parameters.commit_id}}"
          - name: git_repo
            value: "{{inputs.parameters.git_repo}}"
          - name: image_registry
            value: "{{inputs.parameters.image_registry}}"
          - name: image_tag
            value: "{{inputs.parameters.image_tag}}"
            
      - name: update-status
        template: update-task-detail-status
        dependencies:
        - build-and-deploy
        arguments:
          parameters:
          - name: group_id
            value: "{{inputs.parameters.group_id}}"
          - name: deploy_task_id
            value: "{{inputs.parameters.deploy_task_id}}"
          - name: status
            value: "completed"
          - name: message
            value: "部署完成"

  # 按顺序部署剩余组
  - name: deploy-remaining-groups
    inputs:
      parameters:
      - name: remaining_groups
      - name: deploy_task_id
      - name: app_id
      - name: env_id
      - name: commit_id
      - name: git_repo
      - name: image_registry
      - name: image_tag
    steps:
      - - name: check-remaining-groups
          template: check-remaining-groups
          arguments:
            parameters:
            - name: remaining_groups
              value: "{{inputs.parameters.remaining_groups}}"
      
      # 如果有剩余组，按顺序执行
      - - name: process-groups-sequentially
          template: process-groups-sequentially
          when: "{{steps.check-remaining-groups.outputs.parameters.has_remaining_groups}} == true"
          arguments:
            parameters:
            - name: remaining_groups
              value: "{{inputs.parameters.remaining_groups}}"
            - name: deploy_task_id
              value: "{{inputs.parameters.deploy_task_id}}"
            - name: app_id
              value: "{{inputs.parameters.app_id}}"
            - name: env_id
              value: "{{inputs.parameters.env_id}}"
            - name: commit_id
              value: "{{inputs.parameters.commit_id}}"
            - name: git_repo
              value: "{{inputs.parameters.git_repo}}"
            - name: image_registry
              value: "{{inputs.parameters.image_registry}}"
            - name: image_tag
              value: "{{inputs.parameters.image_tag}}"

  # 检查是否有剩余组需要部署
  - name: check-remaining-groups
    inputs:
      parameters:
      - name: remaining_groups
    script:
      image: python:3.9-alpine
      command: [python]
      source: |
        import json
        
        remaining_groups = json.loads("{{inputs.parameters.remaining_groups}}")
        has_remaining = len(remaining_groups) > 0
        
        with open("/tmp/has_remaining", "w") as f:
            f.write(str(has_remaining).lower())
    outputs:
      parameters:
      - name: has_remaining_groups
        valueFrom:
          path: /tmp/has_remaining

  # 顺序处理剩余组
  - name: process-groups-sequentially
    inputs:
      parameters:
      - name: remaining_groups
      - name: deploy_task_id
      - name: app_id
      - name: env_id
      - name: commit_id
      - name: git_repo
      - name: image_registry
      - name: image_tag
    steps:
      # 遍历每个组并按顺序执行
      - - name: deploy-groups-in-order
          template: deploy-group
          withParam: "{{inputs.parameters.remaining_groups}}"
          arguments:
            parameters:
            - name: group_id
              value: "{{item}}"
            - name: deploy_task_id
              value: "{{inputs.parameters.deploy_task_id}}"
            - name: app_id
              value: "{{inputs.parameters.app_id}}"
            - name: env_id
              value: "{{inputs.parameters.env_id}}"
            - name: commit_id
              value: "{{inputs.parameters.commit_id}}"
            - name: git_repo
              value: "{{inputs.parameters.git_repo}}"
            - name: image_registry
              value: "{{inputs.parameters.image_registry}}"
            - name: image_tag
              value: "{{inputs.parameters.image_tag}}"

  # 根据类型选择构建模板
  - name: build-by-type
    inputs:
      parameters:
      - name: group_id
      - name: deploy_task_id
      - name: app_id
      - name: env_id
      - name: commit_id
      - name: git_repo
      - name: image_registry
      - name: image_tag
    steps:
    - - name: execute-build
        template: execute-template
        arguments:
          parameters:
          - name: template_name
            value: "java-jib-build"
          - name: group_id
            value: "{{inputs.parameters.group_id}}"
          - name: deploy_task_id
            value: "{{inputs.parameters.deploy_task_id}}"
          - name: app_id
            value: "{{inputs.parameters.app_id}}"
          - name: env_id
            value: "{{inputs.parameters.env_id}}"
          - name: commit_id
            value: "{{inputs.parameters.commit_id}}"
          - name: git_repo
            value: "{{inputs.parameters.git_repo}}"
          - name: image_registry
            value: "{{inputs.parameters.image_registry}}"
          - name: image_tag
            value: "{{inputs.parameters.image_tag}}"
          - name: java_version
            value: "17"

  # 执行另一个工作流模板
  - name: execute-template
    inputs:
      parameters:
      - name: template_name
      - name: group_id
      - name: deploy_task_id
      - name: app_id
      - name: env_id
      - name: commit_id
      - name: git_repo
      - name: image_registry
      - name: image_tag
        default: "latest"
      - name: java_version
        default: "17"
    resource:
      action: create
      manifest: |
        apiVersion: argoproj.io/v1alpha1
        kind: Workflow
        metadata:
          generateName: {{inputs.parameters.template_name}}-{{inputs.parameters.group_id}}-
          namespace: argo
        spec:
          workflowTemplateRef:
            name: {{inputs.parameters.template_name}}
          arguments:
            parameters:
            - name: app_id
              value: "{{inputs.parameters.app_id}}"
            - name: env_id
              value: "{{inputs.parameters.env_id}}"
            - name: deploy_task_id
              value: "{{inputs.parameters.deploy_task_id}}"
            - name: group_id
              value: "{{inputs.parameters.group_id}}"
            - name: commit_id
              value: "{{inputs.parameters.commit_id}}"
            - name: git_repo
              value: "{{inputs.parameters.git_repo}}"
            - name: image_registry
              value: "{{inputs.parameters.image_registry}}"
            - name: image_tag
              value: "{{inputs.parameters.image_tag}}"
            - name: java_version
              value: "{{inputs.parameters.java_version}}"

  # 更新任务详情状态
  - name: update-task-detail-status
    inputs:
      parameters:
      - name: deploy_task_id
      - name: group_id
      - name: status
      - name: message
    templateRef:
      name: common-templates
      template: update-task-detail-status
    arguments:
      parameters:
      - name: deploy_task_id
        value: "{{inputs.parameters.deploy_task_id}}"
      - name: group_id
        value: "{{inputs.parameters.group_id}}"
      - name: status
        value: "{{inputs.parameters.status}}"
      - name: message
        value: "{{inputs.parameters.message}}" 