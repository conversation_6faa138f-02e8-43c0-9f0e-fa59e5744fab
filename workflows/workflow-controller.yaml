# This file describes the config settings available in the workflow controller configmap
apiVersion: v1
kind: ConfigMap
metadata:
  name: workflow-controller-configmap
  namespace: argo
data:
  # instanceID是标签选择器，用于限制控制器监视特定实例
  instanceID: deploy-gateway

  artifactRepository: |
    s3:
      endpoint: 192.168.100.2:9000
      bucket: my-bucket
      insecure: false
      accessKeySecret:
        name: admin
        key: password123
      secretKeySecret:
        name: admin
        key: password123

  # 命名空间是标签选择器过滤器，用于限制控制器监视特定命名空间
  namespace: argo

  # 并行度限制可以同时执行的最大工作流总数
  parallelism: "10"

  # 限制命名空间中未完成工作流的最大数量
  namespaceParallelism: "10"

  # 全局限制创建pod的速率
  resourceRateLimit: |
    limit: 10
    burst: 25

  # 是否在节点完成时发出事件
  nodeEvents: |
    enabled: true

  # 是否在工作流状态更改时发出事件
  workflowEvents: |
    enabled: true

  # 工作流链接配置
  links: |
    - name: 工作流日志
      scope: workflow
      url: http://logging-facility?namespace=${metadata.namespace}&workflowName=${metadata.name}&startedAt=${status.startedAt}&finishedAt=${status.finishedAt}
    - name: Pod日志
      scope: pod-logs
      url: http://logging-facility?namespace=${metadata.namespace}&podName=${metadata.name}&startedAt=${status.startedAt}&finishedAt=${status.finishedAt}
    - name: 获取帮助
      scope: chat
      url: http://help.yunshanmeicai.com

  # 主容器的默认设置
  mainContainer: |
    imagePullPolicy: IfNotPresent
    resources:
      requests:
        cpu: 0.1
        memory: 64Mi
      limits:
        cpu: 0.5
        memory: 512Mi
    securityContext:
      allowPrivilegeEscalation: false
      capabilities:
        drop:
        - ALL
      readOnlyRootFilesystem: true
      runAsNonRoot: true
      runAsUser: 1000

  # 执行器控制初始化容器和等待容器的自定义方式
  executor: |
    imagePullPolicy: IfNotPresent
    resources:
      requests:
        cpu: 0.1
        memory: 64Mi
      limits:
        cpu: 0.5
        memory: 512Mi

  # 指标配置控制Prometheus指标的路径和端口
  metricsConfig: |
    enabled: true
    path: /metrics
    port: 8080
    metricsTTL: "10m"
    ignoreErrors: false
    secure: true

  # 使用MySQL进行持久化
  persistence: |
    connectionPool:
      maxIdleConns: 10
      maxOpenConns: 100
      connMaxLifetime: "3600s"
    nodeStatusOffLoad: true
    archive: false
    skipMigration: false
    mysql:
      host: **********
      port: 3306
      database: deploy
      tableName: t_argo_workflows
      userNameSecret:
        name: argo-mysql-secrets
        key: username
      passwordSecret:
        name: argo-mysql-secrets
        key: password

  # Pod GC宽限期
  podGCGracePeriodSeconds: "60"

  # Pod GC删除延迟时长
  podGCDeleteDelayDuration: "30s"

  # 工作流保留策略
  retentionPolicy: |
    completed: 100
    failed: 50
    errored: 50

  # 将应用于此控制器中所有工作流的默认值
  workflowDefaults: |
    metadata:
      annotations:
        argo: workflows
      labels:
        app: deploy-gateway
    spec:
      ttlStrategy:
        secondsAfterSuccess: 86400
        secondsAfterCompletion: 86400
        secondsAfterFailure: 172800
      parallelism: 10

  # 工作流限制
  workflowRestrictions: |
    templateReferencing: Strict
