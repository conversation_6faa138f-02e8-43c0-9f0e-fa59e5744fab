# Argo Workflows 模板集

本目录包含用于在Kubernetes上运行CI/CD流程的Argo Workflows工作流模板。这些模板与部署网关(Deploy Gateway)集成，实现自动化构建和部署流程。

## 目录结构

- `templates/`: 包含各种语言和构建工具的工作流模板
  - `java-jib-build.yaml`: Java应用使用Jib构建并优化AppCDS的工作流模板
  - `go-ko-build.yaml`: Go应用使用ko构建生成静态Distroless镜像的工作流模板
  - `py-php-buildkit-build.yaml`: Python/PHP应用使用BuildKit多阶段构建的工作流模板
  - `sequential-group-deployment.yaml`: 支持多组顺序部署的工作流模板，确保部署组按优先级顺序执行
- `workflow-controller.yaml`: Argo Workflows控制器配置

## 先决条件

在使用这些工作流模板前，您需要确保：

1. Kubernetes集群已安装Argo Workflows（v3.5.0+）
2. 创建了必要的持久卷声明：
   - `maven-cache-pvc`: 用于Maven缓存
   - `gradle-cache-pvc`: 用于Gradle缓存
   - `go-cache-pvc`: 用于Go构建缓存
   - `buildkit-cache-pvc`: 用于BuildKit缓存
3. 配置了访问Git仓库和镜像仓库的凭证

## 安装

### 1. 安装Argo Workflows（如果尚未安装）

```bash
kubectl create namespace argo
kubectl apply -n argo -f https://github.com/argoproj/argo-workflows/releases/download/v3.6.10/install.yaml
```

### 2. 应用控制器配置

```bash
kubectl apply -f workflows/workflow-controller.yaml
```

### 3. 安装工作流模板

```bash
kubectl apply -f workflows/templates/java-jib-build.yaml -n argo
kubectl apply -f workflows/templates/go-ko-build.yaml -n argo
kubectl apply -f workflows/templates/py-php-buildkit-build.yaml -n argo
kubectl apply -f workflows/templates/sequential-group-deployment.yaml -n argo
```

## 使用方法

### 多组顺序部署

创建一个工作流，使用顺序部署组模板：

```yaml
apiVersion: argoproj.io/v1alpha1
kind: Workflow
metadata:
  generateName: deploy-task-
spec:
  workflowTemplateRef:
    name: sequential-group-deployment
  arguments:
    parameters:
      - name: app_id
        value: "1001"
      - name: env_id
        value: "2001"
      - name: git_repo
        value: "https://github.com/your-org/your-app.git"
      - name: commit_id
        value: "a1b2c3d4e5f6"
      - name: deploy_task_id
        value: "5001"
      - name: groups_order
        value: "[101, 102, 103]"  # 按优先级排序的组ID
      - name: image_registry
        value: "registry.example.com"
```

### Java应用构建

创建一个工作流，使用Java Jib构建模板：

```yaml
apiVersion: argoproj.io/v1alpha1
kind: Workflow
metadata:
  generateName: java-build-
spec:
  workflowTemplateRef:
    name: java-jib-build
  arguments:
    parameters:
      - name: app_id
        value: "1001"
      - name: env_id
        value: "2001"
      - name: git_repo
        value: "https://github.com/your-org/your-java-app.git"
      - name: commit_id
        value: "a1b2c3d4e5f6"
      - name: deploy_task_id
        value: "5001"
      - name: image_registry
        value: "registry.example.com"
      - name: image_repo
        value: "my-java-app"
      - name: java_version
        value: "17"
```

### Go应用构建

创建一个工作流，使用Go ko构建模板：

```yaml
apiVersion: argoproj.io/v1alpha1
kind: Workflow
metadata:
  generateName: go-build-
spec:
  workflowTemplateRef:
    name: go-ko-build
  arguments:
    parameters:
      - name: app_id
        value: "1002"
      - name: env_id
        value: "2002"
      - name: git_repo
        value: "https://github.com/your-org/your-go-app.git"
      - name: commit_id
        value: "b2c3d4e5f6g7"
      - name: deploy_task_id
        value: "5002"
      - name: image_registry
        value: "registry.example.com"
      - name: image_repo
        value: "my-go-app"
      - name: go_version
        value: "1.21"
```

### Python/PHP应用构建

创建一个工作流，使用BuildKit构建模板：

```yaml
apiVersion: argoproj.io/v1alpha1
kind: Workflow
metadata:
  generateName: python-build-
spec:
  workflowTemplateRef:
    name: py-php-buildkit-build
  arguments:
    parameters:
      - name: app_id
        value: "1003"
      - name: env_id
        value: "2003"
      - name: git_repo
        value: "https://github.com/your-org/your-python-app.git"
      - name: commit_id
        value: "c3d4e5f6g7h8"
      - name: deploy_task_id
        value: "5003"
      - name: image_registry
        value: "registry.example.com"
      - name: image_repo
        value: "my-python-app"
      - name: language_type
        value: "python"
      - name: python_version
        value: "3.11"
```

## 与部署网关集成

这些工作流模板设计用于与部署网关系统集成。当在部署网关中创建部署任务后，系统会自动创建相应的Argo工作流来执行构建和部署过程。

### 集成流程

1. 用户在部署平台创建部署任务
2. 部署网关根据应用类型选择适当的工作流模板
3. 对于包含多个部署组的任务，使用sequential-group-deployment模板按优先级顺序执行
4. 每个部署组根据其类型自动选择合适的构建模板（Java/Go/Python等）
5. 工作流完成后，通过API回调更新部署任务状态

### 部署组顺序执行策略

sequential-group-deployment模板实现了以下部署策略：

1. 部署组按优先级顺序执行（根据t_runtime_setting表中的priority字段）
2. 第一个组（关键组）必须成功完成，才会执行后续组
3. 如果关键组失败，整个工作流将失败，后续组不会执行
4. 支持动态获取组的类型信息并选择适当的构建模板

## 持久化和缓存

工作流使用持久卷进行缓存，提高构建速度：

- Maven/Gradle缓存: 加速Java依赖下载
- Go模块缓存: 加速Go依赖下载
- BuildKit缓存: 加速Docker层构建

## 监控与管理

您可以通过以下方式监控工作流：

1. Argo Workflows UI: http://argo-workflows.your-domain/workflows
2. Argo CLI: `argo list -n argo`
3. Prometheus指标: 可通过工作流控制器的metrics端点获取

## 故障排除

- 如果工作流未启动，检查工作流模板是否正确安装
- 如果构建失败，检查日志以获取详细信息: `argo logs -n argo <workflow-name>`
- 对于持久卷问题，检查PVC状态: `kubectl get pvc -n argo` 