//go:build wireinject
// +build wireinject

package wire

import (
	"ci-gateway/internal/handler"
	"ci-gateway/internal/repository"
	"ci-gateway/internal/server"
	"ci-gateway/internal/service"
	cmdbService "ci-gateway/internal/service/cmdb"
	"ci-gateway/internal/service/gitlab"
	pkghttp "ci-gateway/pkg/http"

	cmdb "ci-gateway/internal/repository/cmdb"

	"github.com/google/wire"
	"github.com/spf13/viper"
	"go.uber.org/zap"
)

var ServerSet = wire.NewSet(server.NewServerHTTP)

var RepositorySet = wire.NewSet(
	repository.NewRepository,
	repository.NewDatabaseManager,
	repository.NewUserRepository,

	repository.NewImageRepository,

	repository.NewAppSettingsRepository,

	repository.NewConfigFileRepository,
	repository.NewDeployTaskRepository,
	repository.NewGroupRepository,
	repository.NewEnvironmentVariableRepository,
	repository.NewBuildTemplateRepository,
	repository.NewBuildConfigRepository,
	repository.NewProbeRepository,
	repository.NewContainerRepository,
)

var CmdbRepositorySet = wire.NewSet(
	cmdb.NewApplicationRepository,
	cmdb.NewSrvTreeRepository,
)

var ServiceSet = wire.NewSet(
	service.NewService,
	service.NewUserService,

	service.NewImageService,

	service.NewAppSettingsService,

	service.NewConfigFileService,
	service.NewDeployTaskService,
	service.NewGroupService,
	service.NewEnvironmentVariableService,
	service.NewWorkflowServiceFromConfig,
	service.NewProbeService,
	service.NewBuildConfigService,
	service.NewPodService,
	service.NewContainerService,
	service.NewConfigSnapshotService,
)

var GitLabServiceSet = wire.NewSet(
	gitlab.NewGitLabService,
)

var CmdbServiceSet = wire.NewSet(
	cmdbService.NewSrvTreeService,
	cmdbService.NewApplicationService,
)

var HandlerSet = wire.NewSet(
	handler.NewHandler,
	handler.NewHealthHandler,
	handler.NewSrvTreeHandler,
	handler.NewImageHandler,
	handler.NewAppHandler,
	handler.NewConfigHandler,
	handler.NewDeployTaskHandler,
	handler.NewGroupHandler,
	handler.NewEnvironmentVariableHandler,
	handler.NewProbeHandler,
	handler.NewGitLabHandler,
	handler.NewBuildConfigHandler,
	handler.NewPodHandler,
	handler.NewContainerHandler,
)

type AppComponents struct {
	Server          *pkghttp.Server
	WorkflowService service.WorkflowService
}

func NewWire(*viper.Viper, *zap.Logger) (*AppComponents, func(), error) {
	panic(wire.Build(
		ServerSet,
		RepositorySet,
		CmdbRepositorySet,
		ServiceSet,
		CmdbServiceSet,
		GitLabServiceSet,
		HandlerSet,
		wire.Struct(new(AppComponents), "*"),
	))
}
