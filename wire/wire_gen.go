// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wire

import (
	"ci-gateway/internal/handler"
	"ci-gateway/internal/repository"
	cmdb2 "ci-gateway/internal/repository/cmdb"
	"ci-gateway/internal/server"
	"ci-gateway/internal/service"
	"ci-gateway/internal/service/cmdb"
	"ci-gateway/internal/service/gitlab"
	"ci-gateway/pkg/http"
	"github.com/google/wire"
	"github.com/spf13/viper"
	"go.uber.org/zap"
)

// Injectors from wire.go:

func NewWire(viperViper *viper.Viper, logger *zap.Logger) (*AppComponents, func(), error) {
	handlerHandler := handler.NewHandler(logger, viperViper)
	healthHandler := handler.NewHealthHandler(handlerHandler)
	srvTreeService := cmdb.NewSrvTreeService(viperViper, logger)
	databaseManager, err := repository.NewDatabaseManager(viperViper, logger)
	if err != nil {
		return nil, nil, err
	}
	repositoryRepository := repository.NewRepository(logger, databaseManager)
	applicationRepository := cmdb2.NewApplicationRepository(repositoryRepository)
	applicationService := cmdb.NewApplicationService(applicationRepository)
	srvTreeHandler := handler.NewSrvTreeHandler(srvTreeService, applicationService, logger)
	serviceService := service.NewService(logger, viperViper)
	imageRepository := repository.NewImageRepository(repositoryRepository)
	imageService := service.NewImageService(serviceService, imageRepository)
	imageHandler := handler.NewImageHandler(handlerHandler, imageService)
	appSettingsRepository := repository.NewAppSettingsRepository(repositoryRepository)
	appSettingsService := service.NewAppSettingsService(serviceService, appSettingsRepository)
	groupRepository := repository.NewGroupRepository(repositoryRepository)
	deployTaskRepository := repository.NewDeployTaskRepository(repositoryRepository)
	groupService := service.NewGroupService(serviceService, groupRepository, deployTaskRepository, logger)
	environmentVariableRepository := repository.NewEnvironmentVariableRepository(repositoryRepository)
	environmentVariableService := service.NewEnvironmentVariableService(serviceService, environmentVariableRepository)
	configFileRepository := repository.NewConfigFileRepository(repositoryRepository)
	configFileService := service.NewConfigFileService(serviceService, configFileRepository)
	appHandler := handler.NewAppHandler(applicationService, appSettingsService, groupService, environmentVariableService, configFileService, handlerHandler)
	configHandler := handler.NewConfigHandler(handlerHandler, configFileService)
	workflowService, err := service.NewWorkflowServiceFromConfig(serviceService, deployTaskRepository, appSettingsService, viperViper)
	if err != nil {
		return nil, nil, err
	}
	buildTemplateRepository := repository.NewBuildTemplateRepository(repositoryRepository)
	buildConfigRepository := repository.NewBuildConfigRepository(repositoryRepository)
	buildConfigService := service.NewBuildConfigService(serviceService, buildTemplateRepository, buildConfigRepository)
	probeRepository := repository.NewProbeRepository(repositoryRepository)
	probeService := service.NewProbeService(probeRepository)
	containerRepository := repository.NewContainerRepository(repositoryRepository)
	containerService := service.NewContainerService(serviceService, containerRepository)
	configSnapshotService := service.NewConfigSnapshotService(buildConfigService, probeService, appSettingsService, groupService, containerService, applicationService)
	deployTaskService := service.NewDeployTaskService(serviceService, deployTaskRepository, workflowService, configSnapshotService)
	deployTaskHandler := handler.NewDeployTaskHandler(handlerHandler, deployTaskService)
	groupHandler := handler.NewGroupHandler(groupService, logger)
	environmentVariableHandler := handler.NewEnvironmentVariableHandler(environmentVariableService, handlerHandler)
	probeHandler := handler.NewProbeHandler(handlerHandler, probeService)
	gitLabService := gitlab.NewGitLabService(serviceService)
	gitLabHandler := handler.NewGitLabHandler(handlerHandler, gitLabService)
	buildConfigHandler := handler.NewBuildConfigHandler(handlerHandler, buildConfigService)
	podService := service.NewPodService(serviceService)
	podHandler := handler.NewPodHandler(handlerHandler, podService)
	containerHandler := handler.NewContainerHandler(handlerHandler, containerService)
	userRepository := repository.NewUserRepository(repositoryRepository)
	httpServer := server.NewServerHTTP(logger, viperViper, healthHandler, srvTreeHandler, imageHandler, appHandler, configHandler, deployTaskHandler, groupHandler, environmentVariableHandler, probeHandler, gitLabHandler, buildConfigHandler, podHandler, containerHandler, userRepository)
	appComponents := &AppComponents{
		Server:          httpServer,
		WorkflowService: workflowService,
	}
	return appComponents, func() {
	}, nil
}

// wire.go:

var ServerSet = wire.NewSet(server.NewServerHTTP)

var RepositorySet = wire.NewSet(repository.NewRepository, repository.NewDatabaseManager, repository.NewUserRepository, repository.NewImageRepository, repository.NewAppSettingsRepository, repository.NewConfigFileRepository, repository.NewDeployTaskRepository, repository.NewGroupRepository, repository.NewEnvironmentVariableRepository, repository.NewBuildTemplateRepository, repository.NewBuildConfigRepository, repository.NewProbeRepository, repository.NewContainerRepository)

var CmdbRepositorySet = wire.NewSet(cmdb2.NewApplicationRepository, cmdb2.NewSrvTreeRepository)

var ServiceSet = wire.NewSet(service.NewService, service.NewUserService, service.NewImageService, service.NewAppSettingsService, service.NewConfigFileService, service.NewDeployTaskService, service.NewGroupService, service.NewEnvironmentVariableService, service.NewWorkflowServiceFromConfig, service.NewProbeService, service.NewBuildConfigService, service.NewPodService, service.NewContainerService, service.NewConfigSnapshotService)

var GitLabServiceSet = wire.NewSet(gitlab.NewGitLabService)

var CmdbServiceSet = wire.NewSet(cmdb.NewSrvTreeService, cmdb.NewApplicationService)

var HandlerSet = wire.NewSet(handler.NewHandler, handler.NewHealthHandler, handler.NewSrvTreeHandler, handler.NewImageHandler, handler.NewAppHandler, handler.NewConfigHandler, handler.NewDeployTaskHandler, handler.NewGroupHandler, handler.NewEnvironmentVariableHandler, handler.NewProbeHandler, handler.NewGitLabHandler, handler.NewBuildConfigHandler, handler.NewPodHandler, handler.NewContainerHandler)

type AppComponents struct {
	Server          *http.Server
	WorkflowService service.WorkflowService
}
