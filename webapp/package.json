{"private": true, "author": "maqiang01 <<EMAIL>>", "scripts": {"build": "max build", "dev": "max dev", "dev:80": "PORT=81 max dev", "format": "prettier --cache --write .", "postinstall": "max setup", "prepare": "husky", "setup": "max setup", "start": "npm run dev:80"}, "dependencies": {"@ant-design/icons": "^5.0.1", "@ant-design/pro-components": "^2.4.4", "@monaco-editor/react": "^4.4.6", "@types/js-yaml": "^4.0.9", "@types/react-beautiful-dnd": "^13.1.8", "@types/xmldom": "^0.1.34", "@umijs/max": "^4.4.11", "@xmldom/xmldom": "^0.9.8", "@xterm/addon-fit": "^0.10.0", "@xterm/addon-web-links": "^0.11.0", "@xterm/xterm": "^5.5.0", "antd": "^5.4.0", "js-yaml": "^4.1.0", "jsonlint-mod": "^1.7.6", "monaco-editor": "^0.34.1", "react-beautiful-dnd": "^13.1.1"}, "devDependencies": {"@types/react": "^18.0.33", "@types/react-dom": "^18.0.11", "husky": "^9", "lint-staged": "^13.2.0", "prettier": "^2.8.7", "prettier-plugin-organize-imports": "^3.2.2", "prettier-plugin-packagejson": "^2.4.3", "typescript": "^5.0.3"}}