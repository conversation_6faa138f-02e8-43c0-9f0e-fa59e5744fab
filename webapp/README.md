# README

`@umijs/max` 模板项目，更多功能参考 [Umi Max 简介](https://umijs.org/docs/max/introduce)

## 后端（Go）

- 使用 [GORM](https://gorm.io/) 作为 ORM 框架
- 主要目录说明：
  - `internal/handler`：接口处理
  - `internal/service`：业务逻辑
  - `internal/repository`：数据访问
  - `internal/model`：数据模型
  - `internal/middleware`：中间件
  - `pkg/config`：配置与日志
- 依赖管理：Go Modules (`go.mod`)
- 启动方式：

```bash
cd ci
go run main.go
```

- 配置文件：`config/local.yml`

## 前端（React + Umi Max）

- 基于 [`@umijs/max`](https://umijs.org/docs/max/introduce) 模板
- 主要目录说明：
  - `src/pages`：页面
  - `src/components`：组件
  - `src/services`：接口服务
  - `src/models`：数据模型
  - `src/utils`：工具函数
- 启动方式：

```bash
cd webapp
npm install
npm start
```

或

```bash
yarn install
yarn start
```

## 开发与部署

1. 启动后端服务（Go）
2. 启动前端服务（webapp）
3. 访问前端页面进行操作

## 其他

- 前端更多功能参考 [Umi Max 简介](https://umijs.org/docs/max/introduce)
- 如需自定义配置，请参考各自目录下的 README 或配置文件

如有问题欢迎提 issue 或联系开发者。

1.检查 Node.js 版本

```bash
node -v
```
如果版本低于 14，建议升级到 16 或 18 及以上。
2. 升级 Node.js
推荐用 nvm 管理 Node 版本：

```bash
# 安装 nvm（如果没装过）
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.7/install.sh | bash
# 重新加载 shell
source ~/.zshrc

# 安装 Node.js 最新 LTS 版本
nvm install --lts

# 使用最新 LTS 版本
nvm use --lts

# 再次确认版本
node -v
```
3. 删除 node_modules 和 lock 文件，重新安装依赖
```bash
cd /Users/<USER>/data/goCode/ci/webapp
rm -rf node_modules package-lock.json yarn.lock
npm install
```
4. 再次执行
```bash
npm install
```


