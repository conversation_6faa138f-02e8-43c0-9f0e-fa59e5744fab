import { defineConfig } from '@umijs/max';

export default defineConfig({
  antd: {},
  access: {},
  model: {},
  initialState: {},
  request: {},
  layout: false,
  history: {
    type: 'browser',
  },
  publicPath: '/',
  hash: false,
  outputPath: 'dist',
  esbuildMinifyIIFE: true,
  proxy: {
    '/api': {
      target: 'http://localhost:8001',
      changeOrigin: true,
      pathRewrite: { '^': '' },
    },
  },
  routes: [
    {
      path: '/',
      redirect: '/deploy',
    },
    {
      name: '应用部署',
      path: '/deploy',
      component: './Deploy',
      icon: 'deploymentUnit',
    },
    {
      name: '镜像与组件',
      path: '/image-component',
      component: './ImageComponent',
      icon: 'appstore',
    },
    {
      name: '应用管理',
      path: '/app-management',
      component: './AppManagement',
      icon: 'setting',
    },
  ],
  npmClient: 'yarn',
});
