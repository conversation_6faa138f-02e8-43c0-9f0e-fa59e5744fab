/**
 * 本地存储工具类
 * 提供localStorage和sessionStorage的操作方法
 */

// localStorage操作
export const localStore = {
  /**
   * 保存数据到localStorage
   * @param key 键名
   * @param value 值
   */
  set: (key: string, value: any): void => {
    if (typeof value === 'object') {
      localStorage.setItem(key, JSON.stringify(value));
    } else {
      localStorage.setItem(key, value);
    }
  },

  /**
   * 从localStorage获取数据
   * @param key 键名
   * @param defaultValue 默认值（如果没有找到数据）
   */
  get: <T>(key: string, defaultValue?: T): T | null => {
    const value = localStorage.getItem(key);
    if (value === null) {
      return defaultValue === undefined ? null : defaultValue;
    }
    
    try {
      return JSON.parse(value) as T;
    } catch (e) {
      return value as unknown as T;
    }
  },

  /**
   * 从localStorage删除数据
   * @param key 键名
   */
  remove: (key: string): void => {
    localStorage.removeItem(key);
  },

  /**
   * 清空localStorage
   */
  clear: (): void => {
    localStorage.clear();
  }
};

// sessionStorage操作
export const sessionStore = {
  /**
   * 保存数据到sessionStorage
   * @param key 键名
   * @param value 值
   */
  set: (key: string, value: any): void => {
    if (typeof value === 'object') {
      sessionStorage.setItem(key, JSON.stringify(value));
    } else {
      sessionStorage.setItem(key, value);
    }
  },

  /**
   * 从sessionStorage获取数据
   * @param key 键名
   * @param defaultValue 默认值（如果没有找到数据）
   */
  get: <T>(key: string, defaultValue?: T): T | null => {
    const value = sessionStorage.getItem(key);
    if (value === null) {
      return defaultValue === undefined ? null : defaultValue;
    }
    
    try {
      return JSON.parse(value) as T;
    } catch (e) {
      return value as unknown as T;
    }
  },

  /**
   * 从sessionStorage删除数据
   * @param key 键名
   */
  remove: (key: string): void => {
    sessionStorage.removeItem(key);
  },

  /**
   * 清空sessionStorage
   */
  clear: (): void => {
    sessionStorage.clear();
  }
};

// 存储键名常量
export const STORAGE_KEYS = {
  DEPLOY_STATE: 'deploy_state',
  USER_SETTINGS: 'user_settings',
  THEME: 'theme',
  LANGUAGE: 'language'
}; 