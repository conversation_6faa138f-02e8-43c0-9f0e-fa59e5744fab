import yaml from 'js-yaml';
import jsonlint from 'jsonlint-mod';
import { ValidationResult, ConfigFileFormat } from '../types/config';

export const validateConfigSyntax = (
  content: string,
  format: ConfigFileFormat,
): ValidationResult => {
  try {
    switch (format) {
      case 'yaml':
        yaml.load(content);
        return { valid: true, error: null };

      case 'json':
        try {
          jsonlint.parse(content);
          return { valid: true, error: null };
        } catch (jsonError) {
          // 尝试从错误消息中提取行号
          const errorMsg = String(jsonError);
          const lineMatch = errorMsg.match(/line\s+(\d+)/i);
          const line = lineMatch ? parseInt(lineMatch[1], 10) : undefined;

          return {
            valid: false,
            error: errorMsg,
            line: line,
            column: 1,
          };
        }

      case 'xml':
        try {
          const parser = new DOMParser();
          parser.parseFromString(content, 'application/xml');
          return { valid: true, error: null };
        } catch (xmlError) {
          // XML错误通常包含行号信息
          const errorMsg =
            xmlError instanceof Error ? xmlError.message : String(xmlError);
          const lineMatch = errorMsg.match(/line\s+(\d+)/i);
          const line = lineMatch ? parseInt(lineMatch[1], 10) : undefined;

          return {
            valid: false,
            error: `XML 语法错误: ${errorMsg}`,
            line: line,
            column: 1,
          };
        }

      case 'properties':
        // 简单验证properties文件格式 (key=value 或 注释行)
        const lines = content.split('\n');
        for (let i = 0; i < lines.length; i++) {
          const line = lines[i].trim();
          // 跳过空行和注释行
          if (line === '' || line.startsWith('#') || line.startsWith('!')) {
            continue;
          }
          // 检查是否符合 key=value 或 key:value 格式
          if (!line.includes('=') && !line.includes(':')) {
            return {
              valid: false,
              error: `第 ${
                i + 1
              } 行不符合 key=value 或 key:value 格式: "${line}"`,
              line: i + 1,
              column: 1,
            };
          }
        }
        return { valid: true, error: null };

      case 'shell':
        // 对Shell脚本不做具体语法检查，只检查是否有明显错误如未闭合的引号
        let inSingleQuote = false;
        let inDoubleQuote = false;

        for (let i = 0; i < content.length; i++) {
          const char = content[i];
          const prevChar = i > 0 ? content[i - 1] : '';

          if (char === "'" && prevChar !== '\\' && !inDoubleQuote) {
            inSingleQuote = !inSingleQuote;
          } else if (char === '"' && prevChar !== '\\' && !inSingleQuote) {
            inDoubleQuote = !inDoubleQuote;
          }
        }

        if (inSingleQuote || inDoubleQuote) {
          return {
            valid: false,
            error: `脚本中存在未闭合的引号`,
          };
        }
        return { valid: true, error: null };

      default:
        // 纯文本格式不做语法检查
        return { valid: true, error: null };
    }
  } catch (e) {
    return {
      valid: false,
      error: `${format} 语法错误: ${
        e instanceof Error ? e.message : String(e)
      }`,
    };
  }
};

export const getDefaultContentByFormat = (format: ConfigFileFormat): string => {
  switch (format) {
    case 'properties':
      return '# 应用配置文件\n# 格式: key=value\n\napp.name=my-application\napp.version=1.0.0\nserver.port=8080\n';
    case 'yaml':
      return '# 应用配置文件\n# YAML 格式\n\napp:\n  name: my-application\n  version: 1.0.0\nserver:\n  port: 8080\n';
    case 'json':
      return '{\n  "app": {\n    "name": "my-application",\n    "version": "1.0.0"\n  },\n  "server": {\n    "port": 8080\n  }\n}';
    case 'xml':
      return '<?xml version="1.0" encoding="UTF-8"?>\n<configuration>\n  <app>\n    <name>my-application</name>\n    <version>1.0.0</version>\n  </app>\n  <server>\n    <port>8080</port>\n  </server>\n</configuration>';
    case 'shell':
      return '#!/bin/bash\n# 配置脚本\n\nexport APP_NAME="my-application"\nexport APP_VERSION="1.0.0"\nexport SERVER_PORT="8080"\n\necho "配置已加载"\n';
    case 'text':
    default:
      return '# 请输入配置文件内容\n';
  }
};

export const getMonacoLanguage = (format: ConfigFileFormat): string => {
  switch (format) {
    case 'properties':
      return 'ini';
    case 'yaml':
      return 'yaml';
    case 'json':
      return 'json';
    case 'xml':
      return 'xml';
    case 'shell':
      return 'shell';
    default:
      return 'plaintext';
  }
}; 