import dayjs from 'dayjs';
import { DeployHistoryView, DeployLogView } from '@/types/deploytask';

/**
 * 将API DeployTask转换为前端DeployHistoryView
 * @param task API.DeployTask对象
 * @param groupsMap 分组映射，用于查找分组名称
 * @returns DeployHistoryView视图模型
 */
export function convertToDeployHistoryView(task: API.DeployTask, groupsMap?: Record<string, API.Group>): DeployHistoryView {
  // 查找任务对应的分组信息
  const groupNames: Record<string, string> = {};
  const targetGroups: string[] = [];
  
  if (task.task_details && task.task_details.length > 0) {
    task.task_details.forEach(detail => {
      const groupId = detail.group_id.toString();
      targetGroups.push(groupId);
      
      // 查找分组名称
      if (groupsMap && groupsMap[groupId]) {
        groupNames[groupId] = groupsMap[groupId].name;
      }
    });
  }
  
  // 计算持续时间
  let duration = '未完成';
  if (task.task_details && task.task_details.length > 0) {
    const detail = task.task_details[0];
    if (detail.end_time && detail.start_time) {
      const durationSeconds = detail.end_time - detail.start_time;
      if (durationSeconds < 60) {
        duration = `${durationSeconds}秒`;
      } else if (durationSeconds < 3600) {
        duration = `${Math.floor(durationSeconds / 60)}分钟`;
      } else {
        duration = `${Math.floor(durationSeconds / 3600)}小时${Math.floor((durationSeconds % 3600) / 60)}分钟`;
      }
    }
  }
  
  // 转换部署类型和策略
  let deployType: '部署' | '编译部署' | '回滚部署' = '部署';
  if (task.task_details && task.task_details.length > 0) {
    const detail = task.task_details[0];
    if (detail.deploy_type === 'rollback') {
      deployType = '回滚部署';
    } else if (detail.deploy_type === 'build_deploy') {
      deployType = '编译部署';
    }
  }
  
  let deployStrategy: '普通' | '蓝绿' | '金丝雀' = '普通';
  if (task.task_details && task.task_details.length > 0) {
    const detail = task.task_details[0];
    if (detail.deploy_strategy === 'blue-green') {
      deployStrategy = '蓝绿';
    } else if (detail.deploy_strategy === 'canary') {
      deployStrategy = '金丝雀';
    }
  }
  
  // 找到主要的分组名称（如果有多个，取第一个）
  const mainGroupName = Object.values(groupNames)[0] || '未知分组';

  // 转换日志
  const logs: DeployLogView[] = [];
  if (task.task_details) {
    task.task_details.forEach(detail => {
      if (detail.logs) {
        detail.logs.forEach(log => {
          logs.push({
            type: log.log_type as 'info' | 'warning' | 'error' | 'success',
            content: log.content,
            timestamp: dayjs(log.timestamp * 1000).format('YYYY-MM-DD HH:mm:ss')
          });
        });
      }
    });
  }
  
  return {
    id: task.id.toString(),
    semver: task.semver || '',
    deployTime: dayjs(task.c_t * 1000).format('YYYY-MM-DD HH:mm:ss'),
    deployType,
    deployStrategy,
    operator: String(task.created_by), // 暂时只有ID，后续可能需要转换为用户名
    status: task.status,
    duration,
    description: task.description,
    commitId: task.commit_id || '-',
    targetGroups,
    groupName: mainGroupName,
    config: {
      runtime: {}
    },
    logs
  };
}

/**
 * 将API DeployLog转换为前端DeployLogView
 * @param log API.DeployLog对象
 * @returns DeployLogView视图模型
 */
export function convertToDeployLogView(log: API.DeployLog): DeployLogView {
  return {
    type: log.log_type as 'info' | 'warning' | 'error' | 'success',
    content: log.content,
    timestamp: dayjs(log.timestamp * 1000).format('YYYY-MM-DD HH:mm:ss')
  };
} 