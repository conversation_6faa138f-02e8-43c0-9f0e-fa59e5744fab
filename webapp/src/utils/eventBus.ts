/**
 * 简单的事件总线实现
 * 用于跨组件通信，特别是非父子组件之间的事件传递
 */
type EventCallback = (...args: any[]) => void;

interface EventBus {
  events: Map<string, EventCallback[]>;
  subscribe: (event: string, callback: EventCallback) => void;
  unsubscribe: (event: string, callback: EventCallback) => void;
  publish: (event: string, ...args: any[]) => void;
}

const eventBus: EventBus = {
  events: new Map<string, EventCallback[]>(),
  
  // 订阅事件
  subscribe(event: string, callback: EventCallback) {
    if (!this.events.has(event)) {
      this.events.set(event, []);
    }
    this.events.get(event)!.push(callback);
  },
  
  // 取消订阅
  unsubscribe(event: string, callback: EventCallback) {
    if (!this.events.has(event)) return;
    
    const callbacks = this.events.get(event)!;
    const index = callbacks.indexOf(callback);
    
    if (index !== -1) {
      callbacks.splice(index, 1);
    }
    
    // 如果没有回调函数了，删除该事件
    if (callbacks.length === 0) {
      this.events.delete(event);
    }
  },
  
  // 发布事件
  publish(event: string, ...args: any[]) {
    if (!this.events.has(event)) return;
    
    this.events.get(event)!.forEach(callback => {
      try {
        callback(...args);
      } catch (err) {
        console.error(`事件处理错误 [${event}]:`, err);
      }
    });
  }
};

// 常用事件名称常量
export const EVENTS = {
  CONFIG_FILE_CREATED: 'configFileCreated',
  CONFIG_FILE_UPDATED: 'configFileUpdated',
  CONFIG_FILE_DELETED: 'configFileDeleted',
  APP_DATA_UPDATED: 'appDataUpdated',
  APP_DATA_NEEDED: 'appDataNeeded',
  DEPLOY_TASK_CREATED: 'deployTaskCreated',
  // 分组相关事件
  GROUP_CREATED: 'groupCreated',
  GROUP_UPDATED: 'groupUpdated',
  GROUP_DELETED: 'groupDeleted',
  GROUP_SELECTED: 'groupSelected',
  DEFAULT_GROUP_SET: 'defaultGroupSet',
};

export default eventBus; 