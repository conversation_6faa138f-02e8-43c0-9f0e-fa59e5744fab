// 示例方法，没有实际意义
export function trim(str: string) {
  return str.trim();
}

/**
 * 格式化长tag，用于优化显示
 */

// tag格式化配置
interface TagFormatConfig {
  maxLength?: number;
  showPrefix?: boolean;
  showVersion?: boolean;
  showDate?: boolean;
}

/**
 * 解析各种格式的tag字符串
 */
export function parseTag(tag: string) {
  // 处理undefined或null
  if (!tag) {
    return {
      type: 'unknown' as const,
      original: '',
    };
  }

  const patterns = [
    // 模式1: 完整模式 rel_test-web_1.2.5.4_20250620
    /^(rel|dev)_([\w-]+)_([\d.]+)_(\d+)$/,
    // 模式2: 无前缀模式 test-web_1.2.5.4_20250620
    /^([\w-]+)_([\d.]+)_(\d+)$/,
    // 模式3: v2.5.0-dev (版本号格式)
    /^v?(\d+\.\d+\.\d+)(?:-(.+))?$/,
    // 模式4: main, develop 等分支名
    /^(main|master|develop|dev|release|hotfix)$/i,
  ];

  // 尝试匹配第一个模式
  const match1 = tag.match(patterns[0]);
  if (match1) {
    return {
      type: 'full' as const,
      prefix: match1[1],
      project: match1[2],
      version: match1[3],
      date: match1[4],
      original: tag,
    };
  }

  // 尝试匹配第二个模式
  const match2 = tag.match(patterns[1]);
  if (match2) {
    return {
      type: 'no-prefix' as const,
      project: match2[1],
      version: match2[2],
      date: match2[3],
      original: tag,
    };
  }

  // 尝试匹配第三个模式
  const match3 = tag.match(patterns[2]);
  if (match3) {
    return {
      type: 'semver' as const,
      version: match3[1],
      suffix: match3[2] || undefined,
      original: tag,
    };
  }

  // 尝试匹配第四个模式
  const match4 = tag.match(patterns[3]);
  if (match4) {
    return {
      type: 'branch' as const,
      branch: match4[1],
      original: tag,
    };
  }

  // 如果都不匹配，返回原始tag
  return {
    type: 'unknown' as const,
    original: tag,
  };
}

/**
 * 格式化日期字符串
 * 20250620 -> "06-20"
 */
export function formatDate(dateStr: string): string {
  if (dateStr.length === 8) {
    const month = dateStr.substring(4, 6);
    const day = dateStr.substring(6, 8);
    return `${month}-${day}`;
  }
  return dateStr;
}

/**
 * 缩短项目名称
 * wms-main -> "wms"
 */
export function shortenProject(project: string): string {
  const parts = project.split('-');
  return parts[0];
}

/**
 * 格式化长tag为简短显示
 */
export function formatTag(tag: string, config: TagFormatConfig = {}): string {
  const {
    maxLength = 12,
    showPrefix = false,
    showVersion = true,
    showDate = true,
  } = config;

  const parsed = parseTag(tag);

  switch (parsed.type) {
    case 'full':
      const parts = [];
      if (showPrefix && parsed.prefix) {
        parts.push(parsed.prefix);
      }
      if (parsed.project) {
        parts.push(shortenProject(parsed.project));
      }
      if (showVersion && parsed.version) {
        // 只显示版本号的最后两位
        const versionParts = parsed.version.split('.');
        if (versionParts.length >= 3) {
          parts.push(`${versionParts[2]}.${versionParts[3] || '0'}`);
        }
      }
      if (showDate && parsed.date) {
        parts.push(formatDate(parsed.date));
      }
      const result = parts.join('_');
      return result.length > maxLength
        ? result.substring(0, maxLength) + '...'
        : result;

    case 'no-prefix':
      const parts2 = [];
      parts2.push(shortenProject(parsed.project));
      if (showVersion && parsed.version) {
        const versionParts = parsed.version.split('.');
        if (versionParts.length >= 3) {
          parts2.push(`${versionParts[2]}.${versionParts[3] || '0'}`);
        }
      }
      if (showDate && parsed.date) {
        parts2.push(formatDate(parsed.date));
      }
      const result2 = parts2.join('_');
      return result2.length > maxLength
        ? result2.substring(0, maxLength) + '...'
        : result2;

    case 'semver':
      if (parsed.suffix) {
        return `v${parsed.version}-${parsed.suffix}`;
      }
      return `v${parsed.version}`;

    case 'branch':
      return parsed.branch;

    default:
      // 如果tag太长，截断并添加省略号
      return tag.length > maxLength ? tag.substring(0, maxLength) + '...' : tag;
  }
}

/**
 * 获取完整的tag显示信息，用于tooltip
 */
export function getTagDisplayInfo(tag: string) {
  const parsed = parseTag(tag);

  switch (parsed.type) {
    case 'full':
      return {
        short: formatTag(tag),
        full: tag,
        details: {
          前缀: parsed.prefix,
          项目: parsed.project,
          版本: parsed.version,
          日期: formatDateForDisplay(parsed.date),
        },
      };

    case 'no-prefix':
      return {
        short: formatTag(tag),
        full: tag,
        details: {
          项目: parsed.project,
          版本: parsed.version,
          日期: formatDateForDisplay(parsed.date),
        },
      };

    case 'semver':
      return {
        short: formatTag(tag),
        full: tag,
        details: {
          版本: parsed.version,
          ...(parsed.suffix && { 类型: parsed.suffix }),
        },
      };

    case 'branch':
      return {
        short: tag,
        full: tag,
        details: {
          分支: parsed.branch,
        },
      };

    default:
      return {
        short: formatTag(tag),
        full: tag,
        details: {},
      };
  }
}

/**
 * 格式化日期用于详细显示
 * 20250620 -> "2025年06月20日"
 */
function formatDateForDisplay(dateStr: string): string {
  if (dateStr.length === 8) {
    const year = dateStr.substring(0, 4);
    const month = dateStr.substring(4, 6);
    const day = dateStr.substring(6, 8);
    return `${year}年${month}月${day}日`;
  }
  return dateStr;
}
