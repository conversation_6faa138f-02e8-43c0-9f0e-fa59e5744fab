import React, { createContext, useContext } from 'react';
import { FormInstance } from 'antd';

interface FormContextType {
  form: FormInstance | null;
}

// 创建一个表单上下文
export const FormContext = createContext<FormContextType>({ form: null });

// 自定义Hook用于访问FormContext
export const useFormContext = () => useContext(FormContext);

// 表单上下文提供器组件
export const FormProvider: React.FC<{ form: FormInstance; children: React.ReactNode }> = ({ 
  form, 
  children 
}) => {
  return (
    <FormContext.Provider value={{ form }}>
      {children}
    </FormContext.Provider>
  );
}; 