import {
  Card,
  Form,
  Layout,
  message,
  Modal,
  Space
} from 'antd';
import React, {  useState } from 'react';
import styles from './index.less';

import useAppDataLoader from '@/hooks/useAppDataLoader';
import useProbe from '@/hooks/useProbe';
import { useModel } from '@umijs/max';
import {
  EnvVariablesTooltip,
  GroupList,
  ProbeEditor,
  TopSelectorArea
} from './components';
import { BuildConfigEditor } from './components/BuildConfig';
import { ContainerConfigEditor } from './components/ContainerConfig';
import ConfigArea from './components/ConfigArea';
import { DeployHistory } from './components/DeployHistory';
import DeployOperation from './components/DeployOperation';
import { DeployStatus as DeployStatusComponent } from './components/DeployStatus';
import { PodRealTimeInfo } from './components/PodRealTimeInfo';
// 声明全局窗口对象类型
declare global {
  interface Window {
    monaco: any;
  }
}

const { Content, Sider } = Layout;

const DeployPage: React.FC = () => {
  const [form] = Form.useForm();
  const [podInfoVisible, setPodInfoVisible] = useState<boolean>(true);
  
  // 配置文件管理相关状态
  const [editMode, setEditMode] = useState<boolean>(false);
  const { deploy} = useModel('deploy');
  const { loading: appDataLoading } = useAppDataLoader(deploy.nodeId, deploy.envId);
  const { getProbeConfig, saveProbes } = useProbe();

  const [healthCheckForm] = Form.useForm();

  const [newGroupForm] = Form.useForm();

  // 添加健康检查编辑器相关状态
  const [probeEditorVisible, setProbeEditorVisible] = useState<boolean>(false);

  // 添加构建配置编辑器状态
  const [buildConfigEditorVisible, setBuildConfigEditorVisible] = useState<boolean>(false);
  
  // 添加容器配置编辑器状态
  const [containerConfigEditorVisible, setContainerConfigEditorVisible] = useState<boolean>(false);
  
  // 添加环境变量编辑器状态
  const [envVariablesEditorVisible, setEnvVariablesEditorVisible] = useState<boolean>(false);

  // 监听环境变化，更新表单中的环境字段
  React.useEffect(() => {
    newGroupForm.setFieldsValue({
      environment: deploy.envName,
    });
  }, [deploy.envName, newGroupForm]);


  // 处理打开探针编辑器
  const handleOpenProbeEditor = () => {
    setProbeEditorVisible(true);
  };

  // 处理探针配置保存
  const handleProbeSave = () => {
    setProbeEditorVisible(false);
  };

  // 打开构建配置编辑器
  const handleOpenBuildConfigEditor = () => {
    setBuildConfigEditorVisible(true);
  };

  // 打开容器配置编辑器
  const handleOpenContainerConfigEditor = () => {
    setContainerConfigEditorVisible(true);
  };

  // 打开分组配置编辑器（占位方法）
  const handleOpenGroupConfigEditor = () => {
    // TODO: 实现分组配置编辑器
    message.info('分组配置编辑功能即将上线');
  };

  // 切换编辑模式
  const toggleEditMode = () => {
    if (editMode) {
      form.validateFields().then((values) => {
        // 在实际应用中这里应该会保存到后端
        setEditMode(false);
      });
    } else {
      setEditMode(true);
    }
  };

  const renderContent = () => {
    if (!deploy.groupId) {
      return (
        <Card>
          <div
            style={{ textAlign: 'center', padding: '40px 0', color: '#8c8c8c' }}
          >
            请先选择一个可用的应用及分组
          </div>
        </Card>
      );
    }

    return (
      <Space direction="vertical" size={16} style={{ width: '100%' }}>
        {/* 服务分组相关信息 - 使用ConfigArea组件 */}
        <ConfigArea 
          editMode={editMode}
          form={form}
          toggleEditMode={toggleEditMode}
          healthCheckForm={healthCheckForm}
        />

        {/* 部署状态组件 */}
        <DeployStatusComponent />

        {/* Pod实时信息区域 - 使用独立组件 */}
        <PodRealTimeInfo
          podInfoVisible={podInfoVisible}
          onToggleVisible={() => setPodInfoVisible(!podInfoVisible)}
        />

        {/* 部署历史区域 - 使用独立组件 */}
        <DeployHistory/>
      </Space>
    );
  };

  return (
    <div>
      <Layout style={{ display: 'flex', flexDirection: 'column', backgroundColor: '#f0f2f5', marginTop: '-16px' }}>
        <TopSelectorArea
          handleOpenProbeEditor={handleOpenProbeEditor}
          handleOpenBuildConfigEditor={handleOpenBuildConfigEditor}
          handleOpenContainerConfigEditor={handleOpenContainerConfigEditor}
          handleOpenGroupConfigEditor={handleOpenGroupConfigEditor}
          loading={appDataLoading}
          probeConfig={getProbeConfig()}
        />

        <Layout className={styles.contentLayout} style={{ flexDirection: "row" }}>
          {/* 左侧边栏 - 分组选择 */}
          <Sider width={280} className={styles.sider}>
            <GroupList/>
          </Sider>

          {/* 中间内容区 - 配置信息 */}
          <Content className={styles.content}>{renderContent()}</Content>

          {/* 右侧边栏 - 部署操作 */}
          <Sider width={300} className={styles.deploySider}>
            <DeployOperation />
          </Sider>
        </Layout>

        {/* 探针编辑器 */}
        <ProbeEditor 
          visible={probeEditorVisible}
          probeConfig={getProbeConfig()}
          onSave={handleProbeSave}
          onCancel={() => setProbeEditorVisible(false)}
          saveProbes={saveProbes}
        />

        {/* 构建配置编辑器 */}
        <BuildConfigEditor
          visible={buildConfigEditorVisible}
          appData={deploy.appData}
          onCancel={() => setBuildConfigEditorVisible(false)}
          onSave={async () => { return true; }}
        />
        
        {/* 容器配置编辑器 */}
        <ContainerConfigEditor
          visible={containerConfigEditorVisible}
          appData={deploy.appData}
          onCancel={() => setContainerConfigEditorVisible(false)}
          onSave={async () => { return true; }}
        />
        
        {/* 环境变量编辑器 */}
        <Modal
          title="环境变量管理"
          open={envVariablesEditorVisible}
          onCancel={() => setEnvVariablesEditorVisible(false)}
          footer={null}
          width={800}
          styles={{ body: { padding: 0 } }}
        >
          <EnvVariablesTooltip
            editable={true}
          />
        </Modal>
      </Layout>
    </div>
  );
};

export default DeployPage;
