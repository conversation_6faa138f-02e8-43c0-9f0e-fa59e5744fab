import React from 'react';

// 部署状态枚举（与后端对应）
export enum DeployStatus {
  Pending = 1,   // 排队中
  Running = 2,   // 进行中
  Paused = 3,    // 暂停中
  Waiting = 4,   // 等待确认
  Completed = 5, // 已完成
  Confirmed = 6, // 已确认
  Cancelled = 7, // 已取消
  Failed = 8     // 失败
}

// 部署状态字符串类型
export type DeployStatusString =
  | '排队中'
  | '进行中'
  | '暂停中'
  | '等待确认'
  | '已完成'
  | '已确认'
  | '已取消'
  | '失败';

// 部署状态配置（颜色、背景色）
export const DEPLOY_STATUS_CONFIG: Record<DeployStatusString, { color: string; bgColor: string }> = {
  '排队中': { color: '#096dd9', bgColor: '#e6f7ff' },
  '进行中': { color: '#389e0d', bgColor: '#f6ffed' },
  '暂停中': { color: '#d46b08', bgColor: '#fff7e6' },
  '等待确认': { color: '#fa8c16', bgColor: '#fff2e8' },
  '已完成': { color: '#389e0d', bgColor: '#f6ffed' },
  '已确认': { color: '#531dab', bgColor: '#f9f0ff' },
  '已取消': { color: '#8c8c8c', bgColor: '#f5f5f5' },
  '失败': { color: '#cf1322', bgColor: '#fff1f0' },
};

// 状态数字到字符串的映射
export const mapStatusNumberToString = (status: DeployStatus): DeployStatusString => {
  switch (status) {
    case DeployStatus.Pending:
      return '排队中';
    case DeployStatus.Running:
      return '进行中';
    case DeployStatus.Paused:
      return '暂停中';
    case DeployStatus.Waiting:
      return '等待确认';
    case DeployStatus.Completed:
      return '已完成';
    case DeployStatus.Confirmed:
      return '已确认';
    case DeployStatus.Cancelled:
      return '已取消';
    case DeployStatus.Failed:
      return '失败';
    default:
      return '进行中';
  }
};


// 部署状态信息接口
export interface DeployStatusInfo {
  status: DeployStatus;
  progress: number;
  startTime: number;
  estimatedEndTime: number;
  currentStep: string;
  totalSteps: number;
  currentStepIndex: number;
  needsConfirmation?: number;
}

// 部署日志项接口
export interface DeployLogItem {
  type: 'info' | 'warning' | 'error' | 'success';
  content: string;
  timestamp: number;
  step?: string;
  stepIndex?: number;
}

// 部署状态组件属性接口
export interface DeployStatusProps {
  // 部署状态信息
  currentDeployStatus: DeployStatusInfo;
  setCurrentDeployStatus: React.Dispatch<React.SetStateAction<DeployStatusInfo>>;
  
  // 部署状态控制
  isDeploying: boolean;
  setIsDeploying: React.Dispatch<React.SetStateAction<boolean>>;
  
  // 日志数据
  logContent: DeployLogItem[];
  setLogContent: React.Dispatch<React.SetStateAction<DeployLogItem[]>>;
  
  // 部署步骤
  deploySteps: string[];
  
  // 回调函数
  onConfirmFunctionality?: () => void;
  onCancelDeployment?: () => void;
  onProceedToConfirmation?: () => void;
  
  // 日志控制相关
  onAddNewLog?: () => void;
  onClearLogs?: () => void;
}

// 部署分组接口
export interface DeployGroup {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  tag: string;
  tagIcon: React.ReactNode;
  status: 'inherit' | 'custom';
  configMode: 'default' | 'custom';
  deployStatus: DeployStatus;
  config: {
    runtime: API.Group;
    business?: Record<string, any>;
    base?: Record<string, any>;
  };
}

// 环境接口
export interface Environment {
  value: string;
  label: string;
  color: string;
  icon: React.ReactNode;
}

// 树节点接口
export interface TreeNode {
  title: string;
  key: string;
  icon: React.ReactNode;
  children?: TreeNode[];
} 