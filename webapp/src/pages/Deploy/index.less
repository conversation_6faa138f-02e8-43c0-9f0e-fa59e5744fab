.layout {
  display: flex;
  flex-direction: column;
  background-color: #f0f2f5;
  margin-top: -16px;
}

.topSelectorArea {
  background-color: #ffffff;
  margin-bottom: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border-bottom: 1px solid #f0f0f0;
  position: sticky;
  top: 0;
  z-index: 10;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  
  &:hover {
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
  }
}

.topSelectorContent {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.topSelectorLeft {
  display: flex;
  align-items: center;
  flex: 1;
  gap: 16px;
}

.topSelectorRight {
  margin-left: 16px;
}

.envSelectorWrapper {
  min-width: 180px;
  .ant-btn-group {
    display: flex;
    background: #f5f7fa;
    border-radius: 8px;
    padding: 2px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05), inset 0 0 0 1px rgba(0, 0, 0, 0.06);
  }
  
  .ant-btn {
    border-radius: 6px !important;
    margin: 0 2px;
    border: none !important;
    box-shadow: none !important;
    height: 32px;
    font-size: 13px;
    padding: 0 12px;
    transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
    
    &:hover {
      transform: translateY(-1px);
    }
    
    &:first-child {
      margin-left: 0;
    }
    
    &:last-child {
      margin-right: 0;
    }
  }
  
  .envButtonActive {
    font-weight: 500;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15) !important;
  }
  
  .envButton {
    background-color: transparent;
    color: #666;
    
    &:hover {
      color: #1890ff;
      background-color: rgba(24, 144, 255, 0.08);
    }
  }
}

.appSelectorWrapper {
  margin-left: 16px;
  display: flex;
  flex-direction: row;
  align-items: center;
  
  :global(.nav-button) {
    margin-left: 8px;
    height: 32px;
    
    .buttonIcon {
      margin-right: 4px;
    }
  }
}

.topSelectorCard {
  border-radius: 0;
  
  .ant-card-body {
    padding: 8px 16px;
  }
}

.topSelectorContainer {
  width: 100%;
  min-height: 36px;
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
  padding: 8px 16px;
  background: linear-gradient(100deg, #ffffff 0%, #f9f9f9 50%, #fafafa 100%);
  border-radius: 2px;
}

.topNavSegmented {
  background: transparent;
  transform: translateY(2px);

  .ant-segmented {
    padding: 4px;
    border-radius: 8px;
    background: rgba(250, 250, 250, 0.8);
    box-shadow: 0 2px 6px rgba(0,0,0,0.08), inset 0 1px 0 rgba(255,255,255,0.6);
    border: 1px solid rgba(0,0,0,0.06);
    overflow: hidden;
    backdrop-filter: blur(8px);

    &-item {
      min-height: 32px;
      padding: 4px 12px;
      margin: 0 2px;
      border-radius: 6px;
      transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
      color: #666;

      &:hover {
        color: #1890ff;
        background-color: rgba(24, 144, 255, 0.04);
        transform: translateY(-1px);
      }

      &-selected {
        background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%) !important;
        box-shadow: 0 2px 8px rgba(24, 144, 255, 0.35);
        color: white !important;
        font-weight: 500;
        transform: translateY(-1px);
        
        .segmented-icon {
          color: white;
        }
      }

      .segmented-icon {
        margin-right: 6px;
        font-size: 14px;
        vertical-align: -0.15em;
      }

      .segmented-label {
        font-size: 12px;
        vertical-align: middle;
      }
    }
  }
}

.customNavButtons {
  margin-left: auto;
  display: flex;
  
  .ant-btn-group {
    display: flex;
    background: #f5f7fa;
    border-radius: 8px;
    padding: 3px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05), inset 0 0 0 1px rgba(0, 0, 0, 0.06);
  }
  
  .nav-button {
    height: 32px;
    margin: 0 2px;
    padding: 0 10px;
    border-radius: 6px !important;
    border: none !important;
    background: transparent;
    transition: all 0.25s cubic-bezier(0.645, 0.045, 0.355, 1);
    color: rgba(0, 0, 0, 0.65);
    display: flex;
    align-items: center;
    justify-content: center;
    
    &:first-child {
      margin-left: 0;
    }
    
    &:last-child {
      margin-right: 0;
    }
    
    &:hover {
      background: rgba(0, 0, 0, 0.04);
      color: #1890ff;
      transform: translateY(-1px);
    }
    
    &.active {
      background: #1890ff;
      color: #fff;
      box-shadow: 0 2px 6px rgba(24, 144, 255, 0.4);
      
      .buttonIcon {
        color: #fff;
      }
      
      .buttonLabel {
        color: #fff;
      }
    }
    
    .buttonIcon {
      margin-right: 6px;
      font-size: 14px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .buttonLabel {
      font-size: 13px;
      font-weight: 500;
      white-space: nowrap;
    }
  }
}

// 信息项样式
.infoItem {
  display: flex;
  margin-bottom: 8px;
}

.infoLabel {
  font-weight: 500;
  color: #666;
  width: 80px;
  flex-shrink: 0;
}

.infoValue {
  color: #333;
  flex: 1;
  word-break: break-all;
}

// 应用选择器样式
:global(.app-selector) {
  margin: 0 12px;
  flex: 1 1 auto;
  max-width: 400px;
  
  .ant-select-selector,
  .ant-input {
    border-radius: 6px !important;
    border: 1px solid #e8e8e8 !important;
    background: #fcfcfc !important;
    height: 32px !important;
    padding: 0 11px !important;
    display: flex;
    align-items: center;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.02);
    transition: all 0.3s ease;
    
    &:hover {
      border-color: #40a9ff !important;
      box-shadow: 0 2px 6px rgba(24, 144, 255, 0.15);
      transform: translateY(-1px);
    }
  }
  
  .ant-popover-inner {
    border-radius: 8px !important;
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12) !important;
    overflow: hidden;
  }
  
  .ant-select-selection-item {
    font-weight: 500;
    color: #1890ff;
  }
}

.topNavTabs {
  margin-bottom: 0 !important;
  
  .ant-tabs-nav {
    margin-bottom: 0;
    padding: 4px 0;
    
    &::before {
      border: none;
    }
  }
  
  .ant-tabs-tab {
    padding: 6px 12px;
    margin: 0 4px;
    transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
    border-radius: 6px;
    opacity: 0.85;
    background: #f8f8f8;
    border: 1px solid #f0f0f0;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
    
    &:hover {
      opacity: 1;
      background: linear-gradient(to bottom, #ffffff, #f5f5f5);
      border-color: #e0e0e0;
      transform: translateY(-2px);
    }
    
    &.ant-tabs-tab-active {
      opacity: 1;
      background: linear-gradient(135deg, #e6f7ff, #f0f9ff);
      border-color: #91d5ff;
      transform: translateY(-2px);
      box-shadow: 0 3px 8px rgba(24, 144, 255, 0.15);
      
      .ant-tabs-tab-btn {
        color: #1890ff;
        font-weight: 500;
        text-shadow: 0 0 0.2px rgba(0, 0, 0, 0.1);
      }
    }
    
    .ant-tabs-tab-btn {
      color: #333;
      display: flex;
      align-items: center;
      
      .tab-icon {
        margin-right: 6px;
        font-size: 14px;
      }
    }
  }
  
  .ant-tabs-ink-bar {
    display: none;
  }
}

.sider {
  background-color: #f0f2f5;
  box-shadow: none;
}

.content {
  margin: 0 16px;
  overflow-y: visible;
  flex: 1;
  background-color: #f0f2f5;
}

.deploySider {
  background-color: #f0f2f5;
  box-shadow: none;
  overflow: visible !important;

  .ant-layout-sider-children {
    overflow: visible !important;
    height: auto !important;
  }
}

.contentLayout {
  padding-left: 0;
  display: flex;
  flex-direction: row;
  background-color: #f0f2f5;
}

.groupItem {
  // 已移动到GroupList/index.less
}

.groupItemSelected {
  // 已移动到GroupList/index.less
}

.groupIcon {
  // 已移动到GroupList/index.less
}

.pageHeader {
  background: white;
  padding: 24px 32px;
  margin-bottom: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f2f5;

  .headerTitle {
    margin: 0;
    color: #1c1c1c;
    font-weight: 600;
    font-size: 24px;
  }

  .ant-select {
    .ant-select-selector {
      border-radius: 8px;
      border: 2px solid #e8f4ff;
      box-shadow: none;
      transition: all 0.3s ease;

      &:hover {
        border-color: #91d5ff;
      }
    }

    &.ant-select-focused .ant-select-selector {
      border-color: #1890ff;
      box-shadow: 0 0 0 3px rgba(24, 144, 255, 0.1);
    }
  }
}

.contentCard {
  background: white;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.04);
  border: 1px solid #f0f2f5;
  margin-bottom: 6px;
  overflow: hidden;

  .ant-card-body {
    padding: 10px 12px;
  }

  &.primaryCard {
    .ant-card-body {
      padding: 12px;
    }
  }
}

.deploymentTag {
  border-radius: 3px;
  font-weight: 500;
  padding: 2px 6px;
  border: none;
  font-size: 11px;
  line-height: 18px;
  transition: all 0.2s ease;
  display: inline-block;
  word-break: break-all;
  white-space: normal;

  &:hover {
    transform: scale(1.02);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.12);
  }

  &.mainTag {
    background: rgba(82, 196, 26, 0.15);
    color: #52c41a;
    border: 1px solid rgba(82, 196, 26, 0.3);
  }

  &.versionTag {
    background: rgba(114, 46, 209, 0.15);
    color: #722ed1;
    border: 1px solid rgba(114, 46, 209, 0.3);
  }
}

.statusTag {
  border-radius: 2px;
  font-weight: 500;
  border: none;
  font-size: 10px;
  padding: 0 4px;
  line-height: 16px;

  &.inheritTag {
    background: #f6f8fa;
    color: #586069;
    border: 1px solid #e1e4e8;
  }

  &.customTag {
    background: linear-gradient(135deg, #52c41a, #73d13d);
    color: white;
  }
}

.configSection {
  .ant-tabs {
    .ant-tabs-tab {
      padding: 4px 8px;
      font-weight: 500;
      border-radius: 3px 3px 0 0;
      margin-right: 2px;
      transition: all 0.3s ease;
      font-size: 12px;

      &:hover {
        background: #f8faff;
      }

      &.ant-tabs-tab-active {
        background: #e6f7ff;
        border-color: #91d5ff;
      }
    }

    .ant-tabs-content-holder {
      padding-top: 6px;
    }
  }
}

.fileTableWrapper {
  background: #ffffff;
  border-radius: 4px;
  overflow: hidden;
  border: 1px solid #f0f0f0;
}

.fileTable {
  .ant-table-thead > tr > th {
    background: #fafafa;
    font-weight: 600;
    font-size: 12px;
    color: #333;
    padding: 8px 12px;
  }

  .ant-table-tbody > tr > td {
    font-size: 12px;
    padding: 8px 12px;
    color: #333;
    border-bottom: 1px solid #f0f0f0;
  }

  .ant-table-tbody > tr:hover > td {
    background: #f5f5f5;
  }

  .ant-btn-link {
    font-size: 12px;
    padding: 0 6px;
    height: auto;
  }

  .configMark {
    display: inline-block;
    width: 100%;
    text-align: center;
    color: #1890ff;
    font-weight: 500;
  }
}

.fileRow {
  transition: all 0.2s ease;

  &:hover {
    cursor: pointer;
  }
}

.basicInfoWrapper {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.infoSection {
  background: #ffffff;
  border-radius: 4px;
  border: 1px solid #f0f0f0;
  overflow: hidden;
}

.infoSectionTitle {
  font-size: 13px;
  font-weight: 600;
  color: #333;
  background: #fafafa;
  padding: 8px 12px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
}

.infoTable {
  padding: 12px;
}

.infoRow {
  width: 100%;
}

.infoItem {
  display: flex;
  flex-direction: row;
  margin-bottom: 12px;
  align-items: baseline;
}

.infoLabel {
  width: 80px;
  font-size: 12px;
  color: #666;
  flex-shrink: 0;
}

.infoValue {
  flex: 1;
  font-size: 12px;
  color: #333;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;

  .anticon {
    color: #1890ff;
    cursor: pointer;
  }
}

.runtimeConfigWrapper {
  background: white;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
}

.runtimeConfigHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #fafafa;
  border-bottom: 1px solid #f0f0f0;
}

.runtimeConfigTitle {
  font-size: 14px;
  font-weight: 600;
  color: #333;

  .runtimeConfigSubtitle {
    font-size: 12px;
    font-weight: normal;
    color: #8c8c8c;
    margin-left: 8px;
  }
}

.runtimeConfig {
  background: white;
  padding: 16px;
  position: relative;

  .configItem {
    margin-bottom: 12px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .configLabel {
    color: #8c8c8c;
    font-size: 12px;
    margin-bottom: 4px;
  }

  .configValue {
    color: #333;
    font-size: 13px;
    font-weight: 500;
  }

  .inheritButton {
    position: absolute;
    top: 16px;
    right: 16px;
    border: none;
    background: transparent;
    color: #1890ff;
    font-weight: 500;
    font-size: 12px;
    padding: 1px 4px;

    &:hover {
      background: rgba(24, 144, 255, 0.1);
      color: #40a9ff;
    }
  }
}

.runtimeConfigEdit {
  background: white;
  padding: 16px;

  .runtimeForm {
    .ant-form-item {
      margin-bottom: 16px;

      .ant-form-item-label {
        padding-bottom: 4px;

        label {
          font-size: 13px;
          color: #666;
        }
      }
    }
  }
}

.actionCard {
  .deployTypeSelector {
    display: flex;
    align-items: center;
    margin-bottom: 8px;

    .deployTypeLabel {
      color: #333;
      font-size: 13px;
      font-weight: 500;
      margin-right: 16px;
    }

    .ant-radio-group {
      .ant-radio-button-wrapper {
        height: 28px;
        padding: 0 16px;
        font-size: 13px;
        line-height: 26px;
      }
    }
  }

  .deployGroupSelector {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    margin-bottom: 16px;

    .deployGroupLabel {
      color: #333;
      font-size: 13px;
      font-weight: 500;
      margin-right: 16px;
      flex-shrink: 0;
    }

    .ant-checkbox-group {
      display: flex;
      flex-wrap: wrap;
      gap: 12px;

      .ant-checkbox-wrapper {
        margin-right: 0;
      }
    }

    .ant-btn-link {
      padding: 0 8px;
      font-size: 12px;
      height: auto;

      &:hover {
        color: #40a9ff;
        background: transparent;
      }
    }
  }

  .deployActions {
    display: flex;
    justify-content: flex-end;

    .ant-btn {
      min-width: 88px;
      height: 32px;
      font-size: 14px;
      font-weight: 500;

      &:not(:first-child) {
        margin-left: 12px;
      }

      &.ant-btn-primary {
        background: linear-gradient(135deg, #1890ff, #40a9ff);
        border: none;

        &:hover:not(:disabled) {
          background: linear-gradient(135deg, #40a9ff, #69c0ff);
        }

        &:disabled {
          background: #f5f5f5;
          color: #d9d9d9;
          cursor: not-allowed;
        }
      }
    }
  }

  .actionTitle {
    margin: 0 0 2px 0;
    color: #1c1c1c;
    font-weight: 600;
    font-size: 13px;
  }

  .actionDescription {
    color: #8c8c8c;
    font-size: 11px;
    margin-bottom: 0;
  }
}

.radioGroup {
  margin-top: 8px;

  .ant-radio-wrapper {
    font-weight: 500;
    margin-right: 12px;
    font-size: 12px;

    .ant-radio {
      .ant-radio-inner {
        border-width: 1px;
        width: 12px;
        height: 12px;

        &:after {
          width: 4px;
          height: 4px;
          top: 2px;
          left: 2px;
        }
      }

      &.ant-radio-checked .ant-radio-inner {
        border-color: #1890ff;
        background-color: #1890ff;
      }
    }
  }
}

.deployStatusTag {
  // 已移动到GroupList/index.less
}

.selectorCard {
  .ant-card-body {
    padding: 16px;
    background: linear-gradient(135deg, #ffffff 0%, #f9fbff 100%);
  }

  .appSelectorContainer {
    display: flex;
    flex-direction: column;
  }

  .envTitle,
  .appTitle {
    font-size: 13px;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
  }

  .envSelectorWrapper {
    display: flex;
    flex-direction: row;
  }

  .envSelector {
    display: flex;
    justify-content: flex-start;
    
    .envButtonGroup {
      display: flex;
    }
    
    .ant-btn {
      min-width: 70px;
      height: 26px;
      border-radius: 4px;
      margin-right: 4px;
      padding: 0 10px;
      line-height: 1;
      font-size: 12px;
      
      &:last-child {
        margin-right: 0;
      }
      
      .anticon {
        font-size: 11px;
        margin-right: 3px;
      }
      
      &.envButtonActive {
        transform: translateY(-1px);
      }
    }
  }

  .appSection {
    .appTitle {
      margin-bottom: 10px;
    }
  }

  .appSelectorTrigger {
    border-radius: 4px;
    font-weight: 500;
    height: 28px;
    padding: 0 10px;
    
    &:hover {
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.09);
    }
    
    .appName {
      max-width: 200px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      display: inline-block;
      vertical-align: middle;
      margin-right: 8px;
      font-size: 12px;
    }
    
    .anticon {
      font-size: 11px;
    }
  }

  .appInfoButton {
    width: 32px;
    height: 32px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s;

    &:hover {
      background: #e6f7ff;
      color: #1890ff;
    }

    .anticon {
      font-size: 16px;
    }
  }
}

// 应用信息弹窗样式
.appInfoPopover {
  width: 640px;
  max-height: 500px;
  overflow-y: auto;
  padding: 0;

  .infoSection {
    border-bottom: 1px solid #f0f0f0;
    margin-bottom: 12px;

    &:last-child {
      border-bottom: none;
      margin-bottom: 0;
    }
  }

  .infoSectionTitle {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid #f0f0f0;
  }

  .infoTable {
    padding: 0 0 12px 0;
  }

  .infoItem {
    margin-bottom: 12px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .infoLabel {
    color: #666;
    font-size: 13px;
    margin-bottom: 4px;
  }

  .infoValue {
    color: #333;
    font-size: 13px;
    font-weight: 500;
    word-break: break-all;
  }
}

.appInfoPopoverOverlay {
  .ant-popover-inner {
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  .ant-popover-title {
    padding: 10px 16px;
    font-size: 14px;
    font-weight: 600;
    border-bottom: 1px solid #f0f0f0;
  }

  .ant-popover-inner-content {
    padding: 12px 16px;
  }
}

.appSelectorPopover {
  width: 280px;

  .popoverHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    gap: 8px;
    background: linear-gradient(135deg, #f6f9fc 0%, #ffffff 100%);
    border-bottom: 1px solid #f0f0f0;

    .ant-input-affix-wrapper {
      flex: 1;
      border-radius: 8px;
      font-size: 13px;
      border: 1px solid #e8e8e8;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

      &:hover {
        border-color: #40a9ff;
        box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
        transform: translateY(-1px);
      }

      &.ant-input-affix-wrapper-focused {
        border-color: #1890ff;
        box-shadow: 0 0 0 3px rgba(24, 144, 255, 0.1), 0 4px 12px rgba(24, 144, 255, 0.15);
        transform: translateY(-1px);
      }

      .ant-input {
        font-size: 13px;
        color: #2c3e50;
        
        &::placeholder {
          color: #bdc3c7;
        }
      }

      .ant-input-prefix {
        color: #7f8c8d;
      }
    }

    .ant-btn {
      font-size: 14px;
      color: #1890ff;
      flex-shrink: 0;
      border-radius: 8px;
      height: 36px;
      width: 36px;
      padding: 0;
      border: 1px solid #e8f4ff;
      background: linear-gradient(135deg, #ffffff 0%, #f8fcff 100%);
      box-shadow: 0 2px 4px rgba(24, 144, 255, 0.08);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

      &:hover {
        color: #40a9ff;
        background: linear-gradient(135deg, #e6f7ff 0%, #f0f9ff 100%);
        border-color: #91d5ff;
        box-shadow: 0 4px 12px rgba(24, 144, 255, 0.2);
        transform: translateY(-2px) scale(1.05);
      }

      &:active {
        transform: translateY(-1px) scale(1.02);
      }
    }
  }

  .popoverTree {
    padding: 8px 12px 16px;
    max-height: 420px;
    overflow-y: auto;
    width: 100%;
    background: #ffffff;

    // 自定义滚动条
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background: linear-gradient(180deg, #d9d9d9 0%, #bfbfbf 100%);
      border-radius: 3px;
      transition: background 0.3s ease;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: linear-gradient(180deg, #bfbfbf 0%, #8c8c8c 100%);
    }

    &::-webkit-scrollbar-track {
      background: #f5f5f5;
      border-radius: 3px;
    }

    .ant-tree {
      background: transparent;

      .ant-tree-treenode {
        position: relative;
        margin: 0;
        padding: 0;

        // 连接线样式优化
        &::before {
          border-color: #e8e8e8;
        }

        .ant-tree-node-content-wrapper {
          border-radius: 8px;
          padding: 8px 12px;
          font-size: 13px;
          line-height: 1.4;
          margin: 2px 0;
          position: relative;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          cursor: pointer;
          overflow: hidden;

          // 添加微妙的背景纹理
          &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, transparent 0%, rgba(255, 255, 255, 0.1) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
          }

          &:hover {
            background: linear-gradient(135deg, #f0f7ff 0%, #e6f4ff 100%);
            box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
            transform: translateX(4px);

            &::before {
              opacity: 1;
            }
          }

          .ant-tree-title {
            color: #2c3e50;
            font-weight: 500;
            font-size: 13px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            transition: color 0.3s ease;
          }
        }

        // 选中状态
        .ant-tree-node-selected {
          .ant-tree-node-content-wrapper {
            background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%) !important;
            color: white;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
            transform: translateX(6px);

            .ant-tree-title {
              color: white;
              font-weight: 600;
            }

            &::before {
              opacity: 1;
              background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
            }
          }
        }

        // 图标样式优化
        .ant-tree-iconEle {
          margin-right: 8px;
          width: 18px;
          height: 18px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 4px;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

          &:hover {
            transform: scale(1.1);
          }
        }

        // 不同层级的样式差异
        &[data-level="0"] .ant-tree-node-content-wrapper {
          font-weight: 600;
          font-size: 14px;
          padding: 10px 12px;
          
          .ant-tree-iconEle {
            font-size: 16px;
          }
        }

        &[data-level="1"] .ant-tree-node-content-wrapper {
          font-weight: 500;
          margin-left: 16px;
          
          .ant-tree-iconEle {
            font-size: 15px;
          }
        }

        &[data-level="2"] .ant-tree-node-content-wrapper {
          margin-left: 32px;
        }

        &[data-level="3"] .ant-tree-node-content-wrapper {
          margin-left: 48px;
        }
      }

      // 连接线样式
      .ant-tree-indent-unit {
        width: 20px;
      }
      
      .ant-tree-indent-unit-start {
        &::before {
          border-left: 1px solid #f0f0f0;
        }
      }
      
      .ant-tree-indent-unit-end {
        &::before {
          border-left: 1px solid #f0f0f0;
          border-bottom: 1px solid #f0f0f0;
        }
      }
    }

    // 添加加载状态样式
    .tree-loading {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20px;
      color: #8c8c8c;
      font-size: 13px;
    }

    // 空状态样式
    .tree-empty {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 40px 20px;
      color: #8c8c8c;
      font-size: 13px;

      .empty-icon {
        font-size: 32px;
        color: #d9d9d9;
        margin-bottom: 12px;
      }
    }
  }
}

.appSelectorPopoverOverlay {
  .ant-popover-inner {
    padding: 0;
    border-radius: 12px;
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.12), 0 2px 6px rgba(0, 0, 0, 0.08);
    width: 500px;
    border: 1px solid rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(8px);
    overflow: hidden;
  }

  .ant-popover-content {
    .ant-popover-arrow {
      display: none;
    }
    
    width: 500px;
  }

  .ant-popover-inner-content {
    padding: 0;
    width: 100%;
  }

  // 添加弹出动画
  &.ant-popover-placement-bottomLeft {
    .ant-popover-inner {
      transform-origin: 0 0;
      animation: popoverSlideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }
  }
}

@keyframes popoverSlideIn {
  0% {
    opacity: 0;
    transform: translateY(-8px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.deployCard {
  height: 100%;
  overflow: visible;
}

.deployCardTitle {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 16px;
  color: #333;
}

.deployCardContent {
  display: flex;
  flex-direction: column;
  gap: 16px;
  overflow: visible;

  .ant-form {
    overflow: visible;
  }

  .ant-form-item {
    margin-bottom: 12px;
  }
}

.deployTypeSelector {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.deployTypeLabel,
.deployGroupLabel {
  font-size: 13px;
  margin-bottom: 8px;
  color: #333;
  font-weight: 600;
}

.deployTypeHint {
  margin-top: 4px;
  font-size: 12px;
}

.deployGroupSelector {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.checkboxContainer {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.currentGroupNote {
  font-size: 12px;
  color: #999;
  margin-top: 8px;
  display: flex;
  align-items: center;

  .anticon {
    margin-right: 4px;
    font-size: 12px;
  }
}

.deployStatus {
  border-top: 1px dashed #eee;
  padding-top: 16px;
}

.statusTitle {
  font-size: 14px;
  color: #333;
  margin-bottom: 12px;
  font-weight: 500;
}

.deployProgress {
  margin-top: 8px;
}

.progressItem {
  margin-bottom: 12px;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #f0f9ff;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }
}

.progressTitle {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
  align-items: center;

  span {
    font-size: 13px;
    color: #333;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 180px;
  }
}

.smallTag {
  font-size: 10px !important;
  padding: 0 4px !important;
  margin: 0 !important;
  line-height: 1.4 !important;
}

.noGroupSelected {
  color: #999;
  text-align: center;
  padding: 16px 0;
  font-size: 13px;
  background-color: #fafafa;
  border-radius: 4px;
}

.deployActions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
  margin-top: auto;
  padding-top: 24px;
  border-top: 1px dashed #eee;
}

// 日志区域样式
.logCard {
  margin-bottom: 16px;

  :global {
    .ant-card-head {
      min-height: 40px;
      padding: 0 12px;

      .ant-card-head-title {
        padding: 8px 0;
      }
    }
  }
}

.logCardHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;

  span {
    font-size: 14px;
    font-weight: 500;
  }
}

.logContent {
  height: 100%;
  max-height: 300px;
  overflow-y: auto;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier,
    monospace;
  font-size: 12px;
  line-height: 1.5;
  background-color: #f5f5f5;
  border-radius: 4px;
  padding: 8px;

  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background: #ccc;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-track {
    background: #f0f0f0;
    border-radius: 3px;
  }
}

.logLine {
  white-space: pre-wrap;
  word-break: break-all;
  padding: 2px 0;
  display: flex;
  align-items: flex-start;
}

.logTimestamp {
  color: #888;
  margin-right: 6px;
  flex-shrink: 0;
}

.logType {
  width: 70px;
  margin-right: 8px;
  flex-shrink: 0;
  font-weight: 500;
}

.logMessage {
  flex: 1;
}

.infoLog {
  color: #333;

  .logType {
    color: #1890ff;
  }
}

.warningLog {
  color: #333;

  .logType {
    color: #faad14;
  }
}

.errorLog {
  color: #333;

  .logType {
    color: #f5222d;
  }
}

.successLog {
  color: #333;

  .logType {
    color: #52c41a;
    font-weight: 600;
  }
}

.emptyLog {
  text-align: center;
  padding: 20px;
  color: #999;
}

// Pod信息区域样式
.podCard {
  margin-bottom: 16px;

  :global {
    .ant-card-head {
      min-height: 40px;
      padding: 0 12px;

      .ant-card-head-title {
        padding: 8px 0;
      }
    }
  }
}

.podCardHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;

  span {
    font-size: 14px;
    font-weight: 600;
    color: #333;
  }
}

.podTableWrapper {
  overflow: auto;

  :global {
    .ant-table-small {
      border-radius: 4px;
    }

    .ant-table-thead > tr > th {
      background-color: #f5f5f5;
      font-size: 12px;
      padding: 8px;
    }

    .ant-table-tbody > tr > td {
      font-size: 12px;
      padding: 6px 8px;
    }

    .ant-table-tbody > tr:hover > td {
      background-color: #f0f7ff;
    }
  }
}

.actionButton {
  padding: 0 4px;
  margin: 0 2px;
  height: 22px;
  width: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #1890ff;
  transition: all 0.3s;

  &:hover {
    background-color: #e6f7ff;
    color: #40a9ff;
    transform: scale(1.1);
  }
}

.podSummary {
  display: flex;
  justify-content: space-between;
  padding: 8px 0 0;
  font-size: 12px;
  color: #666;
}

// 终端样式
.terminalContainer {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #1e1e1e;
  color: #f0f0f0;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier,
    monospace;
  font-size: 13px;
}

.terminalHeader {
  padding: 6px 12px;
  background-color: #333;
  border-bottom: 1px solid #444;

  span {
    color: #ddd;
    font-weight: 500;
  }
}

.terminalBody {
  flex: 1;
  padding: 12px;
  overflow-y: auto;
  line-height: 1.6;
}

.terminalPrompt {
  margin-top: 10px;
  display: flex;
  align-items: center;
}

.promptUser {
  color: #3c9;
}

.promptCursor {
  display: inline-block;
  width: 8px;
  height: 16px;
  background-color: #f0f0f0;
  animation: blink 1s step-end infinite;
}

@keyframes blink {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
}

// Pod日志样式
.podLogsContainer {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.podLogsToolbar {
  padding: 8px 12px;
  background-color: #f5f5f5;
  border-bottom: 1px solid #e8e8e8;
}

.podLogsContent {
  flex: 1;
  padding: 12px;
  overflow-y: auto;
  background-color: #fafafa;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier,
    monospace;
  font-size: 12px;
  line-height: 1.5;
}

// 日志区域样式
.logCard {
  margin-bottom: 16px;

  :global {
    .ant-card-head {
      min-height: 40px;
      padding: 0 12px;

      .ant-card-head-title {
        padding: 8px 0;
      }
    }
  }
}

.historyCard {
  .ant-card-head {
    min-height: 40px;
    padding: 0 12px;

    .ant-card-head-title {
      padding: 8px 0;
    }
  }
}

.historyCardHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;

  > span {
    font-size: 14px;
    font-weight: 600;
    color: #333;
  }

  .ant-input-affix-wrapper {
    border-radius: 4px;
    height: 24px;

    .ant-input {
      font-size: 12px;
    }
  }

  .ant-select {
    .ant-select-selector {
      height: 24px;
      padding-top: 0;
      padding-bottom: 0;

      .ant-select-selection-item {
        line-height: 22px;
        font-size: 12px;
      }
    }
  }
}

.historyTableWrapper {
  overflow-x: auto; // 确保容器支持水平滚动

  .ant-table {
    min-width: 800px; // 设置最小宽度，确保表格不会过度压缩
  }

  .ant-table-thead > tr > th {
    background: #fafafa;
    font-weight: 600;
    font-size: 12px;
    color: #333;
    padding: 8px 12px;
    white-space: nowrap; // 防止表头文字换行
  }

  .ant-table-tbody > tr > td {
    font-size: 12px;
    padding: 8px 12px;
    color: #333;
    white-space: nowrap; // 防止单元格内容换行造成布局混乱
  }

  .ant-table-tbody > tr:hover > td {
    background: #f5f5f5;
  }

  // 操作列的特殊样式
  .ant-table-fixed-right {
    box-shadow: -2px 0 4px rgba(0, 0, 0, 0.08); // 给固定列添加阴影
  }

  .ant-pagination {
    margin: 12px 0 0 0;
  }
}

.deployDetailContent {
  .ant-tabs-tab {
    padding: 6px 12px;
    font-size: 13px;
  }

  .ant-descriptions-item-label {
    width: 100px;
    font-weight: 500;
    color: #333;
  }

  .configTitle {
    font-weight: 500;
    margin-bottom: 8px;
    color: #333;
    font-size: 13px;
  }

  .ant-descriptions {
    margin-bottom: 16px;
  }
}

.deployDetailLogs {
  max-height: 300px;
  overflow: auto;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  padding: 8px;
  background-color: #fafafa;
}

.redeployModalContent {
  .ant-alert {
    border-radius: 4px;
    margin-bottom: 16px;
  }

  .ant-form-item {
    margin-bottom: 16px;
  }

  .ant-radio-group {
    display: flex;
    flex-wrap: wrap;

    .ant-radio-wrapper {
      margin-right: 16px;
      margin-bottom: 8px;
    }
  }

  .ant-checkbox-group {
    display: flex;
    flex-wrap: wrap;

    .ant-checkbox-wrapper {
      margin-right: 16px;
      margin-bottom: 8px;
      width: calc(33.33% - 16px);
    }
  }
}

.deployStatusCard {
  .ant-card-body {
    padding: 12px;
  }
}

.deployStatusHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.deployStatusTitle {
  font-size: 14px;
  font-weight: 600;
  display: flex;
  align-items: center;
}

.deployStatusContent {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.deployProgressInfo {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.deployTimeInfo {
  display: flex;
  justify-content: space-between;
  font-size: 12px;

  .timeLabel {
    color: #8c8c8c;
    margin-right: 4px;
  }

  .timeValue {
    color: #333;
    font-weight: 500;
  }
}

.deployProgress {
  .ant-progress {
    margin-bottom: 4px;
  }
}

.stepInfo {
  font-size: 12px;
  color: #8c8c8c;

  .currentStep {
    font-weight: 500;
  }
}

.deploySteps {
  display: flex;
  justify-content: space-between;
  margin-top: 8px;
  padding: 0 4px;
}

.deployStep {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 18%;
  position: relative;

  &:not(:last-child)::after {
    content: '';
    position: absolute;
    top: 12px;
    right: -50%;
    width: 100%;
    height: 2px;
    background-color: #f0f0f0;
    z-index: 1;
  }

  &.stepCompleted {
    .stepIcon {
      background-color: #52c41a;
      color: white;
    }

    &:not(:last-child)::after {
      background-color: #52c41a;
    }
  }

  &.stepCurrent {
    .stepIcon {
      background-color: #1890ff;
      color: white;
      box-shadow: 0 0 0 4px rgba(24, 144, 255, 0.2);
    }
  }

  &.stepPending {
    .stepIcon {
      background-color: #f0f0f0;
      color: #8c8c8c;
    }
  }
}

.stepIcon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  margin-bottom: 8px;
  position: relative;
  z-index: 2;
  transition: all 0.3s ease;
}

.stepName {
  font-size: 12px;
  color: #333;
  text-align: center;
}

.deployLogSection {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.deployLogHeader {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 8px;
}

.logContent {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  padding: 8px;
  background-color: #fafafa;
}

.deployStatusLogCard {
  .ant-card-body {
    padding: 12px;
  }
}

.deployStatusLogHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.deployStatusLogContent {
  display: flex;
  flex-direction: column;
}

.deployStatusSection {
  margin-bottom: 8px;
}

// 配置文件管理样式
.configManagementWrapper {
  margin: 0;
  padding: 0;
}

.configTableContainer {
  background: white;
  border-radius: 6px;
  overflow: hidden;
  border: 1px solid #e8e8e8;
  margin-top: 8px;
}

.configTableHeaders {
  display: flex;
  background: #fafafa;
  border-bottom: 1px solid #e8e8e8;
}

.configTableHeaderLeft,
.configTableHeaderRight {
  flex: 1;
  padding: 8px 12px;
  font-weight: 500;
  font-size: 13px;
  color: #262626;

  display: flex;
  align-items: center;
  justify-content: space-between;
}

.configTableHeaderLeft {
  border-right: 1px solid #e8e8e8;
}

.configTableBody {
  // 表格主体样式
}

.configTable {
  .ant-table-tbody > tr > td {
    padding: 0 !important;
    border-bottom: 1px solid #f0f0f0;
    vertical-align: top;
    border-right: none;
  }

  .ant-table-tbody > tr > td:nth-child(2) {
    border-left: 1px solid #f5f5f5;
    border-right: 1px solid #f5f5f5;
    background: #fafafa;
  }

  .ant-table-tbody > tr:hover > td {
    background: #f8f9fa !important;
  }

  .ant-table-tbody > tr:hover > td:nth-child(2) {
    background: #f0f2f5 !important;
  }

  .ant-table-thead {
    display: none;
  }
}

.configFileCell {
  display: flex;
  align-items: center;
  padding: 6px 12px;
  min-height: 42px;
}

.configFileInfo {
  flex: 1;
  min-width: 0;
}

.configFileName {
  display: flex;
  align-items: center;
  margin-bottom: 2px;
  flex-wrap: wrap;
  gap: 4px;

  span {
    margin-left: 4px;
    font-weight: 500;
    color: #333;
    font-size: 13px;
  }

  .anticon {
    color: #1890ff;
    font-size: 13px;
  }

  .ant-tag {
    margin-right: 0;
    margin-left: 8px;
    line-height: 14px;
    padding: 0 4px;
    font-size: 11px;
  }
}

.configFileMeta {
  display: flex;
  color: #8c8c8c;
  font-size: 11px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.configFileActions {
  margin-left: 12px;
  opacity: 0;
  transition: opacity 0.3s ease;
  white-space: nowrap;

  .configFileCell:hover & {
    opacity: 1;
  }

  a {
    color: #1890ff;
    text-decoration: none;
    font-size: 12px;

    &:hover {
      color: #40a9ff;
    }
  }
}

.configRelationCell {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 6px 4px;
  position: relative;
  min-height: 42px;
  border-left: none;
  border-right: none;
}

.relationIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background: #f5f5f5;
  transition: all 0.3s ease;
  cursor: default;

  .anticon {
    font-size: 14px;
    color: #8c8c8c;
  }

  &:hover {
    background: #e6f7ff;
    transform: scale(1.05);

    .anticon {
      color: #1890ff;
    }
  }
}

.inheritedRelation {
  background: #f6ffed;

  .anticon {
    color: #52c41a;
  }

  &:hover {
    background: #d9f7be;

    .anticon {
      color: #389e0d;
    }
  }
}

.customizedRelation {
  background: #fff2e8;

  .anticon {
    color: #fa8c16;
  }

  &:hover {
    background: #ffe7ba;

    .anticon {
      color: #d46b08;
    }
  }
}

/* 配置文件编辑器样式 */
.configEditor {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  overflow: hidden;
}

/* 配置文件编辑器样式 - 使用Monaco编辑器原生波浪线 */
.configEditor {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  overflow: hidden;
}

/* Arthas 相关样式 */
.arthasContainer {
  .arthasOperations {
    .ant-card {
      height: 100%;

      .ant-card-head {
        padding: 0 12px;
        min-height: 36px;

        .ant-card-head-title {
          font-size: 14px;
          font-weight: 600;
        }
      }

      .ant-card-body {
        padding: 12px;
      }
    }

    .ant-btn {
      height: 32px;
      line-height: 1.5;
      font-size: 12px;
      border-radius: 4px;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
      }
    }
  }
}

.arthasWebConsole {
  height: 600px;
  position: relative;
  background: #f5f5f5;

  .webConsoleHeader {
    padding: 12px 16px;
    background: white;
    border-bottom: 1px solid #e8e8e8;

    .ant-tag {
      border-radius: 4px;
      font-size: 12px;
      padding: 2px 8px;
      border: none;
    }
  }

  .webConsoleContent {
    height: calc(100% - 60px);
    position: relative;
    background: #1e1e1e;
    color: #d4d4d4;

    iframe {
      background: #1e1e1e;
    }
  }
}

.deployContainer {
  height: 100vh;
  background: #f0f2f5;
  
  .sider {
    background: #fff;
    border-right: 1px solid #f0f0f0;
    height: 100vh;
    overflow-y: auto;
    padding: 16px 0;
    
    &::-webkit-scrollbar {
      width: 6px;
    }
    
    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }
    
    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;
    }
    
    &::-webkit-scrollbar-thumb:hover {
      background: #a8a8a8;
    }
  }
  
  .content {
    background: #fff;
    padding: 24px;
    margin: 0;
    height: 100vh;
    overflow-y: auto;
    
    &::-webkit-scrollbar {
      width: 6px;
    }
    
    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }
    
    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;
    }
    
    &::-webkit-scrollbar-thumb:hover {
      background: #a8a8a8;
    }
  }
  
  .deploySider {
    background: #fff;
    border-left: 1px solid #f0f0f0;
    height: 100vh;
    overflow-y: auto;
    
    &::-webkit-scrollbar {
      width: 6px;
    }
    
    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }
    
    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;
    }
    
    &::-webkit-scrollbar-thumb:hover {
      background: #a8a8a8;
    }
  }
  
  .appCard,
  .groupCard,
  .deployCard {
    margin: 0;
    border-radius: 0;
    box-shadow: none;
    
    .ant-card-head {
      border-bottom: 1px solid #f0f0f0;
      min-height: 56px;
      padding: 0 16px;
      
      .ant-card-head-title {
        font-size: 16px;
        font-weight: 500;
        padding: 16px 0;
      }
    }
    
    .ant-card-body {
      padding: 0;
    }
  }
  
  .appCardTitle,
  .groupCardTitle,
  .deployCardTitle {
    font-size: 16px;
    font-weight: 500;
    color: #262626;
    margin-bottom: 16px;
    padding: 16px 16px 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  
  .appCardContent,
  .groupCardContent,
  .deployCardContent {
    padding: 0 16px 16px;
  }
  
  .groupItem {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    cursor: pointer;
    border-bottom: 1px solid #f5f5f5;
    transition: background-color 0.2s;
    
    &:hover {
      background-color: #f5f5f5;
    }
    
    &.selected {
      background-color: #e6f7ff;
      border-left: 3px solid #1890ff;
      margin-left: -16px;
      padding-left: 13px;
    }
    
    .groupIcon {
      margin-right: 12px;
      font-size: 16px;
      color: #1890ff;
    }
    
    .groupContent {
      flex: 1;
      
      .groupTitle {
        font-size: 14px;
        font-weight: 500;
        color: #262626;
        margin-bottom: 4px;
      }
      
      .groupDescription {
        font-size: 12px;
        color: #8c8c8c;
        margin-bottom: 8px;
      }
    }
    
    .groupTags {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      gap: 4px;
    }
  }
  
  .deploymentTag {
    font-size: 11px;
    padding: 2px 6px;
    height: auto;
    line-height: 1.2;
    border-radius: 4px;
    
    &.mainTag {
      background-color: #f6ffed;
      border-color: #b7eb8f;
      color: #389e0d;
    }
    
    &.versionTag {
      background-color: #e6f7ff;
      border-color: #91d5ff;
      color: #096dd9;
    }
  }
  
  .statusTag {
    font-size: 11px;
    padding: 2px 6px;
    height: auto;
    line-height: 1.2;
    border-radius: 4px;
    
    &.inherit {
      background-color: #f5f5f5;
      border-color: #d9d9d9;
      color: #8c8c8c;
    }
    
    &.custom {
      background-color: #fff7e6;
      border-color: #ffd666;
      color: #d48806;
    }
  }
  
  .configSection {
    margin-bottom: 24px;
    
    .sectionTitle {
      font-size: 16px;
      font-weight: 500;
      color: #262626;
      margin-bottom: 16px;
      display: flex;
      align-items: center;
      gap: 8px;
    }
    
    .sectionContent {
      background: #fafafa;
      border: 1px solid #f0f0f0;
      border-radius: 6px;
      padding: 16px;
    }
  }
  
  .configItem {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f5f5f5;
    
    &:last-child {
      border-bottom: none;
    }
    
    .configLabel {
      font-size: 14px;
      color: #595959;
      font-weight: 500;
    }
    
    .configValue {
      font-size: 14px;
      color: #262626;
      font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Consolas', monospace;
    }
  }
  
  .deployProgress {
    .progressItem {
      margin-bottom: 12px;
      
      .progressTitle {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
        
        span {
          font-size: 12px;
          color: #595959;
          font-weight: 500;
        }
      }
    }
  }
  
  .deployActions {
    display: flex;
    gap: 12px;
    margin-top: 24px;
    
    .ant-btn {
      flex: 1;
      height: 40px;
      font-size: 14px;
      font-weight: 500;
    }
  }
  
  .environmentSelector {
    margin-bottom: 16px;
    
    .ant-select {
      width: 100%;
    }
  }
  
  .createGroupBtn {
    width: 100%;
    margin-bottom: 16px;
    height: 40px;
    font-size: 14px;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
  }
  
  .tabContent {
    padding: 16px 0;
  }
  
  .podList {
    .podItem {
      background: #fafafa;
      border: 1px solid #f0f0f0;
      border-radius: 6px;
      padding: 16px;
      margin-bottom: 12px;
      transition: all 0.2s;
      
      &:hover {
        border-color: #d9d9d9;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }
      
      .podHeader {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;
        
        .podName {
          font-size: 14px;
          font-weight: 500;
          color: #262626;
        }
        
        .podStatus {
          font-size: 12px;
          padding: 2px 8px;
          border-radius: 4px;
          
          &.running {
            background-color: #f6ffed;
            color: #389e0d;
            border: 1px solid #b7eb8f;
          }
          
          &.pending {
            background-color: #fff7e6;
            color: #d48806;
            border: 1px solid #ffd666;
          }
          
          &.failed {
            background-color: #fff1f0;
            color: #cf1322;
            border: 1px solid #ffa39e;
          }
        }
      }
      
      .podInfo {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 8px;
        margin-bottom: 12px;
        
        .podInfoItem {
          font-size: 12px;
          color: #8c8c8c;
          
          .label {
            margin-right: 8px;
          }
          
          .value {
            color: #262626;
            font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Consolas', monospace;
          }
        }
      }
      
      .podActions {
        display: flex;
        gap: 8px;
        flex-wrap: wrap;
        
        .ant-btn {
          height: 28px;
          font-size: 12px;
          padding: 0 8px;
        }
      }
    }
  }
  
  .logViewer {
    background: #1e1e1e;
    color: #d4d4d4;
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Consolas', monospace;
    font-size: 13px;
    padding: 16px;
    border-radius: 6px;
    min-height: 400px;
    max-height: 600px;
    overflow-y: auto;
    
    &::-webkit-scrollbar {
      width: 8px;
    }
    
    &::-webkit-scrollbar-track {
      background: #2d2d2d;
      border-radius: 4px;
    }
    
    &::-webkit-scrollbar-thumb {
      background: #555;
      border-radius: 4px;
    }
    
    &::-webkit-scrollbar-thumb:hover {
      background: #666;
    }
    
    .logLine {
      margin-bottom: 4px;
      padding: 2px 0;
      word-break: break-all;
      
      &.info {
        color: #d4d4d4;
      }
      
      &.warning {
        color: #d19a66;
      }
      
      &.error {
        color: #e06c75;
      }
      
      &.success {
        color: #98c379;
      }
      
      .timestamp {
        color: #61dafb;
        margin-right: 8px;
        font-size: 11px;
      }
    }
  }
  
  .historyCard {
    margin-bottom: 16px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    transition: all 0.2s;
    
    &:hover {
      border-color: #d9d9d9;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
    
    .historyHeader {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;
      
      .historyVersion {
        font-size: 16px;
        font-weight: 600;
        color: #262626;
      }
      
      .historyTime {
        font-size: 12px;
        color: #8c8c8c;
      }
    }
    
    .historyInfo {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 12px;
      margin-bottom: 12px;
      
      .historyInfoItem {
        font-size: 12px;
        
        .label {
          color: #8c8c8c;
          margin-right: 8px;
        }
        
        .value {
          color: #262626;
          font-weight: 500;
        }
      }
    }
    
    .historyDescription {
      font-size: 14px;
      color: #595959;
      margin-bottom: 16px;
      line-height: 1.5;
    }
    
    .historyActions {
      display: flex;
      gap: 8px;
      
      .ant-btn {
        height: 32px;
        font-size: 12px;
        padding: 0 12px;
      }
    }
  }
}

// 修复一些全局样式
.ant-modal {
  .ant-modal-content {
    border-radius: 8px;
  }
  
  .ant-modal-header {
    border-radius: 8px 8px 0 0;
    border-bottom: 1px solid #f0f0f0;
  }
  
  .ant-modal-body {
    padding: 24px;
  }
}

.ant-form-item {
  margin-bottom: 16px;
  
  .ant-form-item-label {
    padding-bottom: 4px;
    
    label {
      font-weight: 500;
      color: #262626;
    }
  }
}

.ant-tabs {
  .ant-tabs-tab {
    font-weight: 500;
    
    &.ant-tabs-tab-active {
      font-weight: 600;
    }
  }
}

.ant-collapse {
  .ant-collapse-header {
    font-weight: 500;
    color: #262626;
  }
}

.ant-table {
  .ant-table-thead > tr > th {
    background: #fafafa;
    font-weight: 600;
    color: #262626;
  }
}

.ant-select-dropdown {
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  
  .ant-select-item {
    padding: 8px 12px;
    
    &:hover {
      background-color: #f5f5f5;
    }
    
    &.ant-select-item-option-selected {
      background-color: #e6f7ff;
      color: #1890ff;
      font-weight: 500;
    }
  }
}

// 在顶层添加独立的选择器，具有更高的优先级
:global(.ant-tree .ant-tree-switcher) {
  margin-right: -10px !important;
  position: relative !important;
  z-index: 2 !important;
  width: 24px !important;
  height: 24px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  transition: all 0.3s !important;
  
  &:hover {
    transform: scale(1.08) !important;
    
    // 悬停在矩形按钮上的效果
    > div {
      border-color: #1890ff !important;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1) !important;
      background-color: #f0f7ff !important;
    }
  }
  
  // 矩形按钮框样式
  .tree-switcher-box {
    display: inline-flex !important;
    justify-content: center !important;
    align-items: center !important;
    width: 14px !important;
    height: 14px !important;
    border: 1px solid #d9d9d9 !important;
    border-radius: 2px !important;
    background-color: #fafafa !important;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05) !important;
    transition: all 0.3s !important;
  }
}

// 展开状态特殊样式
:global(.ant-tree .ant-tree-switcher_open) {
  .tree-switcher-box {
    border-color: #91d5ff !important;
    background-color: #e6f7ff !important;
  }
}

// 应用信息按钮样式
.appInfoButton {
  padding: 4px 8px;
  display: flex;
  align-items: center;
  border-radius: 4px;
  
  &:hover {
    background-color: #f0f5ff;
  }
}

// 应用信息内联显示样式
.appInfoInline {
  margin-left: 12px;
  padding: 0 8px;
  height: 32px;
  display: flex;
  align-items: center;
  background-color: #fafafa;
  border-radius: 4px;
  
  :global(.ant-tag) {
    margin-right: 4px;
    font-size: 12px;
    line-height: 18px;
    height: 20px;
  }
  
  :global(.ant-btn) {
    font-size: 14px;
    height: 22px;
    width: 22px;
    min-width: 22px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

// 应用操作按钮样式
.appActionButtons {
  margin-left: 16px;
  display: flex;
  align-items: center;
  padding: 2px 8px;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;

  
  :global(.ant-btn) {
    margin-right: 8px;
    
    &:last-child {
      margin-right: 0;
    }
  }
}
