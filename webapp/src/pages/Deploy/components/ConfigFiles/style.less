/* 配置文件组件样式 */

/* 容器样式 */
.config-files-container {
  background-color: white;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
  overflow: hidden;
}

.config-files-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  border-bottom: 1px solid #f0f0f0;
  background-color: #fafafa;
}

.config-files-title {
  font-weight: 500;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
}

.config-files-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding: 8px;
  max-height: 400px;
  overflow-y: auto;
}

/* 配置文件项样式 */
.config-file-item {
  margin-bottom: 4px;
  background-color: white;
  border-radius: 6px;
  border: 1px solid #f0f0f0;
  overflow: hidden;
  transition: all 0.2s ease;
  
  &:hover {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
  }
}

.config-file-header {
  padding: 6px 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.config-file-content {
  display: flex;
  align-items: center;
  flex: 1;
  overflow: hidden;
  height: 100%;
}

.config-file-icon {
  min-width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  margin-right: 6px;
  font-size: 12px;
}

.config-file-info {
  flex: 1;
  overflow: hidden;
  display: flex;
  align-items: center;
  height: 100%;
}

.config-file-name {
  font-weight: 500;
  font-size: 13px;
  margin-right: 6px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 180px;
}

.config-file-path {
  font-size: 11px;
  color: rgba(0, 0, 0, 0.45);
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.config-file-tag {
  margin-right: 3px;
  font-size: 10px;
  padding: 0 4px;
  line-height: 16px;
  height: 16px;
}

.config-file-actions {
  display: flex;
  margin-left: 6px;
}

.config-file-action-button {
  padding: 2px 4px;
  height: 22px;
  min-width: 22px;
  margin-left: 2px;
}

/* 空状态 */
.config-files-empty {
  padding: 16px 0;
  text-align: center;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.45);
} 