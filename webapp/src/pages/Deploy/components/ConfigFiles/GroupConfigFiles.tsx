import React, { useMemo, useState } from 'react';
import { Button, Dropdown, List, Modal, Space, Tag, Tooltip, Typography } from 'antd';
import {
  CopyOutlined,
  DeleteOutlined,
  EditOutlined,
  EyeOutlined,
  FileTextOutlined,
  GlobalOutlined,
  HistoryOutlined,
  PlusOutlined,
  SyncOutlined
} from '@ant-design/icons';
import styles from '../ConfigArea/style.less';
import { ConfigEditor } from '@/components/ConfigEditor';
import useConfigFiles from '@/hooks/useConfigFiles';
import { GroupConfigFileView, GroupConfigFileViewType } from '@/types/config';
import useDeploy from '@/models/deploy';

const { Text } = Typography;


// 为不同类型的配置文件定义样式和图标
const fileTypeStyles = {
  properties: {
    color: 'green',
    backgroundColor: '#f6ffed',
    icon: <FileTextOutlined style={{ color: '#52c41a' }} />,
  },
  yaml: {
    color: 'geekblue',
    backgroundColor: '#f0f5ff',
    icon: <FileTextOutlined style={{ color: '#2f54eb' }} />,
  },
  json: {
    color: 'purple',
    backgroundColor: '#f9f0ff',
    icon: <FileTextOutlined style={{ color: '#722ed1' }} />,
  },
  xml: {
    color: 'magenta',
    backgroundColor: '#fff0f6',
    icon: <FileTextOutlined style={{ color: '#eb2f96' }} />,
  },
  default: {
    color: 'default',
    backgroundColor: '#f5f5f5',
    icon: <FileTextOutlined style={{ color: '#8c8c8c' }} />,
  },
};

const viewTypeStyles = {
  groupOnly: {
    color: 'orange',
    backgroundColor: '#fff7e6',
    label: '分组',
    icon: <GlobalOutlined style={{ color: '#fa8c16' }} />,
  },
  overridden: {
    color: 'volcano',
    backgroundColor: '#fff2e8',
    label: '覆盖',
    icon: <CopyOutlined style={{ color: '#fa541c' }} />,
  },
  inherited: {
    color: 'blue',
    backgroundColor: '#e6f7ff',
    label: '继承',
    icon: <GlobalOutlined style={{ color: '#1890ff' }} />,
  },
};

const GroupConfigFiles: React.FC = () => {
  const { deploy } = useDeploy();
  const {
    currentConfigFile,
    configViewMode,
    configModalVisible,
    configFiles,
    configFilesLoading,
    createNewConfigFile,
    getGroupConfigFileView,
    editConfigFile,
    closeConfigModal,
  } = useConfigFiles(deploy.appData);

  // 获取过滤后的配置文件
  const filteredConfigFiles = useMemo(() => {
    return deploy.groupId ? getGroupConfigFileView(deploy.groupId) : [];
  }, [deploy.groupId, getGroupConfigFileView]);

  // 保存配置文件
  const handleSaveConfigFile = async (file: API.ConfigFile) => {
    // 这里可以添加保存配置文件的逻辑
  };

  // 渲染Tab栏右侧的按钮组
  const renderExtraButtons = () => {
    return (
      <Space>
        {!configFilesLoading && configFiles.length > 0 && (
          <Text type="secondary" style={{ fontSize: '12px' }}>
            共 {configFiles.length} 个配置文件
          </Text>
        )}
        <Button
          size="small"
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => createNewConfigFile(deploy.groupId)}
        >
          新建
        </Button>
      </Space>
    );
  };

  // 渲染配置文件列表项
  const renderConfigFileItem = (file: GroupConfigFileView) => {
    const fileFormat = file.format || 'default';
    const viewType = file.viewType || 'groupOnly';
    
    // 获取文件类型对应的样式
    const fileStyle = fileTypeStyles[fileFormat as keyof typeof fileTypeStyles] || fileTypeStyles.default;
    const viewTypeStyle = viewTypeStyles[viewType as keyof typeof viewTypeStyles];
    
    // 根据文件类型选择操作按钮样式
    const operationStyle = {
      inherited: { color: '#1890ff', hover: '#40a9ff' },
      overridden: { color: '#fa541c', hover: '#ff7a45' },
      groupOnly: { color: '#fa8c16', hover: '#ffa940' },
    }[viewType as keyof typeof viewTypeStyles] || { color: '#1890ff', hover: '#40a9ff' };
    
    return (
      <div className={styles.configFileItem}>
        <div className={styles.configFileHeader}>
          <div className={styles.configFileContent}>
            <div className={styles.configFileIcon} style={{ backgroundColor: fileStyle.backgroundColor }}>
              {fileStyle.icon}
            </div>
            
            <div className={styles.configFileInfo}>
              <Text strong className={styles.configFileName}>
                {file.name}
              </Text>
              
              <Tag color={fileStyle.color} className={styles.configFileTag}>
                {fileFormat.toUpperCase()}
              </Tag>
              
              <Tag color={viewTypeStyle.color} className={styles.configFileTag}>
                {viewTypeStyle.label}
              </Tag>
              
              <Text type="secondary" className={styles.configFilePath}>
                {file.path}
              </Text>
            </div>
          </div>
          
          <div className={styles.configFileActions}>
            {/* 查看按钮 */}
            <Tooltip title="查看">
              <Button
                type="text"
                size="small"
                icon={<EyeOutlined style={{ fontSize: '12px' }} />}
                onClick={() => {}}
                className={styles.configFileActionButton}
                style={{ color: '#8c8c8c' }}
              />
            </Tooltip>
            
            {/* 编辑或覆盖按钮 */}
            {file.viewType === 'inherited' ? (
              <Tooltip title="覆盖此配置">
                <Button
                  type="text"
                  size="small"
                  icon={<CopyOutlined style={{ fontSize: '12px' }} />}
                  onClick={() => {
                    // 创建一个新的配置文件，覆盖继承的配置
                    const newFile = {
                      ...file,
                      id: 0, 
                      viewType: 'overridden' as GroupConfigFileViewType,
                      group_id: deploy.groupId,
                    };
                    editConfigFile(newFile);
                  }}
                  className={styles.configFileActionButton}
                  style={{ color: operationStyle.color }}
                />
              </Tooltip>
            ) : (
              <Tooltip title="编辑">
                <Button
                  type="text"
                  size="small"
                  icon={<EditOutlined style={{ fontSize: '12px' }} />}
                  onClick={() => editConfigFile(file)}
                  className={styles.configFileActionButton}
                  style={{ color: operationStyle.color }}
                />
              </Tooltip>
            )}
            
            {/* 更多操作下拉菜单 */}
            <Dropdown
              menu={{
                items: [
                  {
                    key: 'history',
                    label: '操作历史',
                    icon: <HistoryOutlined />,
                    onClick: () => {
                      Modal.info({
                        title: '操作历史',
                        content: (
                          <List
                            size="small"
                            dataSource={[
                              {
                                time: '2025-01-20 15:30:22',
                                user: '张三',
                                action: '编辑',
                              },
                              {
                                time: '2025-01-18 10:25:46',
                                user: '李四',
                                action: '创建',
                              },
                            ]}
                            renderItem={(item) => (
                              <List.Item>
                                <Text
                                  type="secondary"
                                  style={{ marginRight: '8px' }}
                                >
                                  {item.time}
                                </Text>
                                <Text>{item.user}</Text>
                                <Text style={{ marginLeft: '8px' }}>
                                  {item.action}
                                </Text>
                              </List.Item>
                            )}
                          />
                        ),
                        width: 500,
                      });
                    },
                  },
                  ...(file.viewType !== 'inherited' ? [
                      {
                        key: 'delete',
                        label: '删除',
                        icon: <DeleteOutlined />,
                        danger: true,
                        onClick: () => {
                          Modal.confirm({
                            title: '确认删除',
                            content: `确定要删除配置文件 "${file.name}" 吗？`,
                            okText: '删除',
                            okType: 'danger',
                            cancelText: '取消',
                            onOk: () => {
                              Modal.success({
                                content: `已删除配置文件 "${file.name}"`,
                              });
                            },
                          });
                        },
                      },
                    ] : []),
                ],
              }}
              placement="bottomRight"
            >
              <Tooltip title="更多操作">
                <Button
                  type="text"
                  size="small"
                  icon={<span style={{ fontSize: '10px' }}>•••</span>}
                  style={{ 
                    padding: '0 2px', 
                    height: '20px', 
                    minWidth: '20px',
                    color: '#8c8c8c'
                  }}
                />
              </Tooltip>
            </Dropdown>
          </div>
        </div>
      </div>
    );
  };

  return (
    <>
      <div className={styles.configWrapper}>
        <div>
          <div style={{ 
            display: 'flex', 
            justifyContent: 'space-between', 
            alignItems: 'center',
            marginBottom: '6px'
          }}>
            {renderExtraButtons()}
          </div>

          <div>
            {configFilesLoading && (
              <div className={styles.loadingState}>
                <SyncOutlined spin /> 加载配置文件中...
              </div>
            )}
            
            {!configFilesLoading && (
              <div>
                <div className={styles.configList}>
                  {filteredConfigFiles.map((file: GroupConfigFileView) => renderConfigFileItem(file))}
                </div>
                
                {filteredConfigFiles.length === 0 && (
                  <div className={styles.emptyState}>
                    <div className={styles.emptyStateText}>
                      暂无配置文件
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>

      <ConfigEditor 
        visible={configModalVisible}
        configFile={currentConfigFile as API.ConfigFile}
        viewMode={configViewMode}
        onCancel={closeConfigModal}
        onSave={handleSaveConfigFile}
      />
    </>
  );
};

export default GroupConfigFiles; 