import React, { useState } from 'react';
import { Button, Tag, Typography, Tooltip, Modal, message, Card } from 'antd';
import { EyeOutlined, EditOutlined, PlusOutlined, DeleteOutlined, FileTextOutlined } from '@ant-design/icons';
import './style.less';
import useConfigFiles from '@/hooks/useConfigFiles';
import { ConfigEditor } from '@/components/ConfigEditor';
import { useModel } from '@umijs/max';

const { Text, Title } = Typography;


// 为不同类型的配置文件定义样式和图标
const fileTypeStyles = {
  properties: {
    color: 'green',
    backgroundColor: '#f6ffed',
    icon: <FileTextOutlined style={{ color: '#52c41a' }} />,
  },
  yaml: {
    color: 'geekblue',
    backgroundColor: '#f0f5ff',
    icon: <FileTextOutlined style={{ color: '#2f54eb' }} />,
  },
  json: {
    color: 'purple',
    backgroundColor: '#f9f0ff',
    icon: <FileTextOutlined style={{ color: '#722ed1' }} />,
  },
  xml: {
    color: 'magenta',
    backgroundColor: '#fff0f6',
    icon: <FileTextOutlined style={{ color: '#eb2f96' }} />,
  },
  default: {
    color: 'default',
    backgroundColor: '#f5f5f5',
    icon: <FileTextOutlined style={{ color: '#8c8c8c' }} />,
  },
};

const ConfigFilesTooltip: React.FC = () => {
  const { deploy } = useModel('deploy');

  // 使用自定义Hook获取配置文件相关状态
  const {
    currentConfigFile,
    configViewMode,
    configModalVisible,
    viewConfigFile,
    editConfigFile,
    closeConfigModal,
    createNewConfigFile,
    createConfigFile,
    applicationConfigFiles,
  } = useConfigFiles(deploy.appData);

  // 保存配置文件
  const handleSaveConfigFile = async (file: API.ConfigFile) => {
    try {
      // 必填字段检查
      if (!file.name || !file.name.trim()) {
        message.error('文件名不能为空');
        return false;
      }


      
      if (!file.path || !file.path.trim()) {
        message.error('文件路径不能为空');
        return false;
      }
      
      if (!file.content_payload || !file.content_payload.content) {
        message.error('配置文件内容不能为空');
        return false;
      }

      // 设置appData相关信息
      if (file.id === 0) {
        if (!deploy.appData.application.id) {
          message.error('无法获取应用ID');
          return false;
        }
        
        file.app_id = deploy.appData.application.id;
        file.env_id = deploy.envId || 0;
      }


      const createConfigFileRequest: Config.CreateConfigFileRequest = {
        name: file.name,
        path: file.path,
        format: file.format,
        content: file.content_payload?.content,
        app_id: deploy.appData.application.id,
        env_id: deploy.envId || 0,
        group_id: deploy.groupId || 0,
      }


      // 保存配置文件
      return await createConfigFile(createConfigFileRequest);
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : '保存配置文件时发生错误';
      message.error(errorMsg);
      return false;
    }
  };

  // 查看配置文件
  const handleViewFile = (file: API.ConfigFile) => {
    viewConfigFile(file);
  };

  // 编辑配置文件
  const handleEditFile = (file: API.ConfigFile) => {
    editConfigFile(file);
  };

  const confirmDelete = (file: API.ConfigFile) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除配置文件 "${file.name}" 吗？`,
      okText: '删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: () => {
      },
    });
  };
  

  const renderConfigFileItem = (file: API.ConfigFile) => {
    const fileFormat = file.format || 'default';
    const fileStyle = fileTypeStyles[fileFormat as keyof typeof fileTypeStyles] || fileTypeStyles.default;
    
    return (
      <div className="config-file-item">
        <div className="config-file-header">
          <div className="config-file-content">
            <div className="config-file-icon" style={{ backgroundColor: fileStyle.backgroundColor }}>
              {fileStyle.icon}
            </div>
            
            <div className="config-file-info">
              <Text strong className="config-file-name">
                {file.name}
              </Text>
              
              <Tag color={fileStyle.color} className="config-file-tag">
                {fileFormat.toUpperCase()}
              </Tag>
              
              <Text type="secondary" className="config-file-path">
                {file.path}
              </Text>
            </div>
          </div>
          
          <div className="config-file-actions">
            <Tooltip title="查看">
              <Button
                type="text"
                size="small"
                icon={<EyeOutlined />}
                onClick={() => handleViewFile(file)}
                className="config-file-action-button"
              />
            </Tooltip>
            
            <Tooltip title="编辑">
              <Button
                type="text"
                size="small"
                icon={<EditOutlined />}
                onClick={() => handleEditFile(file)}
                className="config-file-action-button"
              />
            </Tooltip>
            
            <Tooltip title="删除">
              <Button
                type="text"
                size="small"
                danger
                icon={<DeleteOutlined />}
                onClick={() => confirmDelete(file)}
                className="config-file-action-button"
              />
            </Tooltip>
          </div>
        </div>
      </div>
    );
  };



  return (
    <Card 
      variant="borderless"
      className="config-files-card"
      style={{ backgroundColor: '#fcfcfc' }}
      styles={{ body: { padding: '0px' } }}
    >
      <div style={{ padding: '8px 12px', display: 'flex', justifyContent: 'space-between', alignItems: 'center', borderBottom: '1px solid #f0f0f0' }}>
        <Title level={5} style={{ margin: 0 }}>
          配置文件 ({applicationConfigFiles.length || 0})
        </Title>
        <Button 
          type="primary" 
          icon={<PlusOutlined />} 
          size="small"
          onClick={() => {
            createNewConfigFile(deploy.groupId || 0);
          }}
        >
          新建
        </Button>
      </div>
      
      <div className="config-files-list" style={{ padding: '8px' }}>
        {applicationConfigFiles.length > 0 ? (
          applicationConfigFiles.map(file => renderConfigFileItem(file))
        ) : (
          <div className="config-files-empty">暂无配置文件</div>
        )}
      </div>

      {/* 配置文件模态框 */}
      <ConfigEditor
        visible={configModalVisible}
        configFile={currentConfigFile as API.ConfigFile}
        viewMode={configViewMode}
        onCancel={closeConfigModal}
        onSave={handleSaveConfigFile}
      />
    </Card>
  );
};

export default ConfigFilesTooltip; 