import { FormProvider } from '@/contexts/FormContext';
import useDeployTask from '@/hooks/useDeployTask';
import useDeploy from '@/models/deploy';
import { DeploymentPlanItem, DeployMode } from '@/types/deploytask';
import eventBus, { EVENTS } from '@/utils/eventBus';
import {
  PlayCircleOutlined,
} from '@ant-design/icons';
import {
  Button,
  Card,
  Form,
  Input,
  message,
  Radio,
  Select,
} from 'antd';
import React, { useEffect, useState } from 'react';
import GroupSelector from './components/GroupSelector';
import RdmVerifier from './components/RdmVerifier';
import DeployOrderModal from './DeployOrderModal';
import styles from './style.less';
import { useModel } from 'umi';


const DeployOperation: React.FC = () => {
  const { deploy } = useModel('deploy');
  const [deployForm] = Form.useForm();
  
  // 渲染内容组件
  const DeployContent: React.FC = () => {
    const { addDeployTask, deployTaskOperationView, updateDeployTaskOperationView, selectAllGroup, clearAllGroup, invertAllGroup } = useDeployTask();
    
    // 部署状态
    const [submitting, setSubmitting] = useState<boolean>(false);
    // 部署顺序调整模态框相关状态
    const [orderModalVisible, setOrderModalVisible] = useState<boolean>(false);
    
    // 处理部署分组选择变化
    const handleDeployGroupsChange = (checkedValues: number[]) => {
      // 确保当前分组始终被选中
      if (!checkedValues.includes(deploy.groupId)) {
        checkedValues = [...checkedValues, deploy.groupId];
      }
      updateDeployTaskOperationView({
        selectedGroupIds: checkedValues,
      });
      // 同步更新表单值
      deployForm.setFieldsValue({ deployGroups: checkedValues });
    };
  
    // 确保当前分组始终被选中
    useEffect(() => {
      const currentGroups = deployForm.getFieldValue('deployGroups') || [];
      if (!currentGroups.includes(deploy.groupId)) {
        const updatedGroups = [...currentGroups, deploy.groupId];
        updateDeployTaskOperationView({
          selectedGroupIds: updatedGroups,
        });
        deployForm.setFieldsValue({ deployGroups: updatedGroups });
      }
    }, [deploy.groupId]);
    
    // 初始化RDM选项
    useEffect(() => {
      // 设置默认的isBranch为0（表示Tag）
      if (deployForm.getFieldValue('isBranch') === undefined) {
        deployForm.setFieldsValue({ isBranch: 0 });
        updateDeployTaskOperationView({ isBranch: false });
      }
    }, []);

    // 处理表单提交，创建部署任务
    const handleDeploySubmit = async (values: any) => {
      try {
        // 验证表单中必要的字段
        if (!values.semver && deployTaskOperationView.deployType !== 'rollback') {
          message.error('请选择或输入Tag/Branch');
          return;
        }

        // 获取选中的分组
        const selectedGroupIds = values.deployGroups || [];
        
        // 如果选择了多个分组，弹出部署顺序调整模态框
        if (selectedGroupIds.length > 1) {
          setOrderModalVisible(true);
          return;
        }
        
        // 只有一个分组，直接部署
        setSubmitting(true);
        
        // 确保deployTaskOperationView中包含表单中的所有值
        const formValues = deployForm.getFieldsValue();
        updateDeployTaskOperationView({
          semver: formValues.semver,
          deployType: formValues.deployType,
          description: formValues.description,
          selectedGroupIds: formValues.deployGroups,
          isBranch: formValues.isBranch === 1,
          commitId: formValues.commitId,
        });
        
        // 调用API创建部署任务
        const taskResponse = await addDeployTask();
        
        if (taskResponse) {
          message.success('部署请求已提交');
          
          // 发布部署任务创建成功事件，触发部署历史刷新
          eventBus.publish(EVENTS.DEPLOY_TASK_CREATED, taskResponse);
          
          // 如果是部署类型，清空semver字段，便于下次输入
          if (formValues.deployType === 'deploy') {
            deployForm.setFieldsValue({ semver: '' });
            updateDeployTaskOperationView({ semver: '' });
          }
        } else {
          message.error('部署请求失败，请稍后重试');
        }
      } catch (error) {
        console.error('部署失败:', error);
        message.error('部署请求失败，请稍后重试');
      } finally {
        setSubmitting(false);
      }
    };

    // 处理部署顺序确认
    const handleOrderConfirm = async (
      deploymentPlan: DeploymentPlanItem[],
      deployMode: DeployMode
    ) => {
      try {
        setSubmitting(true);
        
        // 获取所有部署分组的ID
        const allSelectedGroupIds = deploymentPlan.flatMap(batch => batch.groups);
        
        // 更新部署视图
        updateDeployTaskOperationView({
          selectedGroupIds: allSelectedGroupIds,
          semver: deployForm.getFieldValue('semver'),
          deployType: deployForm.getFieldValue('deployType'),
          description: deployForm.getFieldValue('description'),
          isBranch: deployForm.getFieldValue('isBranch') === 1,
          deploymentPlan, // 添加部署计划
          deployMode, // 设置部署模式
        });
        
        // 调用API创建部署任务
        const taskResponse = await addDeployTask();
        
        if (taskResponse) {
          message.success('部署请求已提交');
          
          // 发布部署任务创建成功事件，触发部署历史刷新
          eventBus.publish(EVENTS.DEPLOY_TASK_CREATED, taskResponse);
          
          // 关闭模态框
          setOrderModalVisible(false);
          
          // 如果是部署类型，清空semver字段，便于下次输入
          if (deployForm.getFieldValue('deployType') === 'deploy') {
            deployForm.setFieldsValue({ semver: '' });
            updateDeployTaskOperationView({ semver: '' });
          }
        } else {
          message.error('部署请求失败，请稍后重试');
        }
      } catch (error) {
        console.error('部署失败:', error);
        message.error('部署请求失败，请稍后重试');
      } finally {
        setSubmitting(false);
      }
    };

    return (
      <div className={styles.deployCardContent}>
        <Form
          form={deployForm}
          layout="vertical"
          initialValues={{
            deployType: deployTaskOperationView.deployType,
            deployGroups: [deploy.groupId ],
            semver: deployTaskOperationView.semver,
            isBranch: deployTaskOperationView.isBranch ? 1 : 0,
            description: deployTaskOperationView.description,
          }}
          onFinish={handleDeploySubmit}
          requiredMark={false}
        >
          {/* 部署方式选择 */}
          <div className={styles.formSection}>
            <Form.Item
              name="deployType"
              label="部署方式"
              rules={[{ required: true, message: '请选择部署方式' }]}
            >
              <Radio.Group
                onChange={(e) => updateDeployTaskOperationView({ deployType: e.target.value })}
                buttonStyle="solid"
                className={styles.deployTypeRadio}
              >
                <Radio.Button value="deploy">部署</Radio.Button>
                <Radio.Button value="rollback">回滚</Radio.Button>
              </Radio.Group>
            </Form.Item>
          </div>

          {/* 回滚版本选择 */}
          {deployTaskOperationView.deployType === 'rollback' && (
            <div className={styles.formSection}>
              <Form.Item
                name="rollbackVersion"
                label="回滚版本"
                rules={[{ required: true, message: '请选择回滚版本' }]}
              >
                <Select
                  placeholder="请选择要回滚的版本"
                  onChange={(value) => {
                    updateDeployTaskOperationView({
                      semver: value,
                    });
                  }}
                  popupClassName={styles.versionDropdown}
                >
                </Select>
              </Form.Item>
            </div>
          )}

          {/* 部署分组选择 */}
          <GroupSelector 
            deployTaskOperationView={deployTaskOperationView}
            handleDeployGroupsChange={handleDeployGroupsChange}
            selectAllGroup={selectAllGroup}
            clearAllGroup={clearAllGroup}
            invertAllGroup={invertAllGroup}
          />

          {/* RDM校验配置 - 仅在非回滚时显示 */}
          {deployTaskOperationView.deployType !== 'rollback' && (
            <RdmVerifier
              updateDeployTaskOperationView={updateDeployTaskOperationView}
              form={deployForm}
              projectId={deploy.appData.app_settings?.repo_project_id || 4573}
            />
          )}

          {/* 部署备注 */}
          <div className={styles.formSection}>
            <Form.Item
              name="description"
              label="部署说明"
              rules={[{ required: true, message: '请输入部署说明' }]}
            >
              <Input.TextArea
                placeholder="请输入本次部署的说明信息"
                rows={3}
                showCount
                maxLength={200}
                className={styles.commentTextarea}
                onChange={(e) => updateDeployTaskOperationView({
                  description: e.target.value
                })}
              />
            </Form.Item>
          </div>

          {/* 部署按钮 */}
          <Form.Item className={styles.deployBtnWrapper}>
            <Button
              type="primary"
              icon={<PlayCircleOutlined />}
              htmlType="submit"
              size="middle"
              className={styles.deployButton}
              loading={submitting}
            >
              {deployTaskOperationView.deployType === 'deploy' ? '部署' : '执行回滚'}
            </Button>
          </Form.Item>
        </Form>
        
        {/* 部署顺序调整模态框 */}
        <DeployOrderModal 
          visible={orderModalVisible}
          onCancel={() => setOrderModalVisible(false)}
          onConfirm={handleOrderConfirm}
          submitting={submitting}
          selectedGroupIds={deployTaskOperationView.selectedGroupIds || []}
          groups={deploy.appData.groups}
          currentGroupId={deploy.groupId}
        />
      </div>
    );
  };

  return (
    <Card className={`${styles.deployCard}`} variant="borderless">
      <div className={styles.deployCardTitle}>
        <span>部署操作</span>
      </div>
      <FormProvider form={deployForm}>
        <DeployContent />
      </FormProvider>
    </Card>
  );
};

export default DeployOperation; 