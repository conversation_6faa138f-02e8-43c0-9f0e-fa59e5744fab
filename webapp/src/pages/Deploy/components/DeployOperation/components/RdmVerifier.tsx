import React, { useState, useEffect, useRef } from 'react';
import { Form, Radio, AutoComplete, Tooltip } from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { getSemverList } from '@/services/api/SemverController';
import { Divider } from 'antd';
import styles from '../style.less';

interface RdmVerifierProps {
  updateDeployTaskOperationView: (params: any) => void;
  form: any;
  projectId: number;
}

const RdmVerifier: React.FC<RdmVerifierProps> = ({
  updateDeployTaskOperationView,
  form,
  projectId,
}) => {
  // Tag/Branch 选项
  const [rdmOptions, setRdmOptions] = useState<{ value: string }[]>([]);
  // 是否正在加载选项
  const [loadingOptions, setLoadingOptions] = useState<boolean>(false);
  // 当前搜索关键字
  const [searchKeyword, setSearchKeyword] = useState<string>('');
  // 搜索延迟定时器
  const searchTimerRef = useRef<NodeJS.Timeout | null>(null);

  // 高亮文本中与搜索关键字匹配的部分
  const highlightMatchText = (text: string, keyword: string) => {
    if (!keyword.trim() || !text) {
      return <span>{text}</span>;
    }
    
    try {
      // 忽略大小写，拆分文本
      const parts = text.split(new RegExp(`(${keyword})`, 'i'));
      return (
        <span>
          {parts.map((part, index) => 
            part.toLowerCase() === keyword.toLowerCase() ? (
              <span key={index} style={{ color: '#1890ff', fontWeight: 'bold', backgroundColor: 'rgba(24, 144, 255, 0.1)' }}>
                {part}
              </span>
            ) : (
              <span key={index}>{part}</span>
            )
          )}
        </span>
      );
    } catch (e) {
      // 如果正则表达式出错，直接返回原文本
      return <span>{text}</span>;
    }
  };

  // 获取Semver选项的函数
  const fetchSemverOptions = async (isBranch: boolean, searchKey: string = '') => {
    try {
      setLoadingOptions(true);
      
      if (!projectId) {
        setRdmOptions([]);
        setLoadingOptions(false);
        return;
      }
      
      // 调用API获取数据
      const response = await getSemverList(isBranch ? 1 : 0, projectId, searchKey, 1, 10);
      
      if (response && response.code === 0 && Array.isArray(response.data)) {
        // 将API返回的数据转换为AutoComplete所需的格式
        const options = response.data.map((item: API.GitLabBranch | API.GitLabTag) => ({
          value: item.name,
          label: (
            <div style={{ 
              padding: '8px', 
              borderRadius: '4px',
              cursor: 'pointer',
              transition: 'background-color 0.2s',
              borderBottom: '1px solid #f0f0f0',
            }}>
              <div style={{ 
                display: 'flex', 
                justifyContent: 'space-between',
                alignItems: 'center' 
              }}>
                <span style={{ 
                  fontWeight: 600,
                  fontSize: '13px',
                  color: searchKeyword ? '#333' : '#1890ff',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  maxWidth: '70%'
                }}>
                  {highlightMatchText(item.name, searchKeyword)}
                </span>
                <span style={{ 
                  color: '#666',
                  backgroundColor: '#f5f5f5',
                  padding: '2px 6px',
                  borderRadius: '4px',
                  fontSize: '12px',
                  fontFamily: 'monospace'
                }}>{item.commit?.id?.substring(0, 8) || ''}</span>
              </div>
              <div style={{ 
                display: 'flex', 
                justifyContent: 'space-between', 
                fontSize: '12px', 
                marginTop: '6px',
                alignItems: 'center'
              }}>
                <span style={{ 
                  color: '#666',
                  display: 'flex',
                  alignItems: 'center'
                }}>
                  <svg viewBox="64 64 896 896" width="12px" height="12px" fill="currentColor" style={{ marginRight: '4px' }}>
                    <path d="M858.5 763.6a374 374 0 00-80.6-119.5 375.63 375.63 0 00-119.5-80.6c-.4-.2-.8-.3-1.2-.5C719.5 518 760 444.7 760 362c0-137-111-248-248-248S264 225 264 362c0 82.7 40.5 156 102.8 201.1-.4.2-.8.3-1.2.5-44.8 18.9-85 46-119.5 80.6a375.63 375.63 0 00-80.6 119.5A371.7 371.7 0 00136 901.8a8 8 0 008 8.2h60c4.4 0 7.9-3.5 8-7.8 2-77.2 33-149.5 87.8-204.3 56.7-56.7 132-87.9 212.2-87.9s155.5 31.2 212.2 87.9C779 752.7 810 825 812 902.2c.1 4.4 3.6 7.8 8 7.8h60a8 8 0 008-8.2c-1-47.8-10.9-94.3-29.5-138.2z" />
                  </svg>
                  {item.commit?.author_name || ''}
                </span>
                <span style={{ 
                  color: '#888',
                  display: 'flex',
                  alignItems: 'center'
                }}>
                  <svg viewBox="64 64 896 896" width="12px" height="12px" fill="currentColor" style={{ marginRight: '4px' }}>
                    <path d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z" />
                    <path d="M686.7 638.6L544.1 535.5V288c0-4.4-3.6-8-8-8H488c-4.4 0-8 3.6-8 8v275.4c0 2.6 1.2 5 3.3 6.5l165.4 120.6c3.6 2.6 8.6 1.8 11.2-1.7l28.6-39c2.6-3.7 1.8-8.7-1.8-11.2z" />
                  </svg>
                  {item.commit?.committed_date ? new Date(item.commit.committed_date).toLocaleString('zh-CN', { 
                    year: 'numeric', 
                    month: '2-digit', 
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit'
                  }) : ''}
                </span>
              </div>
            </div>
          ),
          commit_id: item.commit?.id || '',
          author_name: item.commit?.author_name || '',
          committed_date: item.commit?.committed_date || '',
        }));
        
        setRdmOptions(options);
      } else {
        // 如果API返回错误，设置空数据
        setRdmOptions([]);
      }
    } catch (error) {
      console.error('加载版本列表失败:', error);
      setRdmOptions([]);
    } finally {
      setLoadingOptions(false);
    }
  };

  // 当isBranch变化时更新选项
  useEffect(() => {
    const isBranchValue = form.getFieldValue('isBranch');
    if (isBranchValue !== undefined) {
      // 清空现有选项
      setRdmOptions([]);
      
      // 清空semver字段值
      form.setFieldsValue({ semver: '' });
      
      // 根据类型加载不同的选项
      const isBranch = isBranchValue === 1;
      updateDeployTaskOperationView({ 
        isBranch,
        semver: '', // 清空部署视图中的semver值
        commitId: '' // 同时清空commitId
      });
    }
  }, [form.getFieldValue('isBranch')]);

  // 处理输入变化，增加延迟搜索
  const handleRdmInputChange = (value: string) => {
    // 获取当前选择的类型
    const isBranchValue = form.getFieldValue('isBranch');
    const isBranch = isBranchValue === 1;
    
    // 立即更新搜索关键字，确保高亮正确
    setSearchKeyword(value);
    
    // 更新部署视图中的semver值
    updateDeployTaskOperationView({
      semver: value,
    });
    
    // 如果已经有定时器在运行，先清除它
    if (searchTimerRef.current) {
      clearTimeout(searchTimerRef.current);
      searchTimerRef.current = null;
    }
    
    // 如果输入为空，直接清空选项
    if (!value.trim()) {
      setRdmOptions([]);
      return;
    }
    
    // 设置加载状态，提供用户反馈
    setLoadingOptions(true);
    
    // 延迟执行搜索，给用户更多输入时间
    searchTimerRef.current = setTimeout(() => {
      // 调用API获取匹配的选项
      fetchSemverOptions(isBranch, value);
    }, 100);
  };

  // 组件卸载时清除定时器
  useEffect(() => {
    return () => {
      if (searchTimerRef.current) {
        clearTimeout(searchTimerRef.current);
      }
    };
  }, []);

  return (
    <div className={styles.formSection}>
      <Divider className={styles.sectionDivider}>
        RDM校验
        <Tooltip title="根据Tag/Branch镜像存在情况自动判断部署方式">
          <QuestionCircleOutlined
            className={styles.helpIcon}
          />
        </Tooltip>
      </Divider>

      {/* 第一行：类型选择器 */}
      <Form.Item
        name="isBranch"
        style={{ marginBottom: '12px' }}
        rules={[{ required: true, message: '请选择类型' }]}
      >
        <Radio.Group 
          className={styles.rdmTypeRadio}
          onChange={(e) => {
            updateDeployTaskOperationView({
              isBranch: e.target.value === 1,
            });
          }}
        >
          <Radio.Button value={0}>Tag</Radio.Button>
          <Radio.Button value={1}>Branch</Radio.Button>
        </Radio.Group>
      </Form.Item>

      {/* 第二行：Tag/Branch输入 */}
      <Form.Item
        name="semver"
        style={{ marginBottom: '8px' }}
        rules={[{ required: true, message: '请输入或选择Tag/Branch' }]}
      >
        <AutoComplete
          placeholder="请输入或选择Tag/Branch"
          className={styles.rdmInput}
          options={rdmOptions}
          onSearch={handleRdmInputChange}
          onChange={(value, option: any) => {
            updateDeployTaskOperationView({
              semver: value,
              commitId: option?.commit_id || value,
            });
          }}
          notFoundContent={loadingOptions ? '加载中...' : '无匹配选项'}
          allowClear
          backfill
          autoFocus
          popupClassName={styles.rdmDropdown}
        >
        </AutoComplete>
      </Form.Item>
    </div>
  );
};

export default RdmVerifier; 