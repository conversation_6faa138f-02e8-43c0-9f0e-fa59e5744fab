import React from 'react';
import { Form, Select, Button, Space, Tag } from 'antd';
import styles from '../style.less';
import { useModel } from '@umijs/max';

const { Option } = Select;

interface GroupSelectorProps {
  deployTaskOperationView: any;
  handleDeployGroupsChange: (checkedValues: number[]) => void;
  selectAllGroup: () => void;
  clearAllGroup: () => void;
  invertAllGroup: () => void;
}

/**
 * 部署分组选择器组件
 */
const GroupSelector: React.FC<GroupSelectorProps> = ({
  deployTaskOperationView,
  handleDeployGroupsChange,
  selectAllGroup,
  clearAllGroup,
  invertAllGroup,
}) => {

  // 直接从 deploy model 获取分组数据
  const { deploy } = useModel('deploy');
  const groupList = deploy.groupList || [];
  const selectedGroupId = deploy.groupId;
  const loading = !deploy.appData; 


  return (
    <div className={styles.formSection}>
      <Form.Item
        name="deployGroups"
        label={
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', width: '100%' }}>
            <span>部署分组</span>
            <Space size={4} className={styles.groupActions}>
              <Button
                type="link"
                size="small"
                onClick={() => selectAllGroup()}
                className={styles.actionLink}
                disabled={loading || groupList.length === 0}
              >
                全选
              </Button>
              <span className={styles.divider}>|</span>
              <Button
                type="link"
                size="small"
                onClick={() => invertAllGroup()}
                className={styles.actionLink}
                disabled={loading || groupList.length === 0}
              >
                反选
              </Button>
              <span className={styles.divider}>|</span>
              <Button
                type="link"
                size="small"
                onClick={() => clearAllGroup()}
                className={styles.clearLink}
                disabled={loading}
              >
                清空
              </Button>
            </Space>
          </div>
        }
        rules={[{ required: true, message: '请选择部署分组' }]}
        initialValue={deployTaskOperationView.selectedGroupIds}
      >
        <Select
          mode="multiple"
          placeholder={groupList.length === 0 ? "暂无可选分组" : "请选择部署分组"}
          className={styles.groupSelect}
          onChange={(values) => handleDeployGroupsChange(values as number[])}
          optionLabelProp="label"
          maxTagCount={5}
          allowClear={false}
          showSearch
          optionFilterProp="label"
          filterOption={(input, option) => 
            (option?.label as string)?.toLowerCase().includes(input.toLowerCase())
          }
          menuItemSelectedIcon={null} // 不显示默认的选中图标，用自定义样式代替
          loading={loading}
          disabled={groupList.length === 0}
          notFoundContent={loading ? "加载中..." : "暂无分组数据"}
        >
          {groupList.map((group: API.Group) => (
            <Option 
              key={group.id} 
              value={group.id}
              label={group.name}
              disabled={group.id === selectedGroupId} // 当前分组禁用选择框，但始终保持选中
            >
              <div className={styles.groupOption}>
                <span>{group.name}</span>
                {group.id === selectedGroupId && (
                  <Tag
                    color="blue"
                    style={{ marginLeft: 4, fontSize: '10px', padding: '0 4px' }}
                  >
                    当前
                  </Tag>
                )}
              </div>
            </Option>
          ))}
        </Select>
      </Form.Item>
    </div>
  );
};

export default GroupSelector; 