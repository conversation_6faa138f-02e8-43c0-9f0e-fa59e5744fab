import React, { useState, useEffect } from 'react';
import {
  Button,
  message,
  Modal,
  Select,
  Tooltip,
  Empty,
  Badge,
} from 'antd';
import {
  InfoCircleOutlined,
  MenuOutlined,
  SettingOutlined,
  ClockCircleOutlined,
  ArrowRightOutlined,
  HolderOutlined,
} from '@ant-design/icons';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';
import styles from './DeployOrderModal.less';
import { DeploymentPlanItem, DeployMode } from '@/types/deploytask';

// 批次类型定义
export interface DeploymentBatch {
  id: string;
  groups: any[]; // 修改为任意类型数组
  capacity: number;
  isFirstBatch?: boolean;
}

interface DeployOrderModalProps {
  visible: boolean;
  onCancel: () => void;
  onConfirm: (deploymentPlan: DeploymentPlanItem[], deployMode: DeployMode) => Promise<void>;
  selectedGroupIds: number[];
  groups: API.Group[];
  submitting: boolean;
  currentGroupId: number;
}

const POOL_ID = 'group-pool';

// 获取分组策略颜色和名称 - 基于分组ID确保稳定性
const getGroupStrategy = (group: any) => {
  // 使用分组ID确保同一分组始终有相同的策略
  const strategyId = group.id % 4;
  
  switch(strategyId) {
    case 0:
      return { color: 'blue', name: '蓝绿' };
    case 1:
      return { color: 'green', name: '灰度' };
    case 2:
      return { color: 'orange', name: '金丝雀' };
    case 3:
      return { color: 'purple', name: '滚动' };
    default:
      return { color: 'blue', name: '蓝绿' };
  }
};

const DeployOrderModal: React.FC<DeployOrderModalProps> = ({
  visible,
  onCancel,
  onConfirm,
  selectedGroupIds,
  groups,
  submitting,
  currentGroupId
}) => {
  // 部署顺序调整模态框相关状态
  const [concurrency, setConcurrency] = useState<number>(2);
  const [batches, setBatches] = useState<{id: string; groups: any[]; capacity: number; isFirstBatch?: boolean}[]>([]);
  const [groupPool, setGroupPool] = useState<any[]>([]);
  
  // 计算需要的批次数量
  const calculateBatchCount = (groupCount: number, concurrencyValue: number): number => {
    // 如果没有分组，至少创建一个批次
    if (groupCount <= 0) return 1;
    
    // 第一个批次固定只能放1个分组，剩余的按并行度分配
    const remainingGroups = Math.max(0, groupCount - 1);
    
    // 至少需要1个批次（第一批次），加上剩余分组需要的批次数
    return 1 + (concurrencyValue <= 0 ? 0 : Math.ceil(remainingGroups / concurrencyValue));
  };

  // 初始化批次和分组池
  const initializeBatchesAndPool = (selectedGroups: API.Group[], concurrencyValue: number) => {
    // 先把所有选中的分组放入分组池
    setGroupPool([...selectedGroups]);
    
    // 计算需要的批次数量
    const batchCount = calculateBatchCount(selectedGroups.length, concurrencyValue);
    
    // 创建空批次 - 第一个批次容量固定为1
    const newBatches: DeploymentBatch[] = Array.from({ length: batchCount }, (_, index) => ({
      id: `batch-${index}`,
      groups: [],
      capacity: index === 0 ? 1 : concurrencyValue,
      isFirstBatch: index === 0
    }));
    
    setBatches(newBatches);
  };

  // 当模态框显示或并行度变化时，重新初始化
  useEffect(() => {
    if (visible) {
      // 确保 groups 不为空
      const safeGroups = groups || ([] as API.Group[]);
      const selectedGroups = safeGroups.filter(group => selectedGroupIds.includes(group.id));
      initializeBatchesAndPool(selectedGroups, concurrency);
    }
  }, [visible, concurrency, selectedGroupIds, groups]);

  // 当并行度改变时重新计算批次
  const handleConcurrencyChange = (value: number) => {
    setConcurrency(value);
    
    // 获取所有分组（批次中的+池中的）
    const allGroups: API.Group[] = [
      ...groupPool,
      ...batches.flatMap(batch => batch.groups)
    ];
    
    // 重新初始化
    initializeBatchesAndPool(allGroups, value);
  };

  // 处理拖拽结束事件
  const handleDragEnd = (result: any) => {
    const { source, destination } = result;
    
    // 如果没有目标位置，不做处理
    if (!destination) return;
    
    // 如果拖拽到相同位置，不做处理
    if (source.droppableId === destination.droppableId && source.index === destination.index) {
      return;
    }

    // 处理从分组池到批次的拖拽
    if (source.droppableId === POOL_ID) {
      // 从池中移动到批次
      const updatedPool = [...groupPool];
      const [movedGroup] = updatedPool.splice(source.index, 1);
      setGroupPool(updatedPool);
      
      // 添加到目标批次
      const updatedBatches = [...batches];
      const batchIndex = parseInt(destination.droppableId.split('-')[1]);
      
      // 检查目标批次是否已满
      if (updatedBatches[batchIndex].groups.length >= updatedBatches[batchIndex].capacity) {
        // 批次已满，放回池中
        setGroupPool([...updatedPool, movedGroup]);
        message.warning('批次已达到并行度上限，请选择其他批次');
        return;
      }
      
      updatedBatches[batchIndex].groups.splice(destination.index, 0, movedGroup);
      setBatches(updatedBatches);
    }
    // 处理从批次到分组池的拖拽
    else if (destination.droppableId === POOL_ID) {
      // 从批次移动到池
      const sourceBatchIndex = parseInt(source.droppableId.split('-')[1]);
      const updatedBatches = [...batches];
      const [movedGroup] = updatedBatches[sourceBatchIndex].groups.splice(source.index, 1);
      setBatches(updatedBatches);
      
      // 添加到分组池
      const updatedPool = [...groupPool];
      updatedPool.splice(destination.index, 0, movedGroup);
      setGroupPool(updatedPool);
    }
    // 处理批次之间的拖拽
    else {
      const sourceBatchIndex = parseInt(source.droppableId.split('-')[1]);
      const destBatchIndex = parseInt(destination.droppableId.split('-')[1]);
      
      const updatedBatches = [...batches];
      const [movedGroup] = updatedBatches[sourceBatchIndex].groups.splice(source.index, 1);
      
      // 检查目标批次是否已满
      if (updatedBatches[destBatchIndex].groups.length >= updatedBatches[destBatchIndex].capacity) {
        // 批次已满，放回原批次
        updatedBatches[sourceBatchIndex].groups.splice(source.index, 0, movedGroup);
        setBatches(updatedBatches);
        message.warning('目标批次已达到并行度上限');
        return;
      }
      
      // 添加到目标批次
      updatedBatches[destBatchIndex].groups.splice(destination.index, 0, movedGroup);
      setBatches(updatedBatches);
    }
  };

  // 处理确认
  const handleConfirm = async () => {
    try {
      // 检查是否所有分组都已分配
      if (groupPool.length > 0) {
        message.warning('仍有分组未分配到批次中，请将所有分组拖入批次');
        return;
      }
      
      // 过滤掉空批次
      const nonEmptyBatches = batches.filter(batch => batch.groups.length > 0);
      
      // 构造部署计划
      const deploymentPlan = nonEmptyBatches.map((batch, index) => ({
        batchId: index + 1,
        groups: batch.groups.map(group => group.id)
      }));
      
      // 调用父组件提供的确认函数
      await onConfirm(
        deploymentPlan,
        concurrency === 1 ? 'sequential' : 'parallel'
      );
    } catch (error) {
      console.error('部署失败:', error);
      message.error('部署请求失败，请稍后重试');
    }
  };

  // 自动分配分组到批次
  const handleAutoAssign = () => {
    // 收集所有分组
    const allGroups: API.Group[] = [
      ...groupPool,
      ...batches.flatMap(batch => batch.groups)
    ];
    
    // 清空当前批次和分组池
    const updatedBatches = batches.map(batch => ({
      ...batch,
      groups: [] as any[]
    }));
    
    // 如果有分组，先填充第一个批次（容量为1）
    if (allGroups.length > 0) {
      updatedBatches[0].groups = [allGroups[0]];
    }
    
    // 从第二个批次开始填充剩余分组
    let currentBatchIndex = 1; // 从第二个批次开始
    let currentBatchGroupCount = 0;
    
    // 从第二个分组开始（如果有的话）
    for (let i = 1; i < allGroups.length; i++) {
      // 如果当前批次已满或未定义，移到下一个批次
      if (currentBatchIndex >= updatedBatches.length || 
          currentBatchGroupCount >= updatedBatches[currentBatchIndex].capacity) {
        currentBatchIndex++;
        currentBatchGroupCount = 0;
      }
      
      // 如果没有更多批次可用，添加到分组池
      if (currentBatchIndex >= updatedBatches.length) {
        const newGroupPool = [...groupPool];
        newGroupPool.push(allGroups[i]);
        setGroupPool(newGroupPool);
        continue;
      }
      
      // 添加到当前批次
      updatedBatches[currentBatchIndex].groups.push(allGroups[i]);
      currentBatchGroupCount++;
    }
    
    // 更新状态
    setBatches(updatedBatches);
    // 清空分组池 - 如果有未能分配的分组，上面的逻辑已处理
    setGroupPool(allGroups.length > updatedBatches.reduce((sum, batch) => sum + batch.groups.length, 0) 
      ? [...groupPool] : []);
  };

  // 清空所有批次，将分组移回池
  const handleClearAll = () => {
    // 收集所有批次中的分组
    const allGroups: API.Group[] = batches.flatMap(batch => batch.groups);
    
    // 清空批次
    const updatedBatches = batches.map(batch => ({
      ...batch,
      groups: [] as any[]
    }));
    
    // 更新状态
    setBatches(updatedBatches);
    setGroupPool([...groupPool, ...allGroups]);
  };


  // 创建统一的渲染分组项函数
  const renderGroupItem = (group: any, provided: any, snapshot: any, showDragIcon: boolean = true) => {
    const strategy = getGroupStrategy(group);
    return (
      <Tooltip title={`${group.name} (${strategy.name}部署)`} placement="top">
        <div
          ref={provided.innerRef}
          {...provided.draggableProps}
          {...provided.dragHandleProps}
          className={`${styles.groupItem} ${snapshot.isDragging ? styles.isDragging : ''} ${group.id === currentGroupId ? styles.currentGroup : ''}`}
          style={{
            ...provided.draggableProps.style,
            margin: '2px 4px',
            width: 'auto',
            maxWidth: '110px',
          }}
        >
          <div className={styles.groupLeft}>
            <span className={styles.groupName}>{group.name}</span>
            <span className={`${styles.strategyTag} ${styles[strategy.color]}`}>{strategy.name}</span>
          </div>
          {group.id === currentGroupId && (
            <span className={styles.currentTag}>当前</span>
          )}
          {showDragIcon && <HolderOutlined className={styles.groupDragIcon} />}
        </div>
      </Tooltip>
    );
  };

  return (
    <Modal
      title="调整部署顺序"
      open={visible}
      onCancel={onCancel}
      onOk={handleConfirm}
      confirmLoading={submitting}
      width={800}
      maskClosable={false}
      destroyOnClose
      className={styles.orderModal}
      okText="确定部署"
      cancelText="取消"
    >
      <div className={styles.modalContent}>
        {/* 并行设置 */}
        <div className={styles.parallelSettings}>
          <div className={styles.concurrencyControl}>
            <span>并行度:</span>
            <Select 
              value={concurrency}
              onChange={handleConcurrencyChange}
              options={[
                { value: 1, label: '1 (顺序)' },
                { value: 2, label: '2' },
                { value: 3, label: '3' },
                { value: 4, label: '4' },
                { value: 5, label: '5' },
              ]}
              style={{ width: 100 }}
              size="small"
            />
            <span className={styles.helpText}>每批最多同时部署的分组数</span>
            
            <div style={{ flex: 1 }} />
            
            <Button 
              type="primary" 
              onClick={handleAutoAssign}
              icon={<SettingOutlined />} 
              size="small"
            >
              自动分配
            </Button>
            
            <Button 
              onClick={handleClearAll} 
              size="small"
            >
              清空
            </Button>
          </div>
        </div>

        <div className={styles.deployContainer}>
          <DragDropContext onDragEnd={handleDragEnd}>
            {/* 分组池区域 */}
            <div style={{ marginBottom: '6px', display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <div>待分配分组</div>
              <div style={{ fontSize: '12px', color: '#999' }}>共 {groupPool.length} 个</div>
            </div>

            {/* 分组池 */}
            <Droppable droppableId={POOL_ID} direction="horizontal" isDropDisabled={false}>
              {(provided, snapshot) => (
                <div
                  ref={provided.innerRef}
                  {...provided.droppableProps}
                  className={`${styles.groupPoolContainer} ${snapshot.isDraggingOver ? styles.isDraggingOver : ''}`}
                >
                  {groupPool.length > 0 ? (
                    groupPool.map((group, index) => (
                      <Draggable
                        key={`pool-group-${group.id}`}
                        draggableId={`pool-group-${group.id}`}
                        index={index}
                      >
                        {(provided, snapshot) => renderGroupItem(group, provided, snapshot, false)}
                      </Draggable>
                    ))
                  ) : (
                    <div style={{ width: '100%', color: '#bbb', fontSize: '12px', textAlign: 'center', padding: '10px' }}>
                      所有分组已分配到批次
                    </div>
                  )}
                  {provided.placeholder}
                </div>
              )}
            </Droppable>

            {/* 批次列表 */}
            <div className={styles.batchesSection}>
              <div style={{ marginBottom: '6px', display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <div>部署批次</div>
              </div>
              <div className={styles.batchesContainer}>
                {/* 将批次分组为每行最多4个，并按照蛇形布局排列 */}
                {(() => {
                  // 计算需要多少行
                  const rowCount = Math.ceil(batches.length / 4);
                  const rows = [];
                  
                  for (let rowIndex = 0; rowIndex < rowCount; rowIndex++) {
                    // 确定当前行的批次顺序（奇数行从右到左，偶数行从左到右）
                    const isReverseRow = rowIndex % 2 === 1;
                    const startIdx = rowIndex * 4;
                    const endIdx = Math.min(startIdx + 4, batches.length);
                    let rowBatches = batches.slice(startIdx, endIdx);
                    
                    // 如果是奇数行（从0开始计数，所以实际是第二行），反转批次顺序
                    if (isReverseRow) {
                      rowBatches = [...rowBatches].reverse();
                    }
                    
                    rows.push(
                      <div key={`batch-row-${rowIndex}`} className={styles.batchRow} style={{ 
                        display: 'flex', 
                        flexDirection: 'row',
                        justifyContent: 'flex-start',
                        marginBottom: '12px',
                        flexWrap: 'nowrap'
                      }}>
                        {rowBatches.map((batch, batchIndexInRow) => {
                          // 获取批次在原始数组中的实际索引
                          const actualBatchIndex = isReverseRow 
                            ? endIdx - 1 - batchIndexInRow
                            : startIdx + batchIndexInRow;
                            
                          // 确定是否需要显示箭头以及箭头方向
                          const showArrow = actualBatchIndex > 0;
                          // 在同一行内，第一个批次不显示箭头
                          const isFirstInRow = batchIndexInRow === 0;
                          // 根据行的方向确定箭头方向
                          const arrowDirection = isReverseRow ? 'left' : 'right';
                          
                          return (
                            <React.Fragment key={`batch-fragment-${actualBatchIndex}`}>
                              {showArrow && !isFirstInRow && (
                                <div className={styles.batchArrow}>
                                  {arrowDirection === 'right' ? (
                                    <ArrowRightOutlined className={styles.arrowIcon} />
                                  ) : (
                                    <ArrowRightOutlined className={styles.arrowIcon} style={{ transform: 'rotate(180deg)' }} />
                                  )}
                                </div>
                              )}
                              <div key={`batch-${actualBatchIndex}`} className={`${styles.batchWrapper} ${actualBatchIndex === 0 ? styles.firstBatch : ''}`}>
                                {/* 批次标题 */}
                                <div style={{ 
                                  padding: '4px 8px', 
                                  background: actualBatchIndex === 0 ? '#e6f7ff' : '#f5f5f5', 
                                  borderBottom: '1px solid #e6e6e6',
                                  fontSize: '12px',
                                  fontWeight: 'bold',
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                  color: actualBatchIndex === 0 ? '#1890ff' : 'inherit',
                                }}>
                                  {actualBatchIndex === 0 ? (
                                    <><Badge status="processing" /> 第一批</>
                                  ) : (
                                    `第${actualBatchIndex + 1}批`
                                  )}
                                </div>

                                {/* 批次内的分组容器 */}
                                <Droppable 
                                  droppableId={`batch-${actualBatchIndex}`} 
                                  isDropDisabled={actualBatchIndex === 0 && batch.groups.length >= 1}
                                >
                                  {(provided, snapshot) => (
                                    <div
                                      ref={provided.innerRef}
                                      {...provided.droppableProps}
                                      className={`${styles.groupsContainer} ${
                                        snapshot.isDraggingOver ? styles.isDraggingOver : ''
                                      } ${batch.groups.length === 0 ? styles.isEmpty : ''}`}
                                    >
                                      {batch.groups.length === 0 ? (
                                        <div style={{ color: '#bbb', fontSize: '12px', textAlign: 'center' }}>
                                          {actualBatchIndex === 0 ? '拖入第一个分组（限1个）' : '拖入分组'}
                                        </div>
                                      ) : (
                                        batch.groups.map((group, groupIndex) => (
                                          <Draggable
                                            key={`batch-group-${group.id}`}
                                            draggableId={`batch-group-${group.id}`}
                                            index={groupIndex}
                                          >
                                            {(provided, snapshot) => renderGroupItem(group, provided, snapshot)}
                                          </Draggable>
                                        ))
                                      )}
                                      {provided.placeholder}
                                    </div>
                                  )}
                                </Droppable>
                              </div>
                            </React.Fragment>
                          );
                        })}
                      </div>
                    );
                    
                    // 如果不是最后一行，添加行间连接箭头
                    if (rowIndex < rowCount - 1) {
                      const nextRowIsReverse = (rowIndex + 1) % 2 === 1;
                      // 确定当前行的最后一个批次和下一行的第一个批次
                      const currentRowLastBatchIdx = isReverseRow ? startIdx : Math.min(startIdx + 3, batches.length - 1);
                      const nextRowFirstBatchIdx = (rowIndex + 1) * 4;
                      
                      // 只有当两行都有批次时才显示连接箭头
                      if (nextRowFirstBatchIdx < batches.length) {
                        rows.push(
                          <div key={`row-connector-${rowIndex}`} className={styles.rowConnector}>
                            <div className={styles.connectorLine}>
                              <div className={styles.connectorDot}></div>
                              <div className={styles.connectorArrow}>
                                {nextRowIsReverse ? (
                                  <ArrowRightOutlined className={styles.arrowIcon} style={{ transform: 'rotate(90deg)' }} />
                                ) : (
                                  <ArrowRightOutlined className={styles.arrowIcon} style={{ transform: 'rotate(90deg)' }} />
                                )}
                              </div>
                            </div>
                          </div>
                        );
                      }
                    }
                  }
                  
                  return rows;
                })()}
              </div>
            </div>
          </DragDropContext>
        </div>

        {/* 部署流程说明 */}
        <div className={styles.flowInfo}>
          <InfoCircleOutlined /> 按照批次从左到右依次部署，第一批次固定只能部署一个分组
        </div>
      </div>
    </Modal>
  );
};

export default DeployOrderModal; 