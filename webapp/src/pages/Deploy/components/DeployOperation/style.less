/* 部署操作组件样式 */
.deployCard {
  height: 100%;
  display: flex;
  flex-direction: column;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
  border-radius: 6px;
  transition: all 0.3s;
  
  &:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.12);
  }
  
  :global {
    .ant-card-body {
      padding: 12px 16px;
    }
  }
}

.deployCardTitle {
  font-weight: 500;
  font-size: 16px;
  margin-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 8px;
  color: #262626;
  display: flex;
  align-items: center;
  
  &::before {
    content: '';
    display: inline-block;
    width: 3px;
    height: 16px;
    background: #1890ff;
    margin-right: 8px;
    border-radius: 2px;
  }
}

.deployCardContent {
  flex: 1;
  overflow-y: auto;
  padding: 0 2px;
  
  :global {
    .ant-form-item-label > label {
      font-weight: 500;
      color: #262626;
      font-size: 13px;
    }
    
    .ant-divider {
      color: #595959;
      font-weight: 500;
      font-size: 14px;
    }
    
    .ant-radio-button-wrapper {
      transition: all 0.3s;
    }
    
    .ant-select-selector,
    .ant-input,
    .ant-input-affix-wrapper,
    .ant-checkbox-wrapper {
      border-radius: 4px;
    }
    
    .ant-input-affix-wrapper:hover,
    .ant-input:hover {
      border-color: #40a9ff;
    }
    
    .ant-checkbox-wrapper {
      color: #595959;
    }
    
    .ant-form-item {
      margin-bottom: 14px;
    }
  }
}

.formSection {
  background-color: #fafafa;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 12px;
  transition: all 0.3s;
  border: 1px solid transparent;
  
  &:hover {
    border-color: #e6f7ff;
    background-color: #f5f9fc;
  }
  
  &:last-child {
    margin-bottom: 6px;
  }
}

.sectionDivider {
  margin: 0 0 12px 0 !important;
  font-weight: 500 !important;
  font-size: 13px !important;
}

.helpIcon {
  margin-left: 6px;
  font-size: 12px;
  color: #bfbfbf;
  cursor: help;
}

.deployTypeRadio {
  display: flex;
  
  :global {
    .ant-radio-button-wrapper {
      flex: 1;
      text-align: center;
      transition: all 0.3s;
      
      &:first-child {
        border-radius: 4px 0 0 4px;
      }
      
      &:last-child {
        border-radius: 0 4px 4px 0;
      }
    }
  }
}

.rdmTypeRadio {
  display: flex;
  width: 220px;
  
  :global {
    .ant-radio-button-wrapper {
      flex: 1;
      text-align: center;
    }
  }
}

.rdmInput {
  width: 100%;
  &:hover {
    border-color: #40a9ff;
  }
  
  &:focus {
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }
  
  :global {
    .ant-select-selector {
      box-shadow: none !important;
    }
    
    .ant-select-dropdown {
      max-height: 300px;
      
      .ant-select-item {
        padding: 6px 12px;
        
        &-option-active {
          background-color: #f0f7ff;
        }
      }
    }
    
    .ant-input-suffix {
      .ant-btn {
        margin-right: -7px;
      }
    }
  }
}

.rdmDropdown {
  :global {
    .ant-select-item {
      padding: 0 !important;

      &:hover {
        background-color: transparent !important;

        > div {
          background-color: #e6f7ff !important;
        }
      }

      &-option-selected > div {
        background-color: #e6f7ff !important;
      }
    }
  }
}

.groupActions {
  .actionLink {
    font-size: 12px;
    padding: 0 6px;
    height: 22px;
    color: #1890ff;
    
    &:hover {
      color: #40a9ff;
    }
  }
  
  .clearLink {
    font-size: 12px;
    padding: 0 6px;
    height: 22px;
    color: #8c8c8c;
    
    &:hover {
      color: #595959;
    }
  }
  
  .divider {
    color: #d9d9d9;
  }
}

.groupSelect {
  width: 100%;
  
  :global {
    .ant-select-selector {
      padding: 2px 4px !important;
      min-height: 36px;
    }
    
    .ant-select-selection-item {
      margin: 2px;
      padding: 0 8px;
      background: #f0f7ff;
      border: 1px solid #d6e8fc;
      border-radius: 4px;
      transition: all 0.3s;
      
      &:hover {
        background: #e6f7ff;
        border-color: #91caff;
      }
    }
    
    .ant-select-selection-search {
      margin-left: 4px;
    }
    
    .ant-select-dropdown {
      .ant-select-item {
        padding: 8px 12px;
        
        &-option-selected {
          background-color: #e6f7ff !important;
          font-weight: normal;
          
          &::before {
            content: '✓';
            color: #1890ff;
            margin-right: 6px;
            font-size: 12px;
          }
        }
        
        &-option-active {
          background-color: #f5f5f5;
        }
      }
    }
    
    .ant-tag {
      margin-right: 0;
    }
  }
}

.groupOption {
  display: flex;
  align-items: center;
  padding: 4px 0;
}

.versionDropdown {
  :global {
    .ant-select-item {
      padding: 6px 12px;
      border-radius: 4px;
      transition: all 0.2s;
      
      &:hover {
        background-color: #f0f7ff;
      }
    }
  }
}

.commentTextarea {
  resize: none;
  border-radius: 4px;
  
  &:hover {
    border-color: #40a9ff;
  }
  
  &:focus {
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }
}

.deployButton {
  margin-right: 8px;
  padding: 0 16px;
  height: 34px;
  border-radius: 4px;
  box-shadow: 0 2px 0 rgba(0, 0, 0, 0.043);
  transition: all 0.3s;
  
  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 3px 0 rgba(0, 0, 0, 0.043);
  }
}

.deployBtnWrapper {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
  
  :global {
    .ant-btn {
      transition: all 0.3s;
      display: flex;
      align-items: center;
      justify-content: center;
      
      &:not(:first-child) {
        margin-left: 12px;
      }
      
      .anticon {
        margin-right: 6px;
      }
    }
  }
}

/* 部署顺序调整模态框样式 */
.orderModal {
  :global {
    .ant-modal-header {
      border-bottom: 1px solid #f0f0f0;
      padding: 16px 24px;
    }
    
    .ant-modal-body {
      padding: 20px 24px;
    }
    
    .ant-modal-footer {
      border-top: 1px solid #f0f0f0;
      padding: 10px 16px;
    }
  }
}

.modalContent {
  padding: 8px 0;
}

.instructions {
  background-color: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 4px;
  padding: 10px 12px;
  margin-bottom: 16px;
  color: #389e0d;
  display: flex;
  align-items: center;
  
  :global {
    .anticon {
      margin-right: 8px;
    }
  }
}

.sectionTitle {
  font-weight: 500;
  font-size: 14px;
  margin-bottom: 12px;
  color: #262626;
}

.modeSelection {
  margin-bottom: 16px;
}

.parallelSettings {
  margin-bottom: 16px;
  padding: 12px;
  background: #f5f5f5;
  border-radius: 4px;
}

.concurrencyControl {
  display: flex;
  align-items: center;
  gap: 8px;
}

.helpText {
  color: #8c8c8c;
  font-size: 12px;
  margin-left: 4px;
}

.batchesSection {
  margin-top: 24px;
  margin-bottom: 16px;
}

.batchesContainer {
  display: flex;
  flex-wrap: nowrap;
  overflow-x: auto;
  padding: 8px 0 12px;
  min-height: 160px;
  position: relative;
  gap: 16px;
  align-items: flex-start;
}

.batchWrapper {
  min-width: 140px;
  max-width: 180px;
  flex-shrink: 0;
  position: relative;
  display: flex;
  flex-direction: column;
}

.firstBatch {
  .batchHeader {
    background-color: #e6f7ff;
    border: 1px solid #91d5ff;
  }
  
  .groupsContainer {
    background-color: #f0f7ff;
    border: 1px solid #bae0ff;
  }
}

.batchHeader {
  background: #f5f5f5;
  border-radius: 4px;
  border: 1px solid #d9d9d9;
  padding: 8px 12px;
  margin-bottom: 4px;
  cursor: grab;
  
  &:active {
    cursor: grabbing;
  }
}

.batchLabel {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-weight: 500;
  font-size: 13px;
  color: #262626;
}

.groupsContainer {
  background: #fafafa;
  border-radius: 4px;
  border: 1px solid #e8e8e8;
  padding: 8px;
  min-height: 100px;
  transition: background-color 0.2s;
}

.draggingOver {
  background-color: #e6f7ff;
  border-color: #1890ff;
}

.groupItem {
  background: white;
  border-radius: 4px;
  border: 1px solid #e8e8e8;
  padding: 8px 10px;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all 0.2s;
  cursor: grab;
  
  &:last-child {
    margin-bottom: 0;
  }
  
  &:hover {
    border-color: #40a9ff;
    
    .groupDragIcon {
      color: #40a9ff;
    }
  }
  
  &.dragging {
    border-color: #1890ff;
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
  }
}

.groupDragIcon {
  color: #d9d9d9;
  font-size: 12px;
  cursor: grab;
  
  &:active {
    cursor: grabbing;
  }
}

.emptyBatchHint {
  color: #bfbfbf;
  font-size: 12px;
  text-align: center;
  padding: 16px 0;
  font-style: italic;
}

.arrowContainer {
  position: absolute;
  right: -14px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 2;
  height: 24px;
  width: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  border-radius: 50%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.arrowIcon {
  color: #1890ff;
  font-size: 14px;
}

.dragIcon {
  color: #bfbfbf;
  font-size: 14px;
  margin-left: 4px;
  
  &:hover {
    color: #40a9ff;
  }
}

.priorityLabel {
  color: #1890ff;
  font-size: 12px;
  margin-left: 4px;
}

.parallelLabel {
  color: #fa8c16;
  font-size: 12px;
  margin-left: 4px;
}

.currentTag {
  color: #1890ff;
  font-size: 12px;
  margin-left: 4px;
  
  &::before {
    content: '(当前)';
  }
}

.addBatchButtonContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 120px;
  margin-left: 8px;
  flex-shrink: 0;
}

.addBatchButton {
  width: 100%;
  height: 100%;
  min-height: 120px;
  border-style: dashed;
}

.deployFlowDescription {
  margin-top: 12px;
  color: #8c8c8c;
  font-size: 12px;
  display: flex;
  align-items: center;
  
  :global {
    .anticon {
      margin-right: 6px;
      color: #1890ff;
    }
  }
} 