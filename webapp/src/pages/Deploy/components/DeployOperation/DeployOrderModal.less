.orderModal {
  :global {
    .ant-modal-content {
      border-radius: 8px;
    }
    .ant-modal-header {
      border-bottom: 1px solid #f0f0f0;
      padding: 16px 24px;
    }
    .ant-modal-title {
      font-weight: 600;
      font-size: 18px;
    }
    .ant-modal-body {
      padding: 24px;
    }
    .ant-modal-footer {
      border-top: 1px solid #f0f0f0;
      padding: 12px 24px;
    }
  }
}

.modalContent {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* 并行设置部分 */
.parallelSettings {
  background-color: #fafafa;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 12px;
}

.concurrencyControl {
  display: flex;
  align-items: center;
  gap: 12px;
  
  .helpText {
    color: #666;
    font-size: 12px;
    margin-left: 8px;
  }
}

/* 布局容器 */
.deployContainer {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.sectionTitle {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 10px;
  color: #333;
  position: relative;
  padding-left: 8px;
  display: flex;
  align-items: center;
  
  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 4px;
    bottom: 4px;
    width: 3px;
    background-color: #1890ff;
    border-radius: 2px;
  }
}

/* 分组池区域 */
.groupPoolSection {
  background-color: #f6f9fe;
  border: 1px dashed #d9d9d9;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 12px;
}

.groupPoolContainer {
  padding: 6px;
  min-height: 60px;
  max-height: 120px;
  overflow-y: auto;
  background: #fafafa;
  border-radius: 6px;
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  align-content: flex-start;
  
  &.isDraggingOver {
    background: #f0f7ff;
  }
  
  /* 自定义滚动条 */
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #ccc;
    border-radius: 4px;
    
    &:hover {
      background: #aaa;
    }
  }
}

// 部署策略标签
.strategyTag {
  font-size: 9px;
  padding: 0px 3px;
  border-radius: 2px;
  margin-left: 3px;
  flex-shrink: 0;
  color: #fff;
  
  &.blue {
    background-color: #1890ff;
  }
  
  &.green {
    background-color: #52c41a;
  }
  
  &.orange {
    background-color: #fa8c16;
  }
  
  &.purple {
    background-color: #722ed1;
  }
}

.groupItem {
  margin-bottom: 4px;
  padding: 3px 6px;
  background: #fff;
  border: 1px solid #e6e6e6;
  border-radius: 3px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: grab;
  transition: all 0.2s;
  font-size: 12px;
  line-height: 1.5;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  
  &:active {
    cursor: grabbing;
  }
  
  &:hover {
    background: #fafafa;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  }
  
  &.isDragging {
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #d9d9d9;
    background: #fff;
  }
  
  &.currentGroup {
    border: 1px solid #1890ff;
    background-color: #e6f7ff;
    
    &:hover {
      background-color: #e6f7ff;
    }
  }

  &:last-child {
    margin-bottom: 0;
  }
  
  .groupLeft {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    display: flex;
    align-items: center;
    gap: 3px;
  }
  
  .groupName {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  
  .currentTag {
    font-size: 10px;
    padding: 0px 3px;
    background: #1890ff;
    color: white;
    border-radius: 2px;
    margin-left: 4px;
    flex-shrink: 0;
  }
  
  .groupDragIcon {
    color: #bfbfbf;
    font-size: 10px;
    margin-left: 4px;
    flex-shrink: 0;
    transition: color 0.2s;
    
    &:hover {
      color: #666;
    }
  }
}

.emptyStateText {
  color: #bfbfbf;
  text-align: center;
  font-size: 12px;
}

.flowInfo {
  margin-top: 8px;
  font-size: 12px;
  color: #666;
  display: flex;
  align-items: center;
  gap: 6px;
  background: #f9f9f9;
  padding: 6px 10px;
  border-radius: 4px;
  
  :global(.anticon) {
    color: #1890ff;
  }
}

/* 行连接器样式 */
.rowConnector {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 24px;
  margin-bottom: 12px;
  position: relative;
  
  .connectorLine {
    display: flex;
    flex-direction: column;
    align-items: center;
    height: 100%;
    
    &::before {
      content: '';
      position: absolute;
      top: 0;
      height: 100%;
      width: 2px;
      background-color: #1890ff;
    }
  }
  
  .connectorDot {
    width: 6px;
    height: 6px;
    background-color: #1890ff;
    border-radius: 50%;
    position: absolute;
    top: 0;
    z-index: 1;
  }
  
  .connectorArrow {
    position: absolute;
    bottom: -4px;
    color: #1890ff;
    font-size: 16px;
    z-index: 1;
  }
}

/* 批次之间的箭头 */
.batchArrow {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 0 4px;
  margin-top: 30px;
  
  .arrowIcon {
    color: #1890ff;
    font-size: 16px;
  }
}

/* 批次区域 */
.batchesSection {
  margin-top: 8px;
}

.batchesContainer {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding-bottom: 10px;
  
  /* 自定义滚动条 */
  &::-webkit-scrollbar {
    height: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #ccc;
    border-radius: 4px;
    
    &:hover {
      background: #aaa;
    }
  }
}

/* 批次行样式 */
.batchRow {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  gap: 8px;
  width: 100%;
  
  &:not(:last-child) {
    margin-bottom: 12px;
  }
  
  /* 奇数行（从0开始，实际是第2行）从右到左 */
  &:nth-child(even) {
    flex-direction: row-reverse;
    
    .batchArrow {
      transform: rotate(180deg);
    }
  }
}

.batchWrapper {
  background: #fff;
  border: 1px solid #e6e6e6;
  border-radius: 6px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  min-width: 140px;
  max-width: 160px;
  flex-shrink: 0;
  
  /* 第一批次的特殊样式 */
  &.firstBatch {
    border: 1px solid #1890ff;
    box-shadow: 0 0 8px rgba(24, 144, 255, 0.2);
    position: relative;
    
    &::before {
      content: '起点';
      position: absolute;
      top: -18px;
      left: 50%;
      transform: translateX(-50%);
      background-color: #1890ff;
      color: white;
      padding: 0 6px;
      font-size: 10px;
      border-radius: 10px;
      line-height: 16px;
      z-index: 1;
    }
  }
}

.groupsContainer {
  padding: 6px;
  min-height: 70px;
  max-height: 120px;
  overflow-y: auto;
  transition: background-color 0.2s;
  
  &.isDraggingOver {
    background-color: #f0f7ff;
  }

  // 空批次状态
  &.isEmpty {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #fafafa;
    border: 1px dashed #e0e0e0;
    border-radius: 4px;
    margin: 0 6px 6px 6px;
  }
  
  /* 自定义滚动条 */
  &::-webkit-scrollbar {
    width: 4px;
  }
  
  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 2px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #ccc;
    border-radius: 2px;
    
    &:hover {
      background: #aaa;
    }
  }
  
  /* 第一批次内的分组容器样式 */
  .firstBatch & {
    &.isEmpty {
      border: 1px dashed #1890ff;
      background-color: #f0f7ff;
      
      &::before {
        content: '仅限一个分组';
        color: #1890ff;
        font-size: 11px;
        font-weight: 500;
      }
    }
    
    &.isDraggingOver {
      background-color: #e6f7ff;
    }
  }
} 