import React, { useState, useEffect } from 'react';
import {
  Modal, Form, Input, InputNumber, Button,
  Card, Row, Col, message, Select,
  Typography, Space, Divider, Collapse
} from 'antd';
import {
  PlusOutlined, DeleteOutlined, SettingOutlined,
  DatabaseOutlined, CloudServerOutlined, DeploymentUnitOutlined
} from '@ant-design/icons';
import useContainerConfig from '@/hooks/useContainerConfig';
import './ContainerConfigEditor.less';

const { Text, Title } = Typography;
const { Option } = Select;
const { TextArea } = Input;
const { Panel } = Collapse;

export interface ContainerConfigEditorProps {
  visible: boolean;
  appData: API.AppData | null;
  onCancel: () => void;
  onSave: (updatedSettings: any) => Promise<boolean>;
}

const ContainerConfigEditor: React.FC<ContainerConfigEditorProps> = ({
  visible,
  appData,
  onCancel,
  onSave
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const { containerConfig, saveContainerConfig } = useContainerConfig();

  useEffect(() => {
    if (visible && containerConfig) {
      form.setFieldsValue({
        ...containerConfig,
        // 确保嵌套对象正确设置
        resources: containerConfig.resources || {
          requests: { cpu: '500m', memory: '512Mi' },
          limits: { cpu: '1000m', memory: '1Gi' }
        },
        ports: containerConfig.ports || [
          { name: 'http', container_port: 8080, service_port: 8080, protocol: 'TCP' }
        ],
        rollout_config: containerConfig.rollout_config || {
          max_surge: '25%',
          max_unavailable: '25%',
          revision_history_limit: 10,
          timeout_seconds: 600
        }
      });
    }
  }, [visible, containerConfig, form]);

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);
      
      const success = await saveContainerConfig(values);
      if (success) {
        message.success('容器配置已成功保存');
        onCancel();
      }
    } catch (error) {
      console.error('表单验证失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const addPort = () => {
    const ports = form.getFieldValue('ports') || [];
    ports.push({
      name: `port-${ports.length + 1}`,
      container_port: 8080,
      service_port: 8080,
      protocol: 'TCP'
    });
    form.setFieldsValue({ ports });
  };

  const removePort = (index: number) => {
    const ports = form.getFieldValue('ports') || [];
    if (ports.length > 1) {
      ports.splice(index, 1);
      form.setFieldsValue({ ports });
    }
  };

  if (!appData) {
    return null;
  }

  return (
    <Modal
      title={
        <Space>
          <SettingOutlined />
          <span>容器配置</span>
        </Space>
      }
      open={visible}
      width={800}
      onCancel={onCancel}
      footer={
        <Space>
          <Button onClick={onCancel}>取消</Button>
          <Button type="primary" loading={loading} onClick={handleSubmit}>
            保存配置
          </Button>
        </Space>
      }
      destroyOnClose
      className="container-config-editor compact-layout"
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={containerConfig || undefined}
        size="small"
      >
        <Collapse
          defaultActiveKey={['basic', 'ports', 'resources']}
          size="small"
          ghost
        >
          {/* 基础配置 */}
          <Panel
            header={
              <Space>
                <CloudServerOutlined />
                <span>基础配置</span>
              </Space>
            }
            key="basic"
          >
            <Row gutter={12}>
              <Col span={6}>
                <Form.Item
                  name="replicas"
                  label="副本数"
                  rules={[
                    { required: true, message: '请输入副本数' },
                    { type: 'integer', min: 1, max: 10, message: '副本数必须在1-10之间' }
                  ]}
                >
                  <InputNumber
                    min={1}
                    max={10}
                    style={{ width: '100%' }}
                    placeholder="1"
                    size="small"
                  />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  name="namespace"
                  label="命名空间"
                  rules={[{ required: true, message: '请输入命名空间' }]}
                >
                  <Select placeholder="选择命名空间" size="small">
                    <Option value="default">default</Option>
                    <Option value="production">production</Option>
                    <Option value="staging">staging</Option>
                    <Option value="testing">testing</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  name="strategy"
                  label="部署策略"
                  rules={[{ required: true, message: '请选择部署策略' }]}
                >
                  <Select placeholder="选择策略" size="small">
                    <Option value="RollingUpdate">滚动更新</Option>
                    <Option value="Recreate">重新创建</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  name="base_image"
                  label="基础镜像"
                  rules={[{ required: true, message: '请输入基础镜像' }]}
                >
                  <Input placeholder="openjdk:17-jre-slim" size="small" />
                </Form.Item>
              </Col>
            </Row>
          </Panel>

          {/* 端口配置 */}
          <Panel
            header={
              <Space>
                <DatabaseOutlined />
                <span>端口配置</span>
              </Space>
            }
            key="ports"
            extra={
              <Button
                type="dashed"
                size="small"
                icon={<PlusOutlined />}
                onClick={addPort}
              >
                添加
              </Button>
            }
          >
            <Form.List name="ports">
              {(fields, { add, remove }) => (
                <Space direction="vertical" style={{ width: '100%' }}>
                  {fields.map(({ key, name, ...restField }, index) => (
                    <div key={key} className="port-row-compact">
                      <Row gutter={8} align="bottom">
                        <Col span={5}>
                          <Form.Item
                            {...restField}
                            name={[name, 'name']}
                            label="名称"
                            rules={[{ required: true, message: '请输入端口名称' }]}
                          >
                            <Input placeholder="http" size="small" />
                          </Form.Item>
                        </Col>
                        <Col span={5}>
                          <Form.Item
                            {...restField}
                            name={[name, 'container_port']}
                            label="容器端口"
                            rules={[{ required: true, message: '请输入容器端口' }]}
                          >
                            <InputNumber
                              min={1}
                              max={65535}
                              style={{ width: '100%' }}
                              placeholder="8080"
                              size="small"
                            />
                          </Form.Item>
                        </Col>
                        <Col span={5}>
                          <Form.Item
                            {...restField}
                            name={[name, 'service_port']}
                            label="服务端口"
                            rules={[{ required: true, message: '请输入服务端口' }]}
                          >
                            <InputNumber
                              min={1}
                              max={65535}
                              style={{ width: '100%' }}
                              placeholder="8080"
                              size="small"
                            />
                          </Form.Item>
                        </Col>
                        <Col span={5}>
                          <Form.Item
                            {...restField}
                            name={[name, 'protocol']}
                            label="协议"
                            rules={[{ required: true, message: '请选择协议' }]}
                          >
                            <Select placeholder="TCP" size="small">
                              <Option value="TCP">TCP</Option>
                              <Option value="UDP">UDP</Option>
                            </Select>
                          </Form.Item>
                        </Col>
                        <Col span={4}>
                          {fields.length > 1 && (
                            <Form.Item>
                              <Button
                                type="text"
                                danger
                                icon={<DeleteOutlined />}
                                onClick={() => remove(name)}
                                size="small"
                              />
                            </Form.Item>
                          )}
                        </Col>
                      </Row>
                    </div>
                  ))}
                </Space>
              )}
            </Form.List>
          </Panel>

          {/* 资源配置 */}
          <Panel
            header={
              <Space>
                <DeploymentUnitOutlined />
                <span>资源配置</span>
              </Space>
            }
            key="resources"
          >
            <Row gutter={12}>
              <Col span={12}>
                <Card size="small" title="CPU 配置" style={{ marginBottom: 8 }}>
                  <Row gutter={8}>
                    <Col span={12}>
                      <Form.Item
                        name={['resources', 'requests', 'cpu']}
                        label="请求"
                        rules={[
                          { required: true, message: '请输入CPU请求' },
                          { pattern: /^\d+m?$/, message: 'CPU格式不正确' }
                        ]}
                      >
                        <Input placeholder="500m" size="small" />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        name={['resources', 'limits', 'cpu']}
                        label="限制"
                        rules={[
                          { required: true, message: '请输入CPU限制' },
                          { pattern: /^\d+m?$/, message: 'CPU格式不正确' }
                        ]}
                      >
                        <Input placeholder="1000m" size="small" />
                      </Form.Item>
                    </Col>
                  </Row>
                </Card>
              </Col>
              <Col span={12}>
                <Card size="small" title="内存配置" style={{ marginBottom: 8 }}>
                  <Row gutter={8}>
                    <Col span={12}>
                      <Form.Item
                        name={['resources', 'requests', 'memory']}
                        label="请求"
                        rules={[
                          { required: true, message: '请输入内存请求' },
                          { pattern: /^\d+(Mi|Gi|M|G)$/, message: '内存格式不正确' }
                        ]}
                      >
                        <Input placeholder="512Mi" size="small" />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        name={['resources', 'limits', 'memory']}
                        label="限制"
                        rules={[
                          { required: true, message: '请输入内存限制' },
                          { pattern: /^\d+(Mi|Gi|M|G)$/, message: '内存格式不正确' }
                        ]}
                      >
                        <Input placeholder="1Gi" size="small" />
                      </Form.Item>
                    </Col>
                  </Row>
                </Card>
              </Col>
            </Row>
          </Panel>

          {/* 发布配置 */}
          <Panel
            header={
              <Space>
                <DeploymentUnitOutlined />
                <span>发布配置</span>
              </Space>
            }
            key="rollout"
          >
            <Row gutter={12}>
              <Col span={6}>
                <Form.Item
                  name={['rollout_config', 'max_surge']}
                  label="最大激增"
                  rules={[
                    { required: true, message: '请输入最大激增' },
                    { pattern: /^(\d+%?|\d+)$/, message: '格式不正确' }
                  ]}
                >
                  <Input placeholder="25%" size="small" />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  name={['rollout_config', 'max_unavailable']}
                  label="最大不可用"
                  rules={[
                    { required: true, message: '请输入最大不可用' },
                    { pattern: /^(\d+%?|\d+)$/, message: '格式不正确' }
                  ]}
                >
                  <Input placeholder="25%" size="small" />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  name={['rollout_config', 'revision_history_limit']}
                  label="历史版本"
                  rules={[
                    { type: 'integer', min: 1, max: 100, message: '必须在1-100之间' }
                  ]}
                >
                  <InputNumber
                    min={1}
                    max={100}
                    style={{ width: '100%' }}
                    placeholder="10"
                    size="small"
                  />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  name={['rollout_config', 'timeout_seconds']}
                  label="超时时间(秒)"
                  rules={[
                    { type: 'integer', min: 60, max: 3600, message: '必须在60-3600秒之间' }
                  ]}
                >
                  <InputNumber
                    min={60}
                    max={3600}
                    style={{ width: '100%' }}
                    placeholder="600"
                    size="small"
                  />
                </Form.Item>
              </Col>
            </Row>
          </Panel>

          {/* 环境变量配置 */}
          <Panel
            header={
              <Space>
                <SettingOutlined />
                <span>环境变量</span>
              </Space>
            }
            key="environment"
          >
            <Form.Item
              name="environment"
              label="环境变量 (JSON格式)"
              rules={[
                {
                  validator: (_, value) => {
                    if (!value) return Promise.resolve();
                    try {
                      JSON.parse(value);
                      return Promise.resolve();
                    } catch {
                      return Promise.reject(new Error('请输入有效的JSON格式'));
                    }
                  }
                }
              ]}
            >
              <TextArea
                rows={3}
                placeholder='{"ENV_NAME": "value", "DEBUG": "true"}'
                size="small"
              />
            </Form.Item>
          </Panel>
        </Collapse>
      </Form>
    </Modal>
  );
};

export default ContainerConfigEditor; 