/* ContainerConfigTooltip 组件样式 */

.container-config-card {
  border-radius: 4px;
  border: 1px solid #e8e8e8;

  .ant-descriptions {
    .ant-descriptions-item-label {
      font-weight: 500;
      color: #262626;
      background-color: #fafafa;
      border-right: 1px solid #e8e8e8;
    }

    .ant-descriptions-item-content {
      background-color: white;
      padding: 8px 12px;
    }
  }
}

/* 严肃专业的样式 */
.professional-layout {
  .ant-card {
    border: 1px solid #e8e8e8;
    border-radius: 4px;

    .ant-card-head {
      background-color: #fafafa;
      border-bottom: 1px solid #e8e8e8;
    }

    .ant-card-body {
      background-color: #ffffff;
    }
  }

  .ant-form-item-label > label {
    color: #262626;
    font-weight: 500;
  }

  .ant-input,
  .ant-input-number,
  .ant-select-selector {
    border-color: #d9d9d9;

    &:hover {
      border-color: #40a9ff;
    }

    &:focus {
      border-color: #1890ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }
  }
}

/* 资源配置分组样式 */
.resource-config-group {
  background-color: #fafafa;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  padding: 12px;
  margin-bottom: 16px;

  .resource-title {
    font-weight: 600;
    font-size: 13px;
    color: #262626;
    margin-bottom: 12px;
    display: block;
  }

  .ant-form-item-label > label {
    color: #595959;
    font-weight: 500;
    font-size: 12px;
  }

  .ant-input {
    font-size: 12px;
    border-color: #d9d9d9;

    &:focus {
      border-color: #1890ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }
  }

  .ant-row {
    .ant-col {
      &:first-child {
        .ant-form-item-label > label::after {
          content: " (最小)";
          color: #8c8c8c;
          font-weight: normal;
        }
      }

      &:last-child {
        .ant-form-item-label > label::after {
          content: " (最大)";
          color: #8c8c8c;
          font-weight: normal;
        }
      }
    }
  }
}

/* 简洁的端口配置样式 */
.port-config-item {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  background-color: #fafafa;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 12px;
  margin: 2px;
}

/* 复制按钮样式 */
.copyable-content {
  display: flex;
  align-items: center;
  gap: 8px;
  
  .copy-button {
    opacity: 0;
    transition: opacity 0.2s ease;
    
    &:hover {
      background-color: #f5f5f5;
    }
  }
  
  &:hover .copy-button {
    opacity: 1;
  }
}

/* 表单卡片样式 */
.form-section-card {
  margin-bottom: 12px;
  border-radius: 6px;

  .ant-card-head {
    background-color: #fafafa;
    border-bottom: 1px solid #f0f0f0;
    border-radius: 6px 6px 0 0;
    padding: 0 12px;
    min-height: 36px;

    .ant-card-head-title {
      font-size: 13px;
      font-weight: 500;
      line-height: 36px;
    }
  }

  .ant-card-body {
    background-color: white;
    border-radius: 0 0 6px 6px;
    padding: 12px;
  }

  // 紧凑模式下的表单项
  .ant-form-item {
    margin-bottom: 12px;

    .ant-form-item-label {
      padding-bottom: 2px;

      > label {
        font-size: 12px;
        height: auto;
        line-height: 1.4;
      }
    }

    .ant-form-item-control {
      .ant-input,
      .ant-input-number,
      .ant-select-selector {
        font-size: 12px;
      }
    }
  }
}

/* 表单项增强样式 */
.enhanced-form-item {
  .ant-form-item-label {
    .form-label-with-tooltip {
      display: flex;
      align-items: center;
      gap: 4px;
      
      .tooltip-icon {
        color: #1890ff;
        cursor: help;
        
        &:hover {
          color: #40a9ff;
        }
      }
    }
  }
}

/* 模态框样式增强 */
.container-config-modal {
  .ant-modal-header {
    background-color: #fafafa;
    border-bottom: 1px solid #f0f0f0;
    border-radius: 8px 8px 0 0;
  }
  
  .ant-modal-body {
    background-color: #fcfcfc;
    padding: 24px;
  }
  
  .ant-modal-footer {
    background-color: #fafafa;
    border-top: 1px solid #f0f0f0;
    border-radius: 0 0 8px 8px;
  }
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 24px;
  
  .loading-text {
    margin-top: 16px;
    color: #8c8c8c;
    font-size: 14px;
  }
}

/* 空状态样式 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 24px;
  
  .empty-icon {
    font-size: 48px;
    color: #faad14;
    margin-bottom: 16px;
  }
  
  .empty-text {
    color: #8c8c8c;
    font-size: 14px;
  }
}

/* 紧凑布局样式 */
.compact-layout {
  .ant-descriptions {
    .ant-descriptions-item-label {
      width: 80px;
      font-size: 12px;
      padding: 6px 8px;
    }

    .ant-descriptions-item-content {
      padding: 6px 8px;
      font-size: 12px;
    }
  }

  .ant-tag {
    font-size: 11px;
    padding: 0 4px;
    line-height: 16px;
    margin: 2px;
  }
}

/* 两列布局优化 */
.two-column-layout {
  .ant-descriptions-item {
    &:nth-child(odd) {
      .ant-descriptions-item-label {
        border-right: 1px solid #f0f0f0;
      }
    }

    &:nth-child(even) {
      .ant-descriptions-item-label {
        border-right: none;
      }
    }
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container-config-card {
    .ant-descriptions {
      .ant-descriptions-item {
        padding-bottom: 8px;
      }

      .ant-descriptions-item-label {
        width: 80px;
        font-size: 11px;
        padding: 4px 6px;
      }

      .ant-descriptions-item-content {
        padding: 4px 6px;
        font-size: 11px;
      }
    }
  }

  .resource-config-card {
    margin-bottom: 8px;
    padding: 8px !important;

    .ant-form-item {
      margin-bottom: 6px;
    }
  }

  .port-config-item {
    padding: 4px 6px;

    .port-info {
      flex-direction: column;
      align-items: flex-start;
      gap: 2px;
    }
  }

  .form-section-card {
    .ant-card-body {
      padding: 8px;
    }
  }
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.container-config-card {
  animation: fadeInUp 0.3s ease-out;
}

.form-section-card {
  animation: fadeInUp 0.3s ease-out;
}

/* 深色主题支持 */
@media (prefers-color-scheme: dark) {
  .container-config-card {
    background-color: #1f1f1f;
    border-color: #434343;
    
    .ant-descriptions-item-label {
      background-color: #262626;
      color: #f0f0f0;
      border-color: #434343;
    }
    
    .ant-descriptions-item-content {
      background-color: #1f1f1f;
      color: #f0f0f0;
    }
  }
  
  .resource-config-card {
    &.requests {
      background-color: #162312;
      border-color: #389e0d;
    }
    
    &.limits {
      background-color: #2b1d11;
      border-color: #d46b08;
    }
  }
  
  .port-config-item {
    background-color: #111a2c;
    border-color: #1890ff;
  }
}
