import React, { useState } from 'react';
import { Button, Space, Card, Typography, Alert } from 'antd';
import { SettingOutlined, EyeOutlined } from '@ant-design/icons';
import ContainerConfigEditor from './ContainerConfigEditor';

const { Title, Paragraph } = Typography;

/**
 * ContainerConfigEditor 优化演示页面
 * 展示优化后的紧凑布局和更合适的组件选择
 */
const ContainerConfigEditorDemo: React.FC = () => {
  const [visible, setVisible] = useState(false);

  // 模拟应用数据
  const mockAppData: API.AppData = {
    application: {
      id: 1,
      name: 'demo-app',
      git_url: 'https://github.com/demo/demo-app.git',
      status: 1,
      c_t: Date.now(),
      create_by: 1,
      u_t: Date.now(),
      update_by: 1,
      is_deleted: 0
    },
    environments: []
  };

  const handleSave = async (config: any) => {
    console.log('保存配置:', config);
    // 模拟保存操作
    return new Promise<boolean>((resolve) => {
      setTimeout(() => {
        resolve(true);
      }, 1000);
    });
  };

  return (
    <div style={{ padding: '24px', maxWidth: '1200px', margin: '0 auto' }}>
      <Card>
        <Title level={2}>
          <Space>
            <SettingOutlined />
            ContainerConfigEditor 组件优化演示
          </Space>
        </Title>
        
        <Alert
          message="优化亮点"
          description={
            <div>
              <Paragraph>
                <strong>1. 紧凑布局设计：</strong>
                <ul>
                  <li>Modal 宽度从 900px 优化为 800px</li>
                  <li>组件间距从 16px 减少到 8-12px</li>
                  <li>所有表单控件使用 size="small"</li>
                  <li>优化卡片和面板的 padding</li>
                </ul>
              </Paragraph>
              
              <Paragraph>
                <strong>2. 更合适的组件选择：</strong>
                <ul>
                  <li>使用 Collapse 折叠面板替代多个独立 Card</li>
                  <li>添加语义化图标提升识别度</li>
                  <li>端口配置使用专门的紧凑样式</li>
                  <li>资源配置使用嵌套小卡片</li>
                </ul>
              </Paragraph>
              
              <Paragraph>
                <strong>3. 用户体验改进：</strong>
                <ul>
                  <li>重要配置项默认展开</li>
                  <li>悬停效果和聚焦状态优化</li>
                  <li>完整的响应式和深色主题支持</li>
                </ul>
              </Paragraph>
            </div>
          }
          type="info"
          showIcon
          style={{ marginBottom: '24px' }}
        />

        <Space size="large">
          <Button
            type="primary"
            icon={<EyeOutlined />}
            onClick={() => setVisible(true)}
            size="large"
          >
            查看优化后的容器配置编辑器
          </Button>
        </Space>

        <ContainerConfigEditor
          visible={visible}
          appData={mockAppData}
          onCancel={() => setVisible(false)}
          onSave={handleSave}
        />
      </Card>
    </div>
  );
};

export default ContainerConfigEditorDemo;
