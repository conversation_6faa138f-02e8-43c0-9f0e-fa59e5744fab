import React from 'react';
import { Card, Space } from 'antd';
import ContainerConfigTooltip from './ContainerConfigTooltip';

/**
 * ContainerConfigTooltip 组件演示页面
 * 用于展示优化后的容器配置UI组件
 */
const ContainerConfigDemo: React.FC = () => {
  return (
    <div style={{ padding: '24px', backgroundColor: '#f5f5f5', minHeight: '100vh' }}>
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        <Card title="容器配置组件演示" style={{ maxWidth: 800 }}>
          <p>这是优化后的容器配置UI组件，具有以下特性：</p>
          <ul>
            <li>🎨 现代化的视觉设计，使用卡片布局和丰富的颜色</li>
            <li>📱 响应式设计，支持移动端显示</li>
            <li>🔍 配置状态指示器，一目了然地显示配置完整性</li>
            <li>📋 一键复制功能，方便复制配置信息</li>
            <li>💡 智能提示和验证，提供详细的帮助信息</li>
            <li>⚡ 优化的表单体验，分组展示不同类型的配置</li>
            <li>🌙 支持深色主题</li>
            <li>✨ 流畅的动画效果</li>
          </ul>
        </Card>
        
        <Card title="只读模式" style={{ maxWidth: 800 }}>
          <ContainerConfigTooltip editable={false} />
        </Card>
        
        <Card title="可编辑模式" style={{ maxWidth: 800 }}>
          <ContainerConfigTooltip editable={true} />
        </Card>
      </Space>
    </div>
  );
};

export default ContainerConfigDemo;
