import React from 'react';
import { Button, message } from 'antd';
import { DashboardOutlined, DatabaseOutlined } from '@ant-design/icons';
import './style.less';
interface MonitoringButtonsProps {
    appData: API.AppData;
}

const MonitoringButtons: React.FC<MonitoringButtonsProps> = ({
  appData
}) => {
  // 打开监控系统
  const openMonitoring = () => {
    if (!appData?.application?.id) {
      message.warning('请先选择应用');
      return;
    }
    
    // 获取应用监控链接 - 实际应用中可能需要根据应用ID或名称构建URL
    const monitoringUrl = `http://monitoring.example.com/dashboard/${appData.application.id}`;
    window.open(monitoringUrl, '_blank');
  };

  // 打开缓存平台
  const openCachePlatform = () => {
    if (!appData?.application?.id) {
      message.warning('请先选择应用');
      return;
    }
    
    // 获取应用缓存平台链接 - 实际应用中可能需要根据应用ID或名称构建URL
    const cacheUrl = `http://cache.example.com/app/${appData.application.id}`;
    window.open(cacheUrl, '_blank');
  };

  return (
    <div className="app-action-buttons">
      <Button
        type="text"
        size="small"
        ghost
        icon={<DashboardOutlined />}
        onClick={openMonitoring}
      >
        监控
      </Button>
      
      <Button
        type="text"
        size="small"
        ghost
        icon={<DatabaseOutlined />}
        onClick={openCachePlatform}
      >
        缓存
      </Button>
    </div>
  );
};

export default MonitoringButtons; 