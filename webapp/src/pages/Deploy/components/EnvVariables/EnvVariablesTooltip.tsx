import useEnvironmentVariable from '@/hooks/useEnvironmentVariable';
import {
  CheckCircleOutlined,
  CloseCircleOutlined,
  CodeOutlined,
  DeleteOutlined,
  EditOutlined,
  EyeOutlined,
  PlayCircleOutlined,
  PlusOutlined,
  QuestionCircleOutlined
} from '@ant-design/icons';
import { Editor } from '@monaco-editor/react';
import { useModel } from '@umijs/max';
import {
  Button,
  Card,
  Form,
  Input,
  message,
  Modal,
  Popconfirm,
  Radio,
  Space,
  Table,
  Tabs,
  Tag,
  Tooltip,
  Typography,
  Descriptions,
  Divider
} from 'antd';
import React, { useState } from 'react';
import { SCRIPT_HELP, SCRIPT_TEMPLATE } from './constants';
import EnvVariableEditModal from './EnvVariableEditModal';

// 提取脚本概要的辅助函数
const extractScriptSummary = (script: string): string => {
  if (!script) return '// 空脚本';
  
  // 去除注释和多余空格
  const cleanScript = script.replace(/\/\/.*$/gm, '').trim();
  
  // 尝试提取 return 语句
  const returnMatch = cleanScript.match(/return\s+([^;]+)/);
  if (returnMatch) {
    return `return ${returnMatch[1].substring(0, 30)}${returnMatch[1].length > 30 ? '...' : ''}`;
  }
  
  // 如果没有 return，返回前几个字符
  return `${cleanScript.substring(0, 30)}${cleanScript.length > 30 ? '...' : ''}`;
};

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;

interface EnvVariablesTooltipProps {
  editable?: boolean;
}

// Fix property mismatches in environment variable types
const EnvVariablesTooltip: React.FC<EnvVariablesTooltipProps> = ({
  editable = false,
}) => {
  const { deploy, updateDeploy } = useModel('deploy');
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [viewModalVisible, setViewModalVisible] = useState(false);

  const { 
    envVariableTooltipView, 
    updateEnvVariableTooltipView, 
    updateEnvironmentVariable, 
    createEnvironmentVariable 
  } = useEnvironmentVariable(deploy.appData.application?.id, deploy.envId);

  // 查看环境变量
  const handleViewVariable = (record: API.EnvironmentVariable) => {
    updateEnvVariableTooltipView(record);
    setViewModalVisible(true);
  };

  // 编辑环境变量
  const handleEditVariable = (record: API.EnvironmentVariable) => {
    updateEnvVariableTooltipView(record);
    setEditModalVisible(true);
  };

  // 添加环境变量
  const handleAddVariable = () => {
    updateEnvVariableTooltipView({
      app_id: deploy.appData.application?.id!,
      env_id: deploy.envId,
      key_name: '',
      value: '',
      description: '',
      variable_type: 0,
    });
    setEditModalVisible(true);
  };

  // 删除环境变量
  const handleDelete = async (envVariable: API.EnvironmentVariable) => {
    try {
      const result = await updateEnvironmentVariable({
        id: envVariable.id,
        key_name: envVariable.key_name,
        value: envVariable.value,
        description: envVariable.description,
        variable_type: envVariable.variable_type || 0,
        status: 0
      });
      if (result) {
        message.success('环境变量已删除');
      } else {
        message.error('删除失败');
      }
    } catch (error) {
      message.error('删除失败：' + (error as any).message);
    }
  };

  // 保存成功后的处理
  const handleSaveSuccess = (values: any) => {
    setEditModalVisible(false);
    setViewModalVisible(false);
    form.resetFields();
  };

  // 添加从查看模式切换到编辑模式的处理函数
  const handleSwitchToEdit = () => {
    setViewModalVisible(false);
    setEditModalVisible(true);
    // 不需要重置表单或更新变量，因为我们继续使用相同的变量数据
  };

  // 表格列配置
  const columns = [
        {
          title: '变量名',
          dataIndex: 'key_name',
          key: 'key_name',
      render: (value: string, record: API.EnvironmentVariable) => (
        <Space>
          {value}
          {record.variable_type === 1 && (
            <Tooltip title="脚本变量">
              <CodeOutlined style={{ color: '#1890ff' }} />
            </Tooltip>
          )}
        </Space>
      )
        },
        {
          title: '变量值',
          dataIndex: 'value',
          key: 'value',
          render: (value: string, record: API.EnvironmentVariable) => {
            if (record.variable_type === 1) {
              // 可编程变量 - 显示代码概要
              const scriptSummary = extractScriptSummary(value);
              
              return (
                <Tooltip 
                  title={
                    <div style={{ maxWidth: '400px', maxHeight: '200px', overflow: 'auto' }}>
                      <pre style={{ margin: 0, whiteSpace: 'pre-wrap' }}>{value}</pre>
                      {(record as any).scriptExecutionResult && (
                        <>
                          <Divider style={{ margin: '8px 0' }} />
                          <div>
                            <Text type="secondary">执行结果:</Text>
                            <div style={{ marginTop: '4px', padding: '4px 8px', background: '#f5f5f5', borderRadius: '4px' }}>
                              {(record as any).scriptExecutionResult || '未执行'}
                            </div>
                          </div>
                        </>
                      )}
                    </div>
                  }
                  placement="topLeft"
                  overlayStyle={{ maxWidth: '500px' }}
                >
                  <div>
                    <Text code style={{ color: '#08979c' }}>
                      <CodeOutlined style={{ marginRight: '4px' }} />
                      {scriptSummary}
                    </Text>
                  </div>
                </Tooltip>
              );
            } else {
              // 普通变量 - 显示部分值，如果太长就截断
              if (value && value.length > 30) {
                return (
                  <Tooltip title={value} placement="topLeft">
                    {`${value.substring(0, 30)}...`}
                  </Tooltip>
                );
              }
              return value || <Text type="secondary">-</Text>;
            }
          }
        },
        {
          title: '描述',
          dataIndex: 'description',
          key: 'description',
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: API.EnvironmentVariable) => (
        <Space>
          <Tooltip title="查看">
            <Button 
              type="text" 
              size="small"
              icon={<EyeOutlined />}
              onClick={() => handleViewVariable(record)}
            />
          </Tooltip>
          {editable && (
            <>
              <Tooltip title="编辑">
                <Button 
                  type="text" 
                  size="small"
                  icon={<EditOutlined />}
                  onClick={() => handleEditVariable(record)}
                />
              </Tooltip>
              <Popconfirm
                title="确定要删除此环境变量吗？"
                okText="删除"
                cancelText="取消"
                okButtonProps={{ danger: true }}
                onConfirm={() => handleDelete(record)}
                placement="topRight"
              >
                <Tooltip title="删除">
                  <Button 
                    type="text" 
                    danger
                    size="small"
                    icon={<DeleteOutlined />} 
                  />
                </Tooltip>
              </Popconfirm>
            </>
          )}
          {record.variable_type === 1 && (
            <Tooltip title="执行脚本">
              <Button
                type="text"
                size="small"
                icon={<PlayCircleOutlined />}
                onClick={() => {
                  updateEnvVariableTooltipView(record);
                  setEditModalVisible(true);
                }}
              />
            </Tooltip>
          )}
        </Space>
      ),
    }
  ];

  return (
    <Card 
      variant="borderless"
      className="env-variables-card"
      style={{ backgroundColor: '#fcfcfc' }}
      styles={{ body: { padding: '0px' } }}
    >
      <div style={{ padding: '8px 12px', display: 'flex', justifyContent: 'space-between', alignItems: 'center', borderBottom: '1px solid #f0f0f0' }}>
        <Title level={5} style={{ margin: 0 }}>
          环境变量 ({deploy.environmentVariables.length || 0})
        </Title>
        {editable && (
          <Button 
            type="primary" 
            size="small" 
            icon={<PlusOutlined />} 
            onClick={handleAddVariable}
          >
            添加变量
          </Button>
        )}
      </div>
      
      <Table 
        size="small"
        style={{ backgroundColor: 'white' }}
        pagination={false}
        dataSource={deploy.environmentVariables}
        rowKey="id"
        columns={columns}
        locale={{ emptyText: '暂无环境变量' }}
      />

      {/* 编辑/添加/查看环境变量模态框 */}
      <EnvVariableEditModal
        visible={editModalVisible || viewModalVisible}
        onCancel={() => {
          setEditModalVisible(false);
          setViewModalVisible(false);
          form.resetFields();
        }}
        onSaveSuccess={handleSaveSuccess}
        envVariableTooltipView={envVariableTooltipView}
        updateEnvVariableTooltipView={updateEnvVariableTooltipView}
        loading={loading}
        environmentVariables={deploy.environmentVariables}
        updateEnvironmentVariable={updateEnvironmentVariable}
        createEnvironmentVariable={createEnvironmentVariable}
        readOnly={viewModalVisible}
        onSwitchToEdit={editable ? handleSwitchToEdit : undefined}
      />
    </Card>
  );
};

export default EnvVariablesTooltip; 