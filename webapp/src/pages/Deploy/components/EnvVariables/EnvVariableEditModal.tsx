import React, { useState } from 'react';
import {
  Button,
  Form,
  Input,
  message,
  Modal,
  Radio,
  Space,
  Tooltip,
  Typography,
  Tag
} from 'antd';
import {
  CheckCircleOutlined,
  CloseCircleOutlined,
  PlayCircleOutlined,
  QuestionCircleOutlined,
  CodeOutlined,
  EditOutlined
} from '@ant-design/icons';
import { Editor } from '@monaco-editor/react';
import { SCRIPT_HELP, SCRIPT_TEMPLATE } from './constants';
import { useModel } from 'umi';

const { Title, Text, Paragraph } = Typography;

// 修改类型定义，让它与原组件中使用的类型兼容
interface EnvVariableTooltipView extends API.EnvironmentVariable {
  scriptExecutionResult?: string | null;
  scriptExecutionError?: string | null;
  scriptExecutionLoading?: boolean;
  executionStatus?: number | null;
}

interface EnvVariableEditModalProps {
  visible: boolean;
  onCancel: () => void;
  onSaveSuccess: (values: any) => void;
  envVariableTooltipView: EnvVariableTooltipView | null;
  updateEnvVariableTooltipView: (update: any) => void;
  loading: boolean;
  environmentVariables: API.EnvironmentVariable[];
  updateEnvironmentVariable: (params: API.UpdateEnvironmentVariableRequest) => Promise<API.EnvironmentVariable | null>;
  createEnvironmentVariable: (params: API.CreateEnvironmentVariableRequest) => Promise<API.EnvironmentVariable | null>;
  readOnly?: boolean; // 添加只读模式属性
  onSwitchToEdit?: () => void; // 添加切换到编辑模式的回调
}

const EnvVariableEditModal: React.FC<EnvVariableEditModalProps> = ({
  visible,
  onCancel,
  onSaveSuccess,
  envVariableTooltipView,
  updateEnvVariableTooltipView,
  loading,
  environmentVariables,
  updateEnvironmentVariable,
  createEnvironmentVariable,
  readOnly = false, // 默认为可编辑模式
  onSwitchToEdit
}) => {
  const [form] = Form.useForm();
  const [isSaving, setIsSaving] = useState(false);
  const { deploy } = useModel('deploy');

  // 执行JavaScript代码
  const executeScript = async () => {
    try {
      updateEnvVariableTooltipView({
        scriptExecutionResult: '',
        scriptExecutionError: '',
        scriptExecutionLoading: true,
        executionStatus: 0,
      });
      
      const scriptContent = form.getFieldValue('value');
      if (!scriptContent) {
        updateEnvVariableTooltipView({
          scriptExecutionResult: '',
          scriptExecutionError: '',
          scriptExecutionLoading: false,
          executionStatus: 2,
        });
        return;
      }

      // 创建环境变量对象
      const ENV: Record<string, string> = {};
      environmentVariables.forEach(v => {
        if (v.key_name) {
          // Fix property name mismatch
          ENV[v.key_name] = v.variable_type === 1 ? 
            ((v as any).scriptExecutionResult || (v as any).last_execution_result || '') : 
            v.value;
        }
      });

      try {
        const scriptFunction = new Function('ENV', scriptContent);
        const result = scriptFunction(ENV);
        
        updateEnvVariableTooltipView({
          scriptExecutionResult: String(result),
          scriptExecutionError: '',
          scriptExecutionLoading: false,
          executionStatus: 1,
        });
        message.success('脚本执行成功');
      } catch (err) {
        console.error('脚本执行错误:', err);
        updateEnvVariableTooltipView({
          scriptExecutionResult: '',
          scriptExecutionError: (err as Error).message || '脚本执行失败',
          scriptExecutionLoading: false,
          executionStatus: 2,
        });
      }
    } finally {
      updateEnvVariableTooltipView({
        scriptExecutionLoading: false,
      });
    }
  };

  // 保存编辑/添加的变量
  const handleModalSave = async () => {
    try {
      const values = await form.validateFields();
      setIsSaving(true);
      
      // 如果是可编程变量，尝试执行脚本获取最新的值
      if (values.variable_type === 1) {
        try {
          // 创建环境变量对象
          const ENV: Record<string, string> = {};
          environmentVariables.forEach(v => {
            if (v.key_name) {
              ENV[v.key_name] = v.variable_type === 1 ? 
                ((v as any).last_execution_result || (v as any).scriptExecutionResult || '') : 
                v.value;
            }
          });

          // 执行脚本
          const scriptFunction = new Function('ENV', values.value);
          const result = scriptFunction(ENV);
          
          // 更新执行结果
          values.scriptExecutionResult = String(result);
          values.scriptExecutionTime = Date.now();
          values.executionStatus = 1; // 执行成功
        } catch (err) {
          console.error('保存前脚本执行错误:', err);
          values.executionStatus = 2; // 执行失败
          values.scriptExecutionResult = '';
        }
      }
      
      let result = null;
      
      // 判断是新增还是编辑
      if (values.id) {
        // 编辑 - 调用更新API
        result = await updateEnvironmentVariable({
          id: values.id,
          key_name: values.key_name,
          value: values.value,
          description: values.description,
          variable_type: values.variable_type || 0,
          status: 1
        });
      } else {
        // 新增 - 调用创建API
        result = await createEnvironmentVariable({
          app_id: deploy.appData.application?.id!,
          env_id: deploy.envId,
          key_name: values.key_name,
          value: values.value,
          description: values.description,
          variable_type: values.variable_type || 0,
          dependencies: values.dependencies || ''
        } as API.CreateEnvironmentVariableRequest);
      }
      
      if (result) {
        form.resetFields();
        message.success(values.id ? '环境变量已更新' : '环境变量已添加');
        onSaveSuccess(values);
      } else {
        message.error('保存失败');
      }
    } catch (error) {
      message.error('保存失败：' + (error as any).message);
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    updateEnvVariableTooltipView({
      scriptExecutionResult: null,
      scriptExecutionError: null,
      scriptExecutionLoading: false,
      executionStatus: null,
    });
    onCancel();
  };

  // 获取变量类型标签
  const getVariableTypeTag = (variableType?: number, executionStatus?: number) => {
    if (variableType !== 1) return null;
    return <Tooltip title="脚本变量"><CodeOutlined style={{ color: '#1890ff', marginLeft: 4 }} /></Tooltip>;
  };

  return (
    <Modal
      title={
        readOnly ? (
          <Space>
            环境变量详情
            {envVariableTooltipView?.variable_type === 1 && getVariableTypeTag(
              envVariableTooltipView.variable_type,
              envVariableTooltipView.executionStatus || 0
            )}
          </Space>
        ) : (
          envVariableTooltipView?.id && envVariableTooltipView.id > 0 ? "编辑环境变量" : "新增环境变量"
        )
      }
      open={visible}
      confirmLoading={loading || isSaving}
      onOk={handleModalSave}
      onCancel={handleCancel}
      okText="保存"
      cancelText="取消"
      width={800}
      footer={readOnly ? [
        <Button key="edit" type="primary" icon={<EditOutlined />} onClick={onSwitchToEdit} disabled={!onSwitchToEdit}>
          编辑
        </Button>,
        <Button key="close" onClick={handleCancel}>关闭</Button>
      ] : undefined}
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={envVariableTooltipView || {}}
        disabled={readOnly}
      >
        <Form.Item
          name="key_name"
          label="变量名"
          rules={[{ required: true, message: '请输入变量名' }]}
        >
          <Input placeholder="例如: PORT, DATABASE_URL" />
        </Form.Item>
        
        <Form.Item
          name="variable_type"
          label={
            <span>
              变量类型 
              <Tooltip title="普通变量直接设置值，可编程变量通过JavaScript脚本动态生成值">
                <QuestionCircleOutlined style={{ marginLeft: '4px' }} />
              </Tooltip>
            </span>
          }
          initialValue={0}
        >
          <Radio.Group 
            onChange={(e) => {
              updateEnvVariableTooltipView({
                variable_type: e.target.value,
              });
              if (e.target.value === 1 && !form.getFieldValue('value')) {
                form.setFieldsValue({ value: SCRIPT_TEMPLATE });
              }
            }}
          >
            <Radio value={0}>普通变量</Radio>
            <Radio value={1}>可编程变量</Radio>
          </Radio.Group>
        </Form.Item>

        {form.getFieldValue('variable_type') === 1 ? (
          <div>
            <div style={{ marginBottom: '8px', display: 'flex', justifyContent: 'space-between' }}>
              <Text type="secondary">编写JavaScript代码来动态生成环境变量的值</Text>
              {!readOnly && (
                <Button 
                  type="primary" 
                  size="small"
                  icon={<PlayCircleOutlined />}
                  onClick={executeScript}
                  loading={envVariableTooltipView?.scriptExecutionLoading}
                >
                  测试执行
                </Button>
              )}
            </div>
            
            <Form.Item
              name="value"
              noStyle
            >
              <div style={{ border: '1px solid #d9d9d9', borderRadius: '4px', height: '250px' }}>
                <Editor
                  height="250px"
                  defaultLanguage="javascript"
                  value={form.getFieldValue('value') || SCRIPT_TEMPLATE}
                  onChange={(value: string | undefined) => form.setFieldsValue({ value: value || '' })}
                  options={{
                    minimap: { enabled: false },
                    scrollBeyondLastLine: false,
                    readOnly: readOnly
                  }}
                />
              </div>
            </Form.Item>

            {/* 执行结果区域 */}
            {(envVariableTooltipView?.scriptExecutionResult || envVariableTooltipView?.scriptExecutionError) && (
              <div style={{ marginTop: '16px' }}>
                <Title level={5}>执行结果</Title>
                {envVariableTooltipView?.scriptExecutionResult && (
                  <div style={{ 
                    padding: '8px 12px', 
                    background: '#f6ffed', 
                    border: '1px solid #b7eb8f',
                    borderRadius: '4px',
                    marginBottom: '8px'
                  }}>
                    <Text><CheckCircleOutlined style={{ color: '#52c41a', marginRight: '8px' }} />结果: {envVariableTooltipView?.scriptExecutionResult}</Text>
                  </div>
                )}
                
                {envVariableTooltipView?.scriptExecutionError && (
                  <div style={{ 
                    padding: '8px 12px', 
                    background: '#fff2f0', 
                    border: '1px solid #ffccc7',
                    borderRadius: '4px'
                  }}>
                    <Text><CloseCircleOutlined style={{ color: '#ff4d4f', marginRight: '8px' }} />错误: {envVariableTooltipView?.scriptExecutionError}</Text>
                  </div>
                )}
              </div>
            )}

            <div style={{ marginTop: '16px' }}>
              <Text type="secondary">
                提示: 应用编译或者启动时会自动执行脚本并更新变量值
              </Text>
            </div>

            {/* 脚本帮助信息 */}
            {!readOnly && (
              <div style={{ marginTop: '16px' }}>
                <details>
                  <summary>
                    <Text strong>脚本编写帮助</Text>
                  </summary>
                  <div style={{ marginTop: '8px', padding: '12px', background: '#f5f5f5', borderRadius: '4px' }}>
                    <Paragraph style={{ whiteSpace: 'pre-line' }}>
                      {SCRIPT_HELP}
                    </Paragraph>
                  </div>
                </details>
              </div>
            )}
          </div>
        ) : (
          <Form.Item
            name="value"
            label="变量值"
            rules={[{ required: true, message: '请输入变量值' }]}
          >
            <Input.TextArea rows={4} placeholder="变量值" />
          </Form.Item>
        )}
        
        <Form.Item
          name="description"
          label="描述"
        >
          <Input placeholder="变量用途描述（可选）" />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default EnvVariableEditModal; 