// 编程环境变量示例代码
export const SCRIPT_TEMPLATE = `// 这是一个JavaScript环境变量示例
// 您可以编写逻辑来动态生成环境变量的值
// 示例: 根据当前时间生成值

function generateValue() {
  // 可以访问其他环境变量
  const appName = ENV.APP_NAME || 'my-app';
  const timestamp = new Date().getTime();
  
  // 返回生成的值
  return appName + '-' + timestamp;
}

// 必须返回一个值
return generateValue();`;

// 可编程环境变量的帮助信息
export const SCRIPT_HELP = `
# JavaScript可编程环境变量说明

## 基本规则
- 脚本必须返回一个字符串值作为环境变量的值
- 使用标准JavaScript语法
- 脚本在部署前会被执行，执行结果将成为实际的环境变量值

## 可用的全局对象
- \`ENV\`: 可访问其他环境变量，如 ENV.DATABASE_URL
- \`Date\`: 标准日期对象，用于时间处理
- \`JSON\`: 用于JSON解析和序列化

## 示例用途
- 基于时间戳生成唯一标识
- 组合多个现有环境变量
- 条件判断生成不同的配置值
- 进行简单的数据转换

## 注意事项
- 脚本执行有5秒超时限制
- 不支持外部网络请求
- 不支持文件系统操作
`; 