import React from 'react';
import { Modal, Progress, List, Typography, Tag, Space, Spin } from 'antd';
import { CheckCircleOutlined, CloseCircleOutlined, LoadingOutlined } from '@ant-design/icons';
import { RestartResult, RestartFailure } from '../hooks/useRestartPods';

const { Text, Title } = Typography;

export interface RestartProgressIndicatorProps {
  open: boolean;
  loading: boolean;
  result: RestartResult | null;
  onClose: () => void;
}

const RestartProgressIndicator: React.FC<RestartProgressIndicatorProps> = ({
  open,
  loading,
  result,
  onClose,
}) => {
  const getProgressPercent = () => {
    if (!result) return 0;
    if (result.total_pods === 0) return 100;
    return Math.round(((result.successful_pods + result.failed_pods.length) / result.total_pods) * 100);
  };

  const getProgressStatus = (): "normal" | "active" | "success" | "exception" => {
    if (loading) return "active";
    if (!result) return "normal";
    if (result.failed_pods.length > 0) return "exception";
    return "success";
  };

  const formatDuration = (duration: string) => {
    // 简单的时间格式化
    if (duration.includes('ms')) {
      return duration;
    }
    if (duration.includes('s')) {
      return duration;
    }
    return duration;
  };

  return (
    <Modal
      title={
        <Space>
          {loading ? <LoadingOutlined spin /> : null}
          Pod重启进度
        </Space>
      }
      open={open}
      onCancel={onClose}
      footer={loading ? null : [
        <button
          key="close"
          onClick={onClose}
          style={{
            padding: '6px 16px',
            backgroundColor: '#1890ff',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer',
          }}
        >
          关闭
        </button>
      ]}
      width={600}
      maskClosable={!loading}
    >
      <div style={{ padding: '16px 0' }}>
        {loading ? (
          <div style={{ textAlign: 'center', padding: '40px 0' }}>
            <Spin size="large" />
            <div style={{ marginTop: '16px' }}>
              <Text>正在重启Pod，请稍候...</Text>
            </div>
          </div>
        ) : result ? (
          <>
            {/* 进度条 */}
            <div style={{ marginBottom: '24px' }}>
              <Progress
                percent={getProgressPercent()}
                status={getProgressStatus()}
                strokeColor={result.failed_pods.length > 0 ? '#ff4d4f' : '#52c41a'}
              />
            </div>

            {/* 统计信息 */}
            <div style={{ marginBottom: '24px' }}>
              <Space wrap>
                <Tag color="blue">总计: {result.total_pods}</Tag>
                <Tag color="green">成功: {result.successful_pods}</Tag>
                <Tag color="red">失败: {result.failed_pods.length}</Tag>
                {result.duration && (
                  <Tag color="default">耗时: {formatDuration(result.duration)}</Tag>
                )}
              </Space>
            </div>

            {/* 结果消息 */}
            <div style={{ marginBottom: '16px' }}>
              <Text strong>{result.message}</Text>
            </div>

            {/* 失败详情 */}
            {result.failed_pods.length > 0 && (
              <div>
                <Title level={5} style={{ color: '#ff4d4f', marginBottom: '12px' }}>
                  <CloseCircleOutlined /> 重启失败的Pod
                </Title>
                <List
                  size="small"
                  bordered
                  dataSource={result.failed_pods}
                  renderItem={(item: RestartFailure) => (
                    <List.Item>
                      <div style={{ width: '100%' }}>
                        <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>
                          {item.pod_name}
                        </div>
                        <div style={{ fontSize: '12px', color: '#666' }}>
                          原因: {item.reason}
                        </div>
                        <div style={{ fontSize: '12px', color: '#ff4d4f' }}>
                          错误: {item.error}
                        </div>
                      </div>
                    </List.Item>
                  )}
                />
              </div>
            )}

            {/* 成功提示 */}
            {result.failed_pods.length === 0 && result.successful_pods > 0 && (
              <div style={{ 
                textAlign: 'center', 
                padding: '20px',
                backgroundColor: '#f6ffed',
                border: '1px solid #b7eb8f',
                borderRadius: '6px'
              }}>
                <CheckCircleOutlined style={{ color: '#52c41a', fontSize: '24px' }} />
                <div style={{ marginTop: '8px' }}>
                  <Text style={{ color: '#52c41a', fontWeight: 'bold' }}>
                    所有Pod重启成功！
                  </Text>
                </div>
              </div>
            )}
          </>
        ) : (
          <div style={{ textAlign: 'center', padding: '40px 0' }}>
            <Text type="secondary">暂无重启信息</Text>
          </div>
        )}
      </div>
    </Modal>
  );
};

export default RestartProgressIndicator; 