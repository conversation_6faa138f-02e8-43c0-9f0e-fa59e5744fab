import React from 'react';
import { Modal, Radio, Space, Typography, Alert } from 'antd';
import { ExclamationCircleOutlined } from '@ant-design/icons';

const { Text } = Typography;

export interface RestartConfirmModalProps {
  open: boolean;
  type: 'single' | 'all';
  podName?: string;
  namespace: string;
  totalPods?: number;
  strategy: 'graceful' | 'force';
  onStrategyChange: (strategy: 'graceful' | 'force') => void;
  onConfirm: () => void;
  onCancel: () => void;
  loading?: boolean;
}

const RestartConfirmModal: React.FC<RestartConfirmModalProps> = ({
  open,
  type,
  podName,
  namespace,
  totalPods,
  strategy,
  onStrategyChange,
  onConfirm,
  onCancel,
  loading = false,
}) => {
  const title = type === 'single' 
    ? `确认重启Pod - ${podName}` 
    : `确认重启所有Pod`;

  const content = type === 'single'
    ? `确定要重启Pod "${podName}" 吗？`
    : `确定要重启命名空间 "${namespace}" 下的所有Pod吗？${totalPods ? `这将影响 ${totalPods} 个Pod的服务可用性。` : ''}`;

  const getStrategyDescription = (selectedStrategy: 'graceful' | 'force') => {
    if (selectedStrategy === 'graceful') {
      return '优雅重启：等待Pod处理完现有请求后再停止（推荐）';
    } else {
      return '强制重启：立即停止Pod，可能导致正在处理的请求丢失';
    }
  };

  return (
    <Modal
      title={
        <Space>
          <ExclamationCircleOutlined style={{ color: '#faad14' }} />
          {title}
        </Space>
      }
      open={open}
      onOk={onConfirm}
      onCancel={onCancel}
      okText="确认重启"
      cancelText="取消"
      okButtonProps={{
        danger: true,
        loading,
      }}
      cancelButtonProps={{
        disabled: loading,
      }}
      width={500}
      maskClosable={false}
    >
      <div style={{ padding: '16px 0' }}>
        <Text>{content}</Text>
        
        {type === 'all' && (
          <Alert
            message="批量重启警告"
            description="批量重启将会影响服务的可用性，请确保在合适的时间窗口执行此操作。"
            type="warning"
            style={{ margin: '16px 0' }}
            showIcon
          />
        )}

        <div style={{ marginTop: '16px' }}>
          <Text strong>重启策略：</Text>
          <div style={{ marginTop: '8px' }}>
            <Radio.Group
              value={strategy}
              onChange={(e) => onStrategyChange(e.target.value)}
              disabled={loading}
            >
              <Space direction="vertical">
                <Radio value="graceful">
                  <Text>优雅重启（推荐）</Text>
                </Radio>
                <Radio value="force">
                  <Text>强制重启</Text>
                </Radio>
              </Space>
            </Radio.Group>
          </div>
          
          <div style={{ 
            marginTop: '8px', 
            padding: '8px', 
            backgroundColor: '#f6f8fa', 
            borderRadius: '4px',
            fontSize: '12px',
            color: '#6a737d'
          }}>
            {getStrategyDescription(strategy)}
          </div>
        </div>

        <div style={{ 
          marginTop: '16px',
          padding: '12px',
          backgroundColor: '#fff7e6',
          border: '1px solid #ffd591',
          borderRadius: '4px',
          fontSize: '12px'
        }}>
          <Text strong>注意事项：</Text>
          <ul style={{ margin: '4px 0 0 16px', padding: 0 }}>
            <li>重启操作将删除Pod，Kubernetes会自动重建</li>
            <li>重启过程中服务可能短暂不可用</li>
            <li>请确保在业务低峰期执行重启操作</li>
          </ul>
        </div>
      </div>
    </Modal>
  );
};

export default RestartConfirmModal; 