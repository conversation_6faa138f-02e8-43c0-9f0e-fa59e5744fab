import { useState, useCallback } from 'react';
import { message } from 'antd';
import { startArthas as startArthasAPI, stopArthas as stopArthasAPI } from '@/services/api';

// 使用API命名空间中的类型
export type ArthasStartRequest = API.ArthasStartRequest;

export interface UseArthasProps {
  onInstanceChange?: (instances: Set<string>) => void;
  defaultContainerName?: string;
  defaultPort?: number;
}

export const useArthas = ({ 
  onInstanceChange,
  defaultContainerName = 'app',
  defaultPort = 3658
}: UseArthasProps = {}) => {
  const [runningInstances, setRunningInstances] = useState<Set<string>>(new Set());
  const [loading, setLoading] = useState<Set<string>>(new Set());

  // 更新运行实例并通知父组件
  const updateRunningInstances = useCallback((newInstances: Set<string>) => {
    setRunningInstances(newInstances);
    if (onInstanceChange) {
      onInstanceChange(newInstances);
    }
  }, [onInstanceChange]);

  // 添加加载状态
  const addLoading = useCallback((podName: string) => {
    setLoading(prev => new Set([...prev, podName]));
  }, []);

  // 移除加载状态
  const removeLoading = useCallback((podName: string) => {
    setLoading(prev => {
      const newSet = new Set(prev);
      newSet.delete(podName);
      return newSet;
    });
  }, []);

  // 启动Arthas
  const startArthas = useCallback(async (
    namespace: string, 
    podName: string, 
    options?: { containerName?: string; port?: number }
  ) => {
    if (runningInstances.has(podName)) {
      message.warning(`Pod ${podName} 的Arthas已在运行中`);
      return false;
    }

    try {
      addLoading(podName);

      const request: ArthasStartRequest = {
        namespace,
        pod_name: podName,
        container_name: options?.containerName || defaultContainerName,
        port: options?.port || defaultPort,
      };

      const response = await startArthasAPI(request);

      if (response.success && response.data) {
        message.success(`Arthas启动成功 - ${podName}`);
        const newInstances = new Set([...runningInstances, podName]);
        updateRunningInstances(newInstances);
        return true;
      } else {
        throw new Error(response.errorMessage || '启动Arthas失败');
      }
    } catch (error: any) {
      console.error('启动Arthas失败:', error);
      const errorMessage = error.message || error.errorMessage || '启动Arthas失败';
      message.error(`启动Arthas失败 - ${podName}: ${errorMessage}`);
      return false;
    } finally {
      removeLoading(podName);
    }
  }, [runningInstances, addLoading, removeLoading, updateRunningInstances, defaultContainerName, defaultPort]);

  // 停止Arthas
  const stopArthas = useCallback(async (namespace: string, podName: string) => {
    if (!runningInstances.has(podName)) {
      message.warning(`Pod ${podName} 的Arthas未在运行`);
      return false;
    }

    try {
      addLoading(podName);

      const response = await stopArthasAPI({ namespace, podName });

      if (response.success) {
        message.success(`Arthas停止成功 - ${podName}`);
        const newInstances = new Set(runningInstances);
        newInstances.delete(podName);
        updateRunningInstances(newInstances);
        return true;
      } else {
        throw new Error(response.errorMessage || '停止Arthas失败');
      }
    } catch (error: any) {
      console.error('停止Arthas失败:', error);
      const errorMessage = error.message || error.errorMessage || '停止Arthas失败';
      message.error(`停止Arthas失败 - ${podName}: ${errorMessage}`);
      return false;
    } finally {
      removeLoading(podName);
    }
  }, [runningInstances, addLoading, removeLoading, updateRunningInstances]);

  // 批量停止所有Arthas实例
  const stopAllArthas = useCallback(async (namespace: string) => {
    const instances = Array.from(runningInstances);
    if (instances.length === 0) {
      message.info('没有正在运行的Arthas实例');
      return;
    }

    const results = await Promise.allSettled(
      instances.map(podName => stopArthas(namespace, podName))
    );

    const successful = results.filter(result => result.status === 'fulfilled' && result.value).length;
    const failed = results.length - successful;

    if (failed === 0) {
      message.success(`成功停止所有 ${successful} 个Arthas实例`);
    } else {
      message.warning(`停止了 ${successful} 个实例，${failed} 个实例停止失败`);
    }
  }, [runningInstances, stopArthas]);

  // 检查Pod是否正在运行Arthas
  const isRunning = useCallback((podName: string) => {
    return runningInstances.has(podName);
  }, [runningInstances]);

  // 检查Pod是否正在加载中
  const isLoading = useCallback((podName: string) => {
    return loading.has(podName);
  }, [loading]);

  // 重置所有状态
  const reset = useCallback(() => {
    setRunningInstances(new Set());
    setLoading(new Set());
    if (onInstanceChange) {
      onInstanceChange(new Set());
    }
  }, [onInstanceChange]);

  // 手动设置运行实例（用于初始化或同步状态）
  const setInstances = useCallback((instances: Set<string>) => {
    updateRunningInstances(instances);
  }, [updateRunningInstances]);

  return {
    // 状态
    runningInstances,
    loading,
    runningCount: runningInstances.size,
    
    // 方法
    startArthas,
    stopArthas,
    stopAllArthas,
    isRunning,
    isLoading,
    reset,
    setInstances,
  };
}; 