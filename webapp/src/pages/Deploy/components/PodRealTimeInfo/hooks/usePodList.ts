import { useState, useEffect, useCallback } from 'react';
import { message } from 'antd';
import { getPodList } from '@/services/api';

// 使用API命名空间中的Pod类型
export type Pod = API.Pod;

export interface UsePodListProps {
  namespace: string;
  enabled: boolean; // 是否启用数据获取
  autoRefresh?: boolean; // 是否自动刷新
  refreshInterval?: number; // 刷新间隔（毫秒）
}

export const usePodList = ({ 
  namespace, 
  enabled, 
  autoRefresh = true, 
  refreshInterval = 10000 
}: UsePodListProps) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [pods, setPods] = useState<Pod[]>([]);
  const [error, setError] = useState<string>('');

  // 获取Pod列表
  const fetchPodList = useCallback(async () => {
    if (!namespace) return;
    
    try {
      setLoading(true);
      setError('');
      
      const response = await getPodList({ namespace });
      
      if (response.success && response.data) {
        setPods(response.data.pods || []);
      } else {
        throw new Error(response.errorMessage || '获取Pod列表失败');
      }
    } catch (err: any) {
      console.error('获取Pod列表失败:', err);
      const errorMessage = err.message || err.errorMessage || '获取Pod列表失败';
      setError(errorMessage);
      message.error(`获取Pod列表失败: ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  }, [namespace]);

  // 手动刷新
  const refresh = useCallback(() => {
    fetchPodList();
  }, [fetchPodList]);

  // 重置状态
  const reset = useCallback(() => {
    setPods([]);
    setError('');
    setLoading(false);
  }, []);

  // 获取特定状态的Pod数量
  const getStatusCount = useCallback((status: string) => {
    return pods.filter(pod => pod.status === status).length;
  }, [pods]);

  // 获取统计信息
  const stats = {
    total: pods.length,
    running: getStatusCount('Running'),
    pending: getStatusCount('Pending'),
    failed: getStatusCount('Failed'),
    succeeded: getStatusCount('Succeeded'),
  };

  // 组件挂载时获取Pod列表
  useEffect(() => {
    if (enabled && namespace) {
      fetchPodList();
    } else if (!enabled) {
      reset();
    }
  }, [enabled, namespace, fetchPodList, reset]);

  // 定时刷新Pod状态
  useEffect(() => {
    if (!enabled || !autoRefresh) return;
    
    const timer = setInterval(() => {
      fetchPodList();
    }, refreshInterval);
    
    return () => clearInterval(timer);
  }, [enabled, autoRefresh, refreshInterval, fetchPodList]);

  return {
    // 数据
    pods,
    loading,
    error,
    stats,
    
    // 方法
    refresh,
    reset,
    fetchPodList,
    getStatusCount,
  };
}; 