import { useState } from 'react';
import { message } from 'antd';
import { restartPods as restartPodsAPI } from '@/services/api';

// 使用API命名空间中的类型
export type RestartPodsRequest = API.RestartRequest;
export type RestartResult = API.RestartResult;

export interface RestartFailure {
  pod_name: string;
  reason: string;
  error: string;
}

export const useRestartPods = () => {
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<RestartResult | null>(null);

  const restartPods = async (request: RestartPodsRequest): Promise<RestartResult | null> => {
    try {
      setLoading(true);
      setResult(null);

      // 设置默认值
      const requestWithDefaults: RestartPodsRequest = {
        ...request,
        strategy: request.strategy || 'graceful',
        config: request.config || {
          timeout: 300, // 5分钟（秒）
          max_concurrent: 3,
        },
      };

      const response = await restartPodsAPI(requestWithDefaults);

      if (response.success && response.data) {
        const restartResult = response.data;
        setResult(restartResult);

        // 显示结果消息
        if (restartResult.failed_pods && restartResult.failed_pods.length > 0) {
          message.warning(restartResult.message);
        } else {
          message.success(restartResult.message);
        }

        return restartResult;
      } else {
        throw new Error(response.errorMessage || '重启Pod失败');
      }
    } catch (error: any) {
      console.error('重启Pod失败:', error);
      const errorMessage = error.message || error.errorMessage || '重启Pod失败';
      message.error(`重启Pod失败: ${errorMessage}`);
      return null;
    } finally {
      setLoading(false);
    }
  };

  const restartSinglePod = async (namespace: string, podName: string, strategy: 'graceful' | 'force' = 'graceful') => {
    return restartPods({
      namespace,
      pod_names: [podName],
      strategy,
    });
  };

  const restartAllPods = async (namespace: string, strategy: 'graceful' | 'force' = 'graceful') => {
    return restartPods({
      namespace,
      pod_names: [], // 空数组表示重启所有Pod
      strategy,
    });
  };

  const clearResult = () => {
    setResult(null);
  };

  return {
    loading,
    result,
    restartPods,
    restartSinglePod,
    restartAllPods,
    clearResult,
  };
}; 