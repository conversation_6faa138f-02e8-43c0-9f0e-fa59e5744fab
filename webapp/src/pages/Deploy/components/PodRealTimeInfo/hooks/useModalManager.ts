import { useState, useCallback } from 'react';

export type ModalType = 
  | 'terminal' 
  | 'arthas' 
  | 'restartConfirm' 
  | 'restartProgress';

export interface ModalState {
  [key: string]: {
    visible: boolean;
    data?: any;
  };
}

export interface UseModalManagerProps {
  initialState?: ModalState;
}

export const useModalManager = ({ initialState = {} }: UseModalManagerProps = {}) => {
  const [modals, setModals] = useState<ModalState>(initialState);

  // 打开模态框
  const openModal = useCallback((type: ModalType, data?: any) => {
    setModals(prev => ({
      ...prev,
      [type]: {
        visible: true,
        data,
      },
    }));
  }, []);

  // 关闭模态框
  const closeModal = useCallback((type: ModalType) => {
    setModals(prev => ({
      ...prev,
      [type]: {
        ...prev[type],
        visible: false,
      },
    }));
  }, []);

  // 切换模态框状态
  const toggleModal = useCallback((type: ModalType, data?: any) => {
    setModals(prev => {
      const currentModal = prev[type];
      const isVisible = currentModal?.visible || false;
      
      return {
        ...prev,
        [type]: {
          visible: !isVisible,
          data: !isVisible ? data : currentModal?.data,
        },
      };
    });
  }, []);

  // 更新模态框数据（不改变可见性）
  const updateModalData = useCallback((type: ModalType, data: any) => {
    setModals(prev => ({
      ...prev,
      [type]: {
        ...prev[type],
        data,
      },
    }));
  }, []);

  // 关闭所有模态框
  const closeAllModals = useCallback(() => {
    setModals(prev => {
      const updated: ModalState = {};
      Object.keys(prev).forEach(key => {
        updated[key] = {
          ...prev[key],
          visible: false,
        };
      });
      return updated;
    });
  }, []);

  // 重置所有模态框状态
  const resetModals = useCallback(() => {
    setModals({});
  }, []);

  // 检查模态框是否可见
  const isModalVisible = useCallback((type: ModalType) => {
    return modals[type]?.visible || false;
  }, [modals]);

  // 获取模态框数据
  const getModalData = useCallback((type: ModalType) => {
    return modals[type]?.data;
  }, [modals]);

  // 获取可见的模态框数量
  const visibleModalCount = Object.values(modals).filter(modal => modal.visible).length;

  // 检查是否有模态框可见
  const hasVisibleModal = visibleModalCount > 0;

  // 获取所有可见的模态框类型
  const visibleModalTypes = Object.keys(modals).filter(type => 
    modals[type]?.visible
  ) as ModalType[];

  return {
    // 状态
    modals,
    visibleModalCount,
    hasVisibleModal,
    visibleModalTypes,

    // 模态框操作
    openModal,
    closeModal,
    toggleModal,
    updateModalData,
    closeAllModals,
    resetModals,

    // 查询方法
    isModalVisible,
    getModalData,

    // 便捷方法 - 终端
    openTerminal: (data?: any) => openModal('terminal', data),
    closeTerminal: () => closeModal('terminal'),
    isTerminalVisible: () => isModalVisible('terminal'),
    getTerminalData: () => getModalData('terminal'),

    // 便捷方法 - Arthas
    openArthas: (data?: any) => openModal('arthas', data),
    closeArthas: () => closeModal('arthas'),
    isArthasVisible: () => isModalVisible('arthas'),
    getArthasData: () => getModalData('arthas'),

    // 便捷方法 - 重启确认
    openRestartConfirm: (data?: any) => openModal('restartConfirm', data),
    closeRestartConfirm: () => closeModal('restartConfirm'),
    isRestartConfirmVisible: () => isModalVisible('restartConfirm'),
    getRestartConfirmData: () => getModalData('restartConfirm'),

    // 便捷方法 - 重启进度
    openRestartProgress: (data?: any) => openModal('restartProgress', data),
    closeRestartProgress: () => closeModal('restartProgress'),
    isRestartProgressVisible: () => isModalVisible('restartProgress'),
    getRestartProgressData: () => getModalData('restartProgress'),
  };
}; 