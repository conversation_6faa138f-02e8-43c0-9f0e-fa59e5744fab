import React from 'react';
import { But<PERSON>, <PERSON>, Tag, Tooltip, Typography } from 'antd';
import { Pod } from './usePodList';

const { Text } = Typography;

export interface PodTableActions {
  onOpenTerminal: (podName: string) => void;
  onStartArthas: (podName: string) => void;
  onStopArthas: (podName: string) => void;
  onOpenArthasConsole: (podName: string) => void;
  onRestartPod: (podName: string) => void;
}

export interface UsePodTableColumnsProps {
  actions: PodTableActions;
  runningArthasInstances: Set<string>;
  arthasLoading?: Set<string>;
}

export const usePodTableColumns = ({ 
  actions, 
  runningArthasInstances, 
  arthasLoading = new Set() 
}: UsePodTableColumnsProps) => {
  
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Running': return 'green';
      case 'Pending': return 'orange';
      case 'Failed': return 'red';
      case 'Succeeded': return 'blue';
      default: return 'default';
    }
  };

  const columns = [
    {
      title: 'Pod名称',
      dataIndex: 'name',
      key: 'name',
      width: 200,
      fixed: 'left' as const,
      render: (text: string) => (
        <Tooltip title={text}>
          <Text style={{ fontSize: '12px' }} ellipsis>
            {text}
          </Text>
        </Tooltip>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => (
        <Tag color={getStatusColor(status)} style={{ fontSize: '11px' }}>
          {status}
        </Tag>
      ),
    },
    {
      title: '就绪',
      dataIndex: 'ready',
      key: 'ready',
      width: 80,
      render: (ready: string) => (
        <Text style={{ fontSize: '12px' }}>{ready}</Text>
      ),
    },
    {
      title: '重启次数',
      dataIndex: 'restarts',
      key: 'restarts',
      width: 90,
      render: (restarts: number) => (
        <Text style={{ fontSize: '12px' }}>{restarts}</Text>
      ),
    },
    {
      title: '运行时间',
      dataIndex: 'age',
      key: 'age',
      width: 100,
      render: (age: string) => (
        <Text style={{ fontSize: '12px' }}>{age}</Text>
      ),
    },
    {
      title: 'IP地址',
      dataIndex: 'ip',
      key: 'ip',
      width: 120,
      render: (ip: string) => (
        <Text style={{ fontSize: '12px' }} copyable={{ text: ip }}>
          {ip}
        </Text>
      ),
    },
    {
      title: '节点',
      dataIndex: 'node',
      key: 'node',
      width: 150,
      render: (text: string) => (
        <Tooltip title={text}>
          <Text style={{ fontSize: '12px' }} ellipsis>
            {text}
          </Text>
        </Tooltip>
      ),
    },
    {
      title: 'CPU',
      dataIndex: 'cpu',
      key: 'cpu',
      width: 80,
      render: (cpu: string) => (
        <Text style={{ fontSize: '12px' }}>{cpu || '-'}</Text>
      ),
    },
    {
      title: '内存',
      dataIndex: 'memory',
      key: 'memory',
      width: 80,
      render: (memory: string) => (
        <Text style={{ fontSize: '12px' }}>{memory || '-'}</Text>
      ),
    },
    {
      title: '操作',
      key: 'actions',
      width: 200,
      fixed: 'right' as const,
      render: (_: any, record: Pod) => {
        const isArthasRunning = runningArthasInstances.has(record.name);
        const isArthasLoading = arthasLoading.has(record.name);
        const isPodRunning = record.status === 'Running';

        return (
          <Space size="small">
            {/* 终端按钮 */}
            <Button
              type="text"
              size="small"
              onClick={() => actions.onOpenTerminal(record.name)}
              title="打开终端"
              disabled={!isPodRunning}
            >
              终端
            </Button>

            {/* Arthas相关按钮 */}
            {isArthasRunning ? (
              <>
                <Button
                  type="text"
                  size="small"
                  onClick={() => actions.onOpenArthasConsole(record.name)}
                  title="打开Arthas控制台"
                  disabled={isArthasLoading}
                >
                  控制台
                </Button>
                <Button
                  type="text"
                  size="small"
                  danger
                  onClick={() => actions.onStopArthas(record.name)}
                  title="停止Arthas"
                  loading={isArthasLoading}
                  disabled={isArthasLoading}
                >
                  停止
                </Button>
              </>
            ) : (
              <Button
                type="text"
                size="small"
                onClick={() => actions.onStartArthas(record.name)}
                title="启动Arthas"
                loading={isArthasLoading}
                disabled={!isPodRunning || isArthasLoading}
              >
                Arthas
              </Button>
            )}

            {/* 重启按钮 */}
            <Button
              type="text"
              size="small"
              danger
              onClick={() => actions.onRestartPod(record.name)}
              title="重启Pod"
            >
              重启
            </Button>
          </Space>
        );
      },
    },
  ];

  // 简化的列配置（用于移动端或空间有限的场景）
  const compactColumns = [
    {
      title: 'Pod信息',
      key: 'podInfo',
      render: (_: any, record: Pod) => (
        <div>
          <div style={{ fontWeight: 'bold', fontSize: '12px' }}>
            {record.name}
          </div>
          <div style={{ fontSize: '10px', color: '#666' }}>
            <Tag color={getStatusColor(record.status)} style={{ fontSize: '10px' }}>
              {record.status}
            </Tag>
            <span style={{ marginLeft: 8 }}>
              {record.ready} | {record.restarts}次重启
            </span>
          </div>
        </div>
      ),
    },
    {
      title: '资源',
      key: 'resources',
      width: 120,
      render: (_: any, record: Pod) => (
        <div style={{ fontSize: '10px' }}>
          <div>CPU: {record.cpu || '-'}</div>
          <div>内存: {record.memory || '-'}</div>
          <div>节点: {record.node}</div>
        </div>
      ),
    },
    {
      title: '操作',
      key: 'actions',
      width: 120,
      render: (_: any, record: Pod) => {
        const isArthasRunning = runningArthasInstances.has(record.name);
        const isArthasLoading = arthasLoading.has(record.name);
        const isPodRunning = record.status === 'Running';

        return (
          <Space size="small" direction="vertical">
            <Space size="small">
              <Button
                type="text"
                size="small"
                onClick={() => actions.onOpenTerminal(record.name)}
                disabled={!isPodRunning}
              >
                终端
              </Button>
              <Button
                type="text"
                size="small"
                danger
                onClick={() => actions.onRestartPod(record.name)}
              >
                重启
              </Button>
            </Space>
            {isArthasRunning ? (
              <Space size="small">
                <Button
                  type="text"
                  size="small"
                  onClick={() => actions.onOpenArthasConsole(record.name)}
                  disabled={isArthasLoading}
                >
                  控制台
                </Button>
                <Button
                  type="text"
                  size="small"
                  onClick={() => actions.onStopArthas(record.name)}
                  loading={isArthasLoading}
                >
                  停止
                </Button>
              </Space>
            ) : (
              <Button
                type="text"
                size="small"
                onClick={() => actions.onStartArthas(record.name)}
                loading={isArthasLoading}
                disabled={!isPodRunning}
              >
                Arthas
              </Button>
            )}
          </Space>
        );
      },
    },
  ];

  return {
    columns,
    compactColumns,
    getStatusColor,
  };
}; 