export interface PodInfo {
  id: string;
  name: string;
  namespace: string;
  status: 'Running' | 'Pending' | 'Failed' | 'Succeeded' | 'Unknown';
  restarts: number;
  age: string;
  ready: string;
  ip: string;
  node: string;
  cpu: string;
  memory: string;
  createTime: string;
}

export interface PodRealTimeInfoProps {
  runningArthasInstances: Set<string>;
  podInfoVisible: boolean;
  onToggleVisible: () => void;
  onClearArthas: () => void;
  setRunningArthasInstances?: (instances: Set<string>) => void;
} 