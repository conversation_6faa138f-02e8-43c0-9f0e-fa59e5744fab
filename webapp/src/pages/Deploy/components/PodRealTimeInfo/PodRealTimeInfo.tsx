import React from 'react';
import { Card, Table, Button, Space, Typography, Modal } from 'antd';
import { UpOutlined, DownOutlined, SyncOutlined, ReloadOutlined, ThunderboltOutlined } from '@ant-design/icons';
import TerminalWebConsole from './TerminalWebConsole';
import ArthasWebConsole from './ArthasWebConsole';
import { useRestartPods } from './hooks/useRestartPods';
import RestartConfirmModal from './components/RestartConfirmModal';
import RestartProgressIndicator from './components/RestartProgressIndicator';

// 使用新的hooks
import { 
  usePodList, 
  useArthas, 
  useModalManager, 
  usePodTableColumns,
  type Pod,
  type PodTableActions
} from './hooks';


interface PodRealTimeInfoProps {
  podInfoVisible: boolean;
  onToggleVisible: () => void;
}

const PodRealTimeInfo: React.FC<PodRealTimeInfoProps> = ({
  podInfoVisible,
  onToggleVisible,
}) => {
  // Pod命名空间设置（固定为default）
  const namespace = 'default';

  // 使用Pod列表管理Hook
  const {
    pods,
    loading: podLoading,
    error: podError,
    refresh: refreshPods, 
  } = usePodList({
    namespace,
    enabled: podInfoVisible,
    autoRefresh: true,
    refreshInterval: 10000,
  });

  // 使用Arthas管理Hook
  const {
    runningInstances: localArthasInstances,
    loading: arthasLoadingSet,
    startArthas,
    stopArthas,
    setInstances: setLocalArthasInstances,
  } = useArthas({
    defaultContainerName: 'app',
    defaultPort: 3658,
  });

  // 使用模态框管理Hook
  const {
    openTerminal,
    closeTerminal,
    isTerminalVisible,
    getTerminalData,
    openArthas,
    closeArthas,
    isArthasVisible,
    getArthasData,
    openRestartConfirm,
    closeRestartConfirm,
    isRestartConfirmVisible,
    getRestartConfirmData,
    openRestartProgress,
    closeRestartProgress,
    isRestartProgressVisible,
    getRestartProgressData,
    updateModalData,
  } = useModalManager();

  // 使用重启功能Hook
  const {
    loading: restartLoading,
    result: restartResult,
    restartSinglePod,
    restartAllPods,
    clearResult,
  } = useRestartPods();



  // 重启单个Pod
  const handleRestartSinglePod = (podName: string) => {
    openRestartConfirm({
      type: 'single',
      podName: podName,
      strategy: 'graceful',
    });
  };

  // 重启所有Pod
  const handleRestartAllPods = () => {
    openRestartConfirm({
      type: 'all',
      podName: '',
      strategy: 'graceful',
    });
  };

  // 确认重启
  const handleConfirmRestart = async () => {
    const confirmData = getRestartConfirmData();
    if (!confirmData) return;
    
    const strategy = confirmData.strategy || 'graceful';
    closeRestartConfirm();
    openRestartProgress({
      type: confirmData.type,
      podName: confirmData.podName,
      strategy,
    });
    clearResult();

    try {
      if (confirmData.type === 'single') {
        await restartSinglePod(namespace, confirmData.podName, strategy);
      } else {
        await restartAllPods(namespace, strategy);
      }
      
      // 重启完成后刷新Pod列表
      setTimeout(() => {
        refreshPods();
      }, 2000);
    } catch (error) {
      console.error('重启操作失败:', error);
    }
  };

  // 更新重启策略
  const handleStrategyChange = (strategy: 'graceful' | 'force') => {
    const confirmData = getRestartConfirmData();
    if (confirmData) {
      updateModalData('restartConfirm', {
        ...confirmData,
        strategy,
      });
    }
  };

  // 取消重启
  const handleCancelRestart = () => {
    closeRestartConfirm();
  };

  // 关闭进度指示器
  const handleCloseProgress = () => {
    closeRestartProgress();
    clearResult();
  };

  // 处理终端操作
  const handleOpenTerminal = (podName: string) => {
    openTerminal({ podName, namespace });
  };

  // 处理Arthas操作
  const handleStartArthas = async (podName: string) => {
    const success = await startArthas(namespace, podName);
    if (success) {
      openArthas({ podName, namespace });
    }
  };

  const handleStopArthas = async (podName: string) => {
    await stopArthas(namespace, podName);
  };

  const handleOpenArthasConsole = (podName: string) => {
    openArthas({ podName, namespace });
  };

  // 表格操作配置
  const tableActions: PodTableActions = {
    onOpenTerminal: handleOpenTerminal,
    onStartArthas: handleStartArthas,
    onStopArthas: handleStopArthas,
    onOpenArthasConsole: handleOpenArthasConsole,
    onRestartPod: handleRestartSinglePod,
  };

  // 使用表格列配置Hook
  const { columns } = usePodTableColumns({
    actions: tableActions,
    runningArthasInstances: localArthasInstances,
    arthasLoading: arthasLoadingSet,
  });

  // 渲染模态框组件
  const renderTerminalModal = () => {
    const terminalData = getTerminalData();
    if (!terminalData) return null;
    
    return (
      <Modal
        title={`Pod终端 - ${terminalData.podName}`}
        open={isTerminalVisible()}
        onCancel={closeTerminal}
        footer={null}
        width={1200}
        height={600}
        styles={{
          body: { height: '500px', padding: 0 }
        }}
      >
        <TerminalWebConsole 
          namespace={terminalData.namespace}
          podName={terminalData.podName}
          visible={isTerminalVisible()}
          onClose={closeTerminal}
        />
      </Modal>
    );
  };

  const renderArthasModal = () => {
    const arthasData = getArthasData();
    if (!arthasData) return null;
    
    return (
      <Modal
        title={`Arthas控制台 - ${arthasData.podName}`}
        open={isArthasVisible()}
        onCancel={closeArthas}
        footer={null}
        width={1400}
        height={700}
        styles={{
          body: { height: '600px', padding: 0 }
        }}
      >
        <ArthasWebConsole
          namespace={arthasData.namespace}
          podName={arthasData.podName}
          visible={isArthasVisible()}
          onClose={closeArthas}
        />
      </Modal>
    );
  };



  return (
    <>
      <Card
        title={
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <span>Pod实时信息</span>
            <Space>
              <Button
                type="text"
                size="small"
                icon={<ReloadOutlined />}
                onClick={handleRestartAllPods}
                title="重启所有Pod"
                danger
              >
                重启所有
              </Button>
              <Button
                type="text"
                size="small"
                icon={<SyncOutlined spin={podLoading} />}
                onClick={refreshPods}
                title="刷新Pod列表"
                loading={podLoading}
                disabled={podLoading}
              />
              <Button
                type="text"
                size="small"
                icon={podInfoVisible ? <UpOutlined /> : <DownOutlined />}
                onClick={onToggleVisible}
                title={podInfoVisible ? '收起' : '展开'}
              />
            </Space>
          </div>
        }
        size="small"
        style={{ marginTop: '16px' }}
      >
        {podInfoVisible && (
          <>
            {podError && (
              <div style={{ marginBottom: '16px', color: '#ff4d4f' }}>
                错误: {podError}
              </div>
            )}
            <Table
              columns={columns}
              dataSource={pods}
              rowKey="id"
              size="small"
              scroll={{ x: 1200 }}
              pagination={{
                size: 'small',
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 个Pod`,
              }}
              loading={podLoading}
            />
          </>
        )}
      </Card>

      {/* 重启确认对话框 */}
      <RestartConfirmModal
        open={isRestartConfirmVisible()}
        type={getRestartConfirmData()?.type || 'single'}
        podName={getRestartConfirmData()?.podName || ''}
        namespace={namespace}
        totalPods={pods.length}
        strategy={getRestartConfirmData()?.strategy || 'graceful'}
        onStrategyChange={handleStrategyChange}
        onConfirm={handleConfirmRestart}
        onCancel={handleCancelRestart}
        loading={restartLoading}
      />

      {/* 重启进度指示器 */}
      <RestartProgressIndicator
        open={isRestartProgressVisible()}
        loading={restartLoading}
        result={restartResult}
        onClose={handleCloseProgress}
      />

      {/* 终端和Arthas模态框 */}
      {renderTerminalModal()}
      {renderArthasModal()}
    </>
  );
};

export default PodRealTimeInfo; 