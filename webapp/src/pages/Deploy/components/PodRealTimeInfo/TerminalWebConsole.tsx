import React, { useEffect, useRef, useState } from 'react';
import { Terminal } from '@xterm/xterm';
import { FitAddon } from '@xterm/addon-fit';
import { WebLinksAddon } from '@xterm/addon-web-links';
import '@xterm/xterm/css/xterm.css';
import { message, Button, Space, Spin } from 'antd';
import { 
  DisconnectOutlined, 
  ReloadOutlined, 
  SettingOutlined,
  ClearOutlined,
  FullscreenOutlined,
  FullscreenExitOutlined,
} from '@ant-design/icons';

interface TerminalWebConsoleProps {
  namespace: string;
  podName: string;
  containerName?: string;
  shell?: string;
  visible: boolean;
  onClose: () => void;
}

interface TerminalMessage {
  operation: string;
  data?: string;
  rows?: number;
  cols?: number;
  error?: string;
}

const TerminalWebConsole: React.FC<TerminalWebConsoleProps> = ({
  namespace,
  podName,
  containerName = 'app',
  shell = '/bin/bash',
  visible,
  onClose,
}) => {
  const terminalRef = useRef<HTMLDivElement>(null);
  const [terminal, setTerminal] = useState<Terminal | null>(null);
  const [fitAddon, setFitAddon] = useState<FitAddon | null>(null);
  const [websocket, setWebsocket] = useState<WebSocket | null>(null);
  const [sessionId, setSessionId] = useState<string>('');
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'disconnected' | 'error'>('disconnected');
  const [isFullscreen, setIsFullscreen] = useState<boolean>(false);
  const [reconnectAttempts, setReconnectAttempts] = useState<number>(0);
  const maxReconnectAttempts = 5;

  // 启动终端会话
  const startTerminalSession = async (): Promise<string | null> => {
    try {
      const response = await fetch('/api/pods/terminal/start', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          namespace,
          pod_name: podName,
          container_name: containerName,
          shell,
          cols: 80,
          rows: 24,
          timeout: 3600,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || '启动终端会话失败');
      }

      const result = await response.json();
      if (result.code === 0 && result.data) {
        return result.data.session_id;
      } else {
        throw new Error(result.message || '启动终端会话失败');
      }
    } catch (error: any) {
      message.error(`启动终端会话失败: ${error.message}`);
      return null;
    }
  };

  // 建立WebSocket连接
  const connectWebSocket = (sessionId: string) => {
    if (websocket) {
      websocket.close();
    }

    const wsProtocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const wsUrl = `${wsProtocol}//${window.location.host}/api/pods/terminal/ws/${namespace}/${podName}?session_id=${sessionId}&container_name=${containerName}&shell=${encodeURIComponent(shell)}&cols=80&rows=24`;

    const ws = new WebSocket(wsUrl);
    
    ws.onopen = () => {
      setConnectionStatus('connected');
      setReconnectAttempts(0);
      message.success('终端连接成功');
      
      // 发送ping保持连接
      const pingInterval = setInterval(() => {
        if (ws.readyState === WebSocket.OPEN) {
          ws.send(JSON.stringify({ operation: 'ping' }));
        } else {
          clearInterval(pingInterval);
        }
      }, 30000); // 每30秒ping一次
    };

    ws.onmessage = (event) => {
      try {
        const wsMessage: TerminalMessage = JSON.parse(event.data);
        
        switch (wsMessage.operation) {
          case 'stdout':
            if (terminal && wsMessage.data) {
              terminal.write(wsMessage.data);
            }
            break;
          case 'stderr':
            if (terminal && wsMessage.data) {
              terminal.write(`\r\n\x1b[31m${wsMessage.data}\x1b[0m\r\n`);
            }
            break;
          case 'error':
            message.error(`终端错误: ${wsMessage.data}`);
            setConnectionStatus('error');
            break;
          case 'pong':
            // 心跳响应，不需要处理
            break;
        }
      } catch (error) {
        console.error('解析WebSocket消息失败:', error);
      }
    };

    ws.onclose = (event) => {
      setConnectionStatus('disconnected');
      if (event.code !== 1000 && reconnectAttempts < maxReconnectAttempts) {
        // 非正常关闭，尝试重连
        setTimeout(() => {
          setReconnectAttempts(prev => prev + 1);
          connectWebSocket(sessionId);
        }, Math.pow(2, reconnectAttempts) * 1000); // 指数退避
      }
    };

    ws.onerror = () => {
      setConnectionStatus('error');
      message.error('WebSocket连接错误');
    };

    setWebsocket(ws);
  };

  // 初始化终端
  const initializeTerminal = () => {
    if (!terminalRef.current) return;

    const term = new Terminal({
      fontSize: 14,
      fontFamily: '"JetBrains Mono", "Monaco", "Consolas", "Ubuntu Mono", monospace',
      theme: {
        background: '#1e1e1e',
        foreground: '#d4d4d4',
        cursor: '#ffffff',
        black: '#000000',
        red: '#cd3131',
        green: '#0dbc79',
        yellow: '#e5e510',
        blue: '#2472c8',
        magenta: '#bc3fbc',
        cyan: '#11a8cd',
        white: '#e5e5e5',
        brightBlack: '#666666',
        brightRed: '#f14c4c',
        brightGreen: '#23d18b',
        brightYellow: '#f5f543',
        brightBlue: '#3b8eea',
        brightMagenta: '#d670d6',
        brightCyan: '#29b8db',
        brightWhite: '#e5e5e5',
      },
      cursorBlink: true,
      allowProposedApi: true,
    });

    const fit = new FitAddon();
    const webLinks = new WebLinksAddon();
    
    term.loadAddon(fit);
    term.loadAddon(webLinks);
    
    term.open(terminalRef.current);
    
    // 适应容器大小
    fit.fit();
    
    // 监听用户输入
    term.onData((data) => {
      if (websocket && websocket.readyState === WebSocket.OPEN) {
        websocket.send(JSON.stringify({
          operation: 'stdin',
          data: data,
        }));
      }
    });

    // 监听终端大小变化
    term.onResize(({ cols, rows }) => {
      if (websocket && websocket.readyState === WebSocket.OPEN) {
        websocket.send(JSON.stringify({
          operation: 'resize',
          cols: cols,
          rows: rows,
        }));
      }
    });

    setTerminal(term);
    setFitAddon(fit);

    // 监听窗口大小变化
    const handleResize = () => {
      setTimeout(() => {
        fit.fit();
      }, 100);
    };

    window.addEventListener('resize', handleResize);
    
    return () => {
      window.removeEventListener('resize', handleResize);
      term.dispose();
    };
  };

  // 清理资源
  const cleanup = () => {
    if (websocket) {
      websocket.close();
      setWebsocket(null);
    }
    if (terminal) {
      terminal.dispose();
      setTerminal(null);
    }
    if (sessionId) {
      // 停止终端会话
      fetch(`/api/pods/terminal/stop/${namespace}/${podName}?session_id=${sessionId}`, {
        method: 'POST',
      }).catch(console.error);
    }
  };

  // 重新连接
  const reconnect = async () => {
    setConnectionStatus('connecting');
    cleanup();
    
    const newSessionId = await startTerminalSession();
    if (newSessionId) {
      setSessionId(newSessionId);
      connectWebSocket(newSessionId);
    }
  };

  // 清空终端
  const clearTerminal = () => {
    if (terminal) {
      terminal.clear();
    }
  };

  // 切换全屏
  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
    setTimeout(() => {
      if (fitAddon) {
        fitAddon.fit();
      }
    }, 100);
  };

  // 组件挂载时初始化
  useEffect(() => {
    if (visible) {
      const init = async () => {
        setConnectionStatus('connecting');
        
        // 初始化终端
        const cleanupTerminal = initializeTerminal();
        
        // 启动会话
        const newSessionId = await startTerminalSession();
        if (newSessionId) {
          setSessionId(newSessionId);
          connectWebSocket(newSessionId);
        } else {
          setConnectionStatus('error');
        }

        return cleanupTerminal;
      };

      init();
    }

    return () => {
      if (!visible) {
        cleanup();
      }
    };
  }, [visible]);

  // 组件卸载时清理
  useEffect(() => {
    return cleanup;
  }, []);

  // 连接状态指示器
  const renderConnectionStatus = () => {
    const statusConfig = {
      connecting: { color: '#faad14', text: '连接中...', icon: <Spin size="small" /> },
      connected: { color: '#52c41a', text: '已连接', icon: null },
      disconnected: { color: '#d9d9d9', text: '已断开', icon: <DisconnectOutlined /> },
      error: { color: '#ff4d4f', text: '连接错误', icon: <DisconnectOutlined /> },
    };

    const config = statusConfig[connectionStatus];
    
    return (
      <Space>
        {config.icon}
        <span style={{ color: config.color, fontSize: '12px' }}>
          {config.text}
        </span>
      </Space>
    );
  };

  return (
    <div style={{ 
      display: 'flex', 
      flexDirection: 'column', 
      height: '100%',
      backgroundColor: '#1e1e1e',
    }}>
      {/* 工具栏 */}
      <div style={{
        padding: '8px 12px',
        backgroundColor: '#2d2d2d',
        borderBottom: '1px solid #404040',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
      }}>
        <div style={{ color: '#d4d4d4', fontSize: '14px', fontWeight: 500 }}>
          终端 - {podName}/{containerName}
        </div>
        
        <Space>
          {renderConnectionStatus()}
          
          <Button
            type="text"
            size="small"
            icon={<ClearOutlined />}
            onClick={clearTerminal}
            disabled={connectionStatus !== 'connected'}
            style={{ color: '#d4d4d4' }}
            title="清空终端"
          />
          
          <Button
            type="text"
            size="small"
            icon={<ReloadOutlined />}
            onClick={reconnect}
            loading={connectionStatus === 'connecting'}
            style={{ color: '#d4d4d4' }}
            title="重新连接"
          />
          
          <Button
            type="text"
            size="small"
            icon={isFullscreen ? <FullscreenExitOutlined /> : <FullscreenOutlined />}
            onClick={toggleFullscreen}
            style={{ color: '#d4d4d4' }}
            title={isFullscreen ? "退出全屏" : "全屏"}
          />
        </Space>
      </div>

      {/* 终端区域 */}
      <div 
        ref={terminalRef} 
        style={{ 
          flex: 1, 
          backgroundColor: '#1e1e1e',
          overflow: 'hidden',
        }} 
      />

      {/* 状态信息 */}
      {connectionStatus === 'error' && (
        <div style={{
          padding: '8px 12px',
          backgroundColor: '#ff4d4f',
          color: '#fff',
          fontSize: '12px',
          textAlign: 'center',
        }}>
          连接失败，请检查网络连接或Pod状态
          {reconnectAttempts < maxReconnectAttempts && (
            <Button 
              type="link" 
              size="small" 
              onClick={reconnect}
              style={{ color: '#fff', padding: '0 8px' }}
            >
              重试
            </Button>
          )}
        </div>
      )}
    </div>
  );
};

export default TerminalWebConsole; 