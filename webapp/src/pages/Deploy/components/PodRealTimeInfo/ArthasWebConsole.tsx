import React, { useState, useEffect, useRef } from 'react';
import { Button, Space, Tag, message } from 'antd';
import { GlobalOutlined, ReloadOutlined, DisconnectOutlined } from '@ant-design/icons';

interface ArthasWebConsoleProps {
  namespace: string;
  podName: string;
  visible: boolean;
  onClose?: () => void;
}

const ArthasWebConsole: React.FC<ArthasWebConsoleProps> = ({
  namespace,
  podName,
  visible,
  onClose
}) => {
  const [connected, setConnected] = useState<boolean>(false);
  const [connecting, setConnecting] = useState<boolean>(false);
  const [error, setError] = useState<string>('');
  const wsRef = useRef<WebSocket | null>(null);
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // WebSocket连接URL
  const getWebSocketURL = () => {
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const host = window.location.host;
    return `${protocol}//${host}/api/pods/arthas/ws/${namespace}/${podName}`;
  };

  // HTTP代理URL  
  const getProxyURL = (path: string = '/') => {
    return `/api/pods/arthas/proxy/${namespace}/${podName}${path}`;
  };

  // 建立WebSocket连接
  const connectWebSocket = () => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      return;
    }

    setConnecting(true);
    setError('');

    try {
      const wsURL = getWebSocketURL();
      console.log('连接到Arthas WebSocket:', wsURL);
      
      const ws = new WebSocket(wsURL);
      wsRef.current = ws;

      ws.onopen = () => {
        console.log('Arthas WebSocket连接已建立');
        setConnected(true);
        setConnecting(false);
        setError('');
        message.success('Arthas WebSocket连接成功');
      };

      ws.onmessage = (event) => {
        // 处理从Arthas接收的消息
        console.log('收到Arthas消息:', event.data);
        
        // 如果iframe已加载，转发消息给iframe
        if (iframeRef.current?.contentWindow) {
          try {
            iframeRef.current.contentWindow.postMessage({
              type: 'arthas-message',
              data: event.data
            }, '*');
          } catch (error) {
            console.warn('转发消息到iframe失败:', error);
          }
        }
      };

      ws.onerror = (error) => {
        console.error('Arthas WebSocket连接错误:', error);
        setError('WebSocket连接错误');
        setConnecting(false);
        message.error('WebSocket连接失败');
      };

      ws.onclose = (event) => {
        console.log('Arthas WebSocket连接已关闭:', event.code, event.reason);
        setConnected(false);
        setConnecting(false);
        
        if (event.code !== 1000) { // 非正常关闭
          setError(`连接异常关闭 (${event.code}): ${event.reason || '未知原因'}`);
          message.error('WebSocket连接断开');
        }
      };

    } catch (error) {
      console.error('创建WebSocket连接失败:', error);
      setError('创建WebSocket连接失败');
      setConnecting(false);
      message.error('创建WebSocket连接失败');
    }
  };

  // 断开WebSocket连接
  const disconnectWebSocket = () => {
    if (wsRef.current) {
      wsRef.current.close(1000, '用户主动断开');
      wsRef.current = null;
    }
    setConnected(false);
    setConnecting(false);
  };

  // 发送消息到Arthas
  const sendToArthas = (message: string) => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      wsRef.current.send(message);
    }
  };

  // 监听iframe消息
  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      if (event.data?.type === 'arthas-command') {
        // 从iframe接收到命令，发送到Arthas
        sendToArthas(event.data.command);
      }
    };

    window.addEventListener('message', handleMessage);
    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, []);

  // 当组件可见时自动连接
  useEffect(() => {
    if (visible && !connected && !connecting) {
      connectWebSocket();
    }
  }, [visible, connected, connecting]);

  // 组件卸载时断开连接
  useEffect(() => {
    return () => {
      disconnectWebSocket();
    };
  }, []);

  // 重新连接
  const handleReconnect = () => {
    disconnectWebSocket();
    setTimeout(() => {
      connectWebSocket();
    }, 1000);
  };

  // 在新窗口中打开
  const openInNewWindow = () => {
    const proxyURL = getProxyURL('/arthas-output/');
    window.open(proxyURL, '_blank', 'width=1200,height=800');
  };

  if (!visible) {
    return null;
  }

  return (
    <div ref={containerRef} style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* 头部工具栏 */}
      <div style={{
        padding: '8px 12px',
        borderBottom: '1px solid #f0f0f0',
        backgroundColor: '#fafafa',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        <Space>
          <Tag color="green">Pod: {podName}</Tag>
          <Tag color="blue">Namespace: {namespace}</Tag>
          <Tag color={connected ? 'green' : connecting ? 'orange' : 'red'}>
            {connecting ? '连接中...' : connected ? '已连接' : '未连接'}
          </Tag>
          {error && <Tag color="red">{error}</Tag>}
        </Space>
        
        <Space>
          <Button
            size="small"
            icon={<ReloadOutlined />}
            onClick={handleReconnect}
            loading={connecting}
            disabled={connecting}
          >
            重连
          </Button>
          <Button
            size="small"
            icon={<DisconnectOutlined />}
            onClick={disconnectWebSocket}
            disabled={!connected}
          >
            断开
          </Button>
          <Button
            size="small"
            type="primary"
            icon={<GlobalOutlined />}
            onClick={openInNewWindow}
          >
            新窗口
          </Button>
        </Space>
      </div>

      {/* 内容区域 */}
      <div style={{ flex: 1, position: 'relative', backgroundColor: '#fff' }}>
        {connected ? (
          <iframe
            ref={iframeRef}
            src={getProxyURL('/arthas-output/')}
            style={{ 
              width: '100%', 
              height: '100%', 
              border: 'none',
              backgroundColor: '#fff'
            }}
            title="Arthas Web Console"
            onLoad={() => {
              console.log('Arthas iframe已加载');
              // 可以在这里初始化iframe内的JavaScript通信
            }}
            onError={(e) => {
              console.error('Arthas iframe加载失败:', e);
              setError('iframe加载失败');
            }}
          />
        ) : (
          <div style={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            textAlign: 'center',
            color: '#999'
          }}>
            {connecting ? (
              <div>
                <div style={{ fontSize: '48px', marginBottom: '16px' }}>
                  <ReloadOutlined spin />
                </div>
                <div>正在连接Arthas Web控制台...</div>
              </div>
            ) : error ? (
              <div>
                <div style={{ fontSize: '48px', marginBottom: '16px', color: '#ff4d4f' }}>
                  <DisconnectOutlined />
                </div>
                <div style={{ color: '#ff4d4f' }}>连接失败</div>
                <div style={{ fontSize: '12px', marginTop: '8px' }}>{error}</div>
                <Button 
                  type="primary" 
                  size="small" 
                  style={{ marginTop: '16px' }}
                  onClick={handleReconnect}
                >
                  重试连接
                </Button>
              </div>
            ) : (
              <div>
                <div style={{ fontSize: '48px', marginBottom: '16px' }}>
                  <GlobalOutlined />
                </div>
                <div>等待连接Arthas Web控制台</div>
                <Button 
                  type="primary" 
                  size="small" 
                  style={{ marginTop: '16px' }}
                  onClick={connectWebSocket}
                >
                  开始连接
                </Button>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default ArthasWebConsole; 