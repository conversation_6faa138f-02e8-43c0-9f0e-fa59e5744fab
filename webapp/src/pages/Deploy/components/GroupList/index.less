.groupCard {
  .ant-card-head {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    padding: 0 12px;
    min-height: 36px;

    .ant-card-head-title {
      color: white;
      font-weight: 600;
      font-size: 13px;
      padding: 8px 0;

      .ant-badge {
        .ant-badge-count {
          background-color: rgba(255, 255, 255, 0.9);
          color: #1890ff;
          border: 1px solid rgba(255, 255, 255, 0.3);
        }
      }
    }
  }

  .ant-card-body {
    padding: 0;
    min-height: 100px;
  }
}

.groupHeader {
  padding: 6px 10px;
  border-bottom: 1px solid #f0f2f5;
  margin-bottom: 6px;
  background: linear-gradient(90deg, #fafbfc 0%, #f8faff 100%);
  border-radius: 3px;

  .ant-btn {
    border-radius: 3px;
    font-size: 11px;
    height: 24px;
    padding: 0 8px;
    border: 1px solid #d9d9d9;
    color: #666;

    &:hover {
      border-color: #1890ff;
      color: #1890ff;
    }
  }

  .ant-input-affix-wrapper {
    border: none !important;
    box-shadow: none !important;
    background: transparent !important;
    font-size: 11px;
    color: #666;
    padding: 0 4px;

    .ant-input {
      background: transparent !important;
      border: none !important;
      box-shadow: none !important;
      font-size: 11px;
      color: #666;
      padding: 0;

      &::placeholder {
        color: #999;
        font-size: 11px;
      }
    }

    .ant-input-prefix {
      margin-right: 4px;
    }

    .ant-input-clear-icon {
      font-size: 10px;
      color: #999;

      &:hover {
        color: #1890ff;
      }
    }

    &:hover {
      .ant-input {
        color: #1890ff;
      }
    }

    &.ant-input-affix-wrapper-focused {
      .ant-input {
        color: #1890ff;
      }
    }
  }

  .ant-select {
    .ant-select-selector {
      border: none !important;
      box-shadow: none !important;
      background: transparent !important;
      font-size: 11px;
      color: #666;
      padding: 0 4px;

      .ant-select-selection-item {
        color: #666;
        font-weight: 500;
      }
    }

    .ant-select-arrow {
      color: #999;
      font-size: 10px;
    }

    &:hover .ant-select-selector {
      color: #1890ff;

      .ant-select-selection-item {
        color: #1890ff;
      }
    }

    &.ant-select-focused .ant-select-selector {
      color: #1890ff;

      .ant-select-selection-item {
        color: #1890ff;
      }
    }
  }

  .ant-badge {
    .ant-badge-count {
      font-size: 10px;
      min-width: 16px;
      height: 16px;
      line-height: 14px;
      padding: 0 4px;
    }
  }
}

.groupItem {
  padding: 8px 12px;
  cursor: pointer;
  border: none;
  border-bottom: 1px solid #f0f2f5;
  transition: all 0.3s ease;
  position: relative;

  &:hover {
    background: linear-gradient(90deg, #f8faff 0%, #f0f7ff 100%);
    transform: translateX(1px);
  }

  &:last-child {
    border-bottom: none;
  }
}

.groupItemSelected {
  background: linear-gradient(90deg, #e6f7ff 0%, #f0f9ff 100%);
  border-left: 2px solid #1890ff;
  transform: translateX(1px);
  box-shadow: inset 0 0 0 1px rgba(24, 144, 255, 0.2);

  &:hover {
    background: linear-gradient(90deg, #e6f7ff 0%, #f0f9ff 100%);
  }
}

.groupIcon {
  font-size: 16px;
  color: #1890ff;
  margin-right: 6px;
  margin-top: 1px;
  transition: all 0.3s ease;

  .groupItem:hover & {
    color: #40a9ff;
    transform: scale(1.01);
  }
}

.serviceTitle {
  margin: 0;
  color: #1c1c1c;
  font-weight: 600;
  font-size: 14px;
}

.deployStatusTag {
  border-radius: 2px;
  font-weight: 500;
  border: none;
  font-size: 10px;
  padding: 0 4px;
  line-height: 16px;
  font-weight: 400;

  // 动态样式通过内联样式设置，这里只定义基础样式
  transition: all 0.3s ease;

  &:hover {
    opacity: 0.8;
  }
}

// 创建分组Modal相关样式
:global {
  .ant-form-item-label > label {
    font-weight: 500;
    color: #333;
  }

  .ant-modal-header {
    border-bottom: 1px solid #f0f0f0;
    padding: 14px 24px;
  }

  .ant-modal-title {
    font-weight: 600;
    font-size: 16px;
    color: #262626;
  }

  .ant-modal-body {
    padding: 20px 24px;
  }

  .ant-modal-footer {
    border-top: 1px solid #f0f0f0;
    padding: 10px 24px;
  }

  // 加载状态相关样式
  .ant-spin {
    .ant-spin-text {
      margin-top: 8px;
      font-size: 12px;
      color: #1890ff;
    }
  }

  // 空状态相关样式
  .ant-empty {
    margin: 16px 0;
    
    .ant-empty-image {
      height: 40px;
      margin-bottom: 8px;
    }
    
    .ant-empty-description {
      font-size: 12px;
    }
  }
} 