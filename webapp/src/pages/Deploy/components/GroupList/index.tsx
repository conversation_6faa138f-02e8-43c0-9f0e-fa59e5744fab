import React, { useMemo, useEffect, useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>ton,
  Card,
  Col,
  Input,
  Row,
  Select,
  Space,
  Tag,
  Typography,
  Spin,
  Empty,
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  TagOutlined,
  BranchesOutlined,
  ReloadOutlined,
} from '@ant-design/icons';
import { DeployStatus,DEPLOY_STATUS_CONFIG, mapStatusNumberToString } from '../../types/common';
import styles from './index.less';
import useGroup from '@/hooks/useGroup';
import CreateGroupModal from './CreateGroupModal';

const { Option } = Select;
const { Text } = Typography;

export const GroupList: React.FC = () => {
  const [nameFilter, setNameFilter] = React.useState<string>('');
  const [statusFilter, setStatusFilter] = React.useState<number>(0);
  const [createModalVisible, setCreateModalVisible] = useState(false);
  
  // 使用 useGroup hook 管理分组相关状态和操作
  const {
    loading,
    groupList,
    selectedGroupId,
    selectGroup,
    reloadGroups,
  } = useGroup();

  // 根据groups计算部署版本数量（暂时移除，因为Group类型中没有version字段）
// 根据groups计算部署版本数量
const deployedVersionSet = new Set(
  groupList
    .map(g => {
      console.log('group item:', g);
      return g.semver;
    })
    .filter(Boolean)
);
  const deployedVersionsCount = deployedVersionSet.size;

  // 根据状态和名称过滤分组
  const filteredGroups = useMemo(() => {
    if (!groupList || groupList.length === 0) return [];

    return groupList.filter((group: API.Group) => {
      const matchesName = group.name.toLowerCase().includes(nameFilter.toLowerCase());
      const matchesStatus = statusFilter === 0 || group.status === statusFilter;
      return matchesName && matchesStatus;
    });
  }, [groupList, nameFilter, statusFilter]);

  // 渲染状态选择器选项
  const renderStatusOptions = () => {
    return Object.values(DeployStatus)
      .filter(value => typeof value === 'number')
      .map(statusNumber => {
        const statusStr = mapStatusNumberToString(statusNumber as DeployStatus);
        const config = DEPLOY_STATUS_CONFIG[statusStr];
        return (
          <Option key={statusNumber} value={statusNumber}>
            <div style={{display: 'flex', alignItems: 'center'}}>
              <div style={{
                width: 12, 
                height: 12, 
                borderRadius: '50%', 
                backgroundColor: config.color,
                marginRight: 6
              }}></div>
              {statusStr}
            </div>
          </Option>
        );
      });
  };

  // 打开创建分组弹窗
  const handleOpenCreateModal = () => {
    setCreateModalVisible(true);
  };

  // 关闭创建分组弹窗
  const handleCloseCreateModal = () => {
    setCreateModalVisible(false);
  };

  // 创建分组成功后刷新列表
  const handleCreateSuccess = async () => {
    setCreateModalVisible(false);
    await reloadGroups();
  };

  // 手动刷新列表
  const handleRefresh = () => {
    reloadGroups();
  };

  return (
    <>
      <Card
        title={
          <div
            style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}
          >
            <Space size="small">
              <span>分组</span>
              <Badge
                count={filteredGroups.length}
                style={{ backgroundColor: '#1890ff' }}
                size="small"
              />
              <Typography.Text
                type="secondary"
                style={{ fontSize: '12px', marginLeft: '8px' }}
              >
                版本数: {deployedVersionsCount}
              </Typography.Text>
            </Space>
            <Space>
              <Button
                type="text"
                icon={<ReloadOutlined />}
                size="small"
                onClick={handleRefresh}
                loading={loading}
                style={{ color: '#1890ff' }}
              />
              <Button
                type="text"
                icon={<PlusOutlined />}
                size="small"
                onClick={handleOpenCreateModal}
                style={{ color: '#1890ff' }}
              />
            </Space>
          </div>
        }
        size="small"
        className={styles.groupCard}
        variant="borderless"
      >
        <div className={styles.groupHeader}>
          <div
            style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              width: '100%',
            }}
          >
            <div>
              <Input
                size="small"
                placeholder="搜索分组"
                value={nameFilter}
                onChange={(e) => setNameFilter(e.target.value)}
                prefix={
                  <SearchOutlined style={{ fontSize: 10, color: '#999' }} />
                }
                style={{ width: 120 }}
                variant="borderless"
                allowClear
              />
            </div>
            <div>
              <Select
                size="small"
                value={statusFilter}
                onChange={setStatusFilter}
                style={{ width: 120 }}
                variant="borderless"
                placeholder="状态"
              >
                <Option key={0} value={0}>全部</Option>
                {renderStatusOptions()}
              </Select>
            </div>
          </div>
        </div>
        
        <div style={{ marginTop: 8 }}>
          <Spin spinning={loading} tip="加载中...">
            {filteredGroups.length === 0 ? (
              <Empty
                image={Empty.PRESENTED_IMAGE_SIMPLE}
                description={
                  <Text type="secondary">
                    {nameFilter.trim()
                      ? '未找到匹配的分组'
                      : '暂无符合条件的分组'}
                  </Text>
                }
                style={{ padding: '32px 0' }}
              />
            ) : (
              filteredGroups.map((group: API.Group) => (
                <div
                  key={group.id}
                  className={`${styles.groupItem} ${
                    selectedGroupId === group.id
                      ? styles.groupItemSelected
                      : ''
                  }`}
                  onClick={() => selectGroup(group.id)}
                >
                  <div>
                    <Row
                      justify="space-between"
                      align="middle"
                      style={{ marginBottom: '6px' }}
                    >
                      <Col>
                        <div className={styles.serviceTitle}>
                          {group.name}
                        </div>
                      </Col>
                      <Col>
                        {(() => {
                          const statusStr = mapStatusNumberToString(group.status as DeployStatus);
                          const config = DEPLOY_STATUS_CONFIG[statusStr];
                          
                          return (
                            <Tag
                              className={styles.deployStatusTag}
                              style={{
                                color: config.color,
                                backgroundColor: 'white',
                                border: `1px solid ${config.color}`,
                                fontSize: '11px',
                                padding: '0 6px',
                                fontWeight: 500,
                                lineHeight: '18px',
                              }}
                            >
                              {statusStr}
                            </Tag>
                          );
                        })()}
                      </Col>
                    </Row>

                    {/* 版本行 - 暂时隐藏，因为Group类型中没有version和is_branch字段 */}
                    
                    <Row>
                      <Col>
                        {(() => {
                          const isTag = group.is_branch === 0;
                          const TagIcon = isTag ? TagOutlined : BranchesOutlined;
                          
                          return (
                            <div 
                              style={{
                                display: 'flex',
                                alignItems: 'center',
                                fontSize: '12px',
                                color: isTag ? '#096dd9' : '#389e0d',
                                marginTop: '4px'
                              }}
                            >
                              <TagIcon style={{ marginRight: '4px' }} />
                              <span>{group.semver || '无版本'}</span>
                            </div>
                          );
                        })()}
                      </Col>
                    </Row>
                   
                  </div>
                </div>
              ))
            )}
          </Spin>
        </div>
      </Card>

      {/* 创建分组的Modal */}
      <CreateGroupModal
        visible={createModalVisible}
        onClose={handleCloseCreateModal}
        onSuccess={handleCreateSuccess}
      />
    </>
  );
};
