import React, { useState } from 'react';
import { Modal, message } from 'antd';
import { useModel } from '@umijs/max';
import type { ProFormInstance } from '@ant-design/pro-components';
import {
  ProCard,
  ProFormCheckbox,
  ProFormDatePicker,
  ProFormDateRangePicker,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
  StepsForm,
} from '@ant-design/pro-components';
import { useRef } from 'react';
import { createGroup } from '@/services/api/GroupController';


interface CreateGroupModalProps {
  visible: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

const CreateGroupModal: React.FC<CreateGroupModalProps> = ({ visible, onClose, onSuccess }) => {
  const formRef = useRef<ProFormInstance>();
  const { deploy } = useModel('deploy');

// 示例接口函数（请替换为你的真实接口）
const apiStep1 = async (values: any) => {
  // 组装后端需要的参数
  const data: API.CreateGroupRequest = {
    app_id: deploy.appData.application.id,
    env_id: deploy.envId,
    code: values.code,
    name: values.name,
    description : values.description

    // 如果 CreateGroupRequest 没有 description 字段，则不要传递
    // 你可以根据后端需要补充其它字段
  };
  return await createGroup(data);
};
const apiStep2 = async (data: any) => {
  return { code: 0, message: 'ok' };
};
const apiStep3 = async (data: any) => {
  return { code: 0, message: 'ok' };
};
const apiStep4 = async (data: any) => {
  return { code: 0, message: 'ok' };
};

  return (
    <Modal
      title=""
      open={visible}
      onCancel={onClose}
      footer={null}
      width={900}
      maskClosable={false}
      destroyOnClose
    >
      <ProCard>
        <StepsForm
          formRef={formRef}
        >
          {/* 第一步 */}
          <StepsForm.StepForm
            name="base"
            title="创建分组"
            onFinish={async (values) => {
              // 提交当前步骤数据到后台
              const res = await apiStep1(values);
              if (res.code === 0) {
                message.success('基本信息已保存');
                return true;
              } else {
                message.error(res.message || '保存失败');
                return false;
              }
            }}
          >
            <ProFormText
              name="code"
              label="分组编码"
              width="md"
              // tooltip=""
              placeholder="请输入编码"
              rules={[{ required: true, max: 50, message: '编码不能超过50个字符' }]}
            />
             <ProFormText
              name="name"
              label="分组名称"
              width="md"
              // tooltip=""
              placeholder="请输入名称"
              rules={[{ required: true, max: 50, message: '名称不能超过50个字符' }]}
            />
            <ProFormTextArea
              name="description"
              label="备注"
              width="lg"
              placeholder="请输入备注"
              rules={[{ max: 500, message: '备注不能超过500个字符' }]}
              fieldProps={{ maxLength: 500, showCount: true }}
            />
          </StepsForm.StepForm>

          {/* 第二步 */}
          <StepsForm.StepForm
            name="build"
            title="Build配置"
            onFinish={async (values) => {
              // 获取所有表单数据
              const allValues = formRef.current?.getFieldsValue();
              const res = await apiStep2(allValues);
              if (res.code === 0) {
                message.success('Build配置已保存');
                return true;
              } else {
                message.error(res.message || '保存失败');
                return false;
              }
            }}
          >
            <ProFormCheckbox.Group
              name="checkbox"
              label="迁移类型"
              width="lg"
              options={['结构迁移', '全量迁移', '增量迁移', '全量校验']}
            />
            <ProFormText name="dbname" label="业务 DB 用户名" />
            <ProFormDatePicker name="datetime" label="记录保存时间" width="sm" />
            <ProFormCheckbox.Group
              name="checkbox2"
              label="迁移类型"
              options={['完整 LOB', '不同步 LOB', '受限制 LOB']}
            />
          </StepsForm.StepForm>

          {/* 第三步 */}
          <StepsForm.StepForm
            name="container"
            title="容器配置"
            onFinish={async (values) => {
              const allValues = formRef.current?.getFieldsValue();
              const res = await apiStep3(allValues);
              if (res.code === 0) {
                message.success('容器配置已保存');
                return true;
              } else {
                message.error(res.message || '保存失败');
                return false;
              }
            }}
          >
            <ProFormCheckbox.Group
              name="checkbox3"
              label="部署单元"
              rules={[
                {
                  required: true,
                },
              ]}
              options={['部署单元1', '部署单元2', '部署单元3']}
            />
            <ProFormSelect
              label="部署分组策略"
              name="remark"
              rules={[
                {
                  required: true,
                },
              ]}
              initialValue="1"
              options={[
                {
                  value: '1',
                  label: '策略一',
                },
                { value: '2', label: '策略二' },
              ]}
            />
            <ProFormSelect
              label="Pod 调度策略"
              name="remark2"
              initialValue="2"
              options={[
                {
                  value: '1',
                  label: '策略一',
                },
                { value: '2', label: '策略二' },
              ]}
            />
          </StepsForm.StepForm>

          {/* 第四步 */}
          <StepsForm.StepForm
            name="probe"
            title="探针配置"
            onFinish={async (values) => {
              const allValues = formRef.current?.getFieldsValue();
              const res = await apiStep4(allValues);
              if (res.code === 0) {
                message.success('探针配置已保存');
                // 最后一步，调用 onSuccess 关闭弹窗并刷新
                onSuccess();
                return true;
              } else {
                message.error(res.message || '保存失败');
                return false;
              }
            }}
          >
            <ProFormCheckbox.Group
              name="checkbox4"
              label="部署单元"
              rules={[
                {
                  required: true,
                },
              ]}
              options={['部署单元1', '部署单元2', '部署单元3']}
            />
            <ProFormSelect
              label="部署分组策略"
              name="remark3"
              rules={[
                {
                  required: true,
                },
              ]}
              initialValue="1"
              options={[
                {
                  value: '1',
                  label: '策略一',
                },
                { value: '2', label: '策略二' },
              ]}
            />
            <ProFormSelect
              label="Pod 调度策略"
              name="remark4"
              initialValue="2"
              options={[
                {
                  value: '1',
                  label: '策略一',
                },
                { value: '2', label: '策略二' },
              ]}
            />
          </StepsForm.StepForm>
        </StepsForm>
      </ProCard>
    </Modal>
  );
};

export default CreateGroupModal; 