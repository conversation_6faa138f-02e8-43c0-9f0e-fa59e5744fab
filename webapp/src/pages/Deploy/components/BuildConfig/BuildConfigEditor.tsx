import React, { useState, useEffect } from 'react';
import { 
  Modal, Form, Input, InputNumber, Button, Space, 
  Card, Divider, Row, Col, message, Select, 
  Typography, Switch, Radio, Alert, Tooltip
} from 'antd';
import {
  CodeOutlined, RocketOutlined,
  <PERSON>boltOutlined, ContainerOutlined,
  SettingOutlined, ToolOutlined,
  QuestionCircleOutlined
} from '@ant-design/icons';

const { Text } = Typography;
const { Option } = Select;
const { TextArea } = Input;

export interface BuildConfigEditorProps {
  visible: boolean;
  appData: API.AppData | null;
  onCancel: () => void;
  onSave: (updatedSettings: any) => Promise<boolean>;
}

const BuildConfigEditor: React.FC<BuildConfigEditorProps> = ({
  visible,
  appData,
  onCancel,
  onSave
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [selectedLanguage, setSelectedLanguage] = useState('java');
  const [selectedTemplate, setSelectedTemplate] = useState('smart-detect');
  const [memoryMode, setMemoryMode] = useState('preset');
  const [gcMode, setGcMode] = useState('preset');
  const [previewCommand, setPreviewCommand] = useState('');

  useEffect(() => {
    if (visible && appData?.application) {
      const currentConfig = {
        language: appData.application.language || 'java',
        template_type: 'smart-detect',
        build_params: {
          java_version: '17',
          build_tool: 'auto',
          skip_tests: true,
          parallel_build: true,
          main_class: '',
          maven_profiles: ''
        },
        runtime_params: {
          jvm_memory: '1g',
          gc_type: 'G1GC',
          startup_command: '',
          jvm_options: '',
          environment_variables: ''
        },
        container_params: {
          replicas: 1,
          namespace: 'default',
          port: 8080,
          expose_ports: '',
          volumes: '',
          base_image: 'openjdk:17-jre-slim',
          cpu_request: 200,
          cpu_limit: 1000,
          memory_request: 512,
          memory_limit: 1024
        },
        pre_build_script: '',
        post_build_script: '',
        custom_build_script: ''
      };
      
      form.setFieldsValue(currentConfig);
      setSelectedLanguage(currentConfig.language);
      setSelectedTemplate(currentConfig.template_type);
    }
  }, [visible, appData, form]);

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);
      
      const buildConfig = {
        language: values.language,
        template_type: values.template_type,
        build_params: values.build_params,
        runtime_params: values.runtime_params,
        container_params: values.container_params,
        pre_build_script: values.pre_build_script,
        post_build_script: values.post_build_script,
        custom_build_script: values.custom_build_script
      };
      
      const success = await onSave(buildConfig);
      if (success) {
        message.success('构建配置已成功保存');
        onCancel();
      }
    } catch (error) {
      console.error('表单验证失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleLanguageChange = (language: string) => {
    setSelectedLanguage(language);
    
    // 重置模板和基础参数
    const defaults = {
      java: {
        template_type: 'smart-detect',
        port: 8080,
        build_params: { java_version: '17', build_tool: 'auto' },
        base_image: 'openjdk:17-jre-slim'
      },
      go: {
        template_type: 'gin-api',
        port: 8080,
        build_params: { go_version: '1.21', build_tool: 'go' },
        base_image: 'golang:1.21-alpine'
      },
      nodejs: {
        template_type: 'express-api',
        port: 3000,
        build_params: { node_version: '18', package_manager: 'npm' },
        base_image: 'node:18-alpine'
      },
      python: {
        template_type: 'flask-api',
        port: 5000,
        build_params: { python_version: '3.11', package_manager: 'pip' },
        base_image: 'python:3.11-slim'
      },
      php: {
        template_type: 'laravel',
        port: 80,
        build_params: { php_version: '8.2', package_manager: 'composer' },
        base_image: 'php:8.2-fpm-alpine'
      }
    };

    const langDefaults = defaults[language as keyof typeof defaults];
    setSelectedTemplate(langDefaults.template_type);
    
    form.setFieldsValue({
      template_type: langDefaults.template_type,
      build_params: langDefaults.build_params,
      container_params: {
        ...form.getFieldValue('container_params'),
        port: langDefaults.port,
        base_image: langDefaults.base_image
      }
    });
  };

  const handleTemplateChange = (templateType: string) => {
    setSelectedTemplate(templateType);
  };

  const generatePreviewCommand = () => {
    const values = form.getFieldsValue();
    const runtimeParams = values.runtime_params || {};
    
    let command = '';
    
    if (runtimeParams.jvm_memory && runtimeParams.jvm_memory !== 'auto') {
      if (runtimeParams.jvm_memory.includes('custom:')) {
        command += runtimeParams.jvm_memory.replace('custom:', '') + ' ';
      } else {
        const memory = runtimeParams.jvm_memory;
        command += `-Xmx${memory} -Xms${Math.floor(parseInt(memory) * 0.5)}${memory.slice(-1)} `;
      }
    }
    
    if (runtimeParams.gc_type) {
      if (runtimeParams.gc_type.includes('custom:')) {
        command += runtimeParams.gc_type.replace('custom:', '') + ' ';
      } else {
        switch (runtimeParams.gc_type) {
          case 'G1GC':
            command += '-XX:+UseG1GC ';
            break;
          case 'ParallelGC':
            command += '-XX:+UseParallelGC ';
            break;
          case 'ZGC':
            command += '-XX:+UseZGC ';
            break;
          case 'ShenandoahGC':
            command += '-XX:+UseShenandoahGC ';
            break;
        }
      }
    }
    
    if (runtimeParams.jvm_options) {
      command += runtimeParams.jvm_options + ' ';
    }
    
    if (runtimeParams.startup_command) {
      command += runtimeParams.startup_command;
    } else {
      command += 'java -jar app.jar';
    }
    
    return command.trim();
  };

  const handleFormChange = () => {
    if (selectedLanguage === 'java') {
      setPreviewCommand(generatePreviewCommand());
    }
  };

  return (
    <Modal
      title={
        <Space>
          <RocketOutlined />
          <span>构建配置</span>
        </Space>
      }
      open={visible}
      width={900}
      onCancel={onCancel}
      maskClosable={false}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          取消
        </Button>,
        <Button 
          key="submit" 
          type="primary" 
          loading={loading} 
          onClick={handleSubmit}
        >
          保存配置
        </Button>
      ]}
      destroyOnClose
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          language: 'java',
          template_type: 'smart-detect'
        }}
        onChange={handleFormChange}
      >
        {/* 基础配置 */}
        <Card 
          title={
            <Space>
              <CodeOutlined />
              <Text strong>基础配置</Text>
            </Space>
          }
          size="small" 
          style={{ marginBottom: 16 }}
          variant="borderless"
        >
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                label={
                  <Space>
                    <span>编程语言</span>
                    <Tooltip title="选择应用的编程语言">
                      <QuestionCircleOutlined style={{ color: '#999' }} />
                    </Tooltip>
                  </Space>
                }
                name="language"
                rules={[{ required: true, message: '请选择编程语言' }]}
              >
                <Select 
                  onChange={handleLanguageChange}
                  placeholder="选择编程语言"
                >
                  <Option value="java">Java</Option>
                  <Option value="go">Go</Option>
                  <Option value="nodejs">Node.js</Option>
                  <Option value="python">Python</Option>
                  <Option value="php">PHP</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label={
                  <Space>
                    <span>构建模板</span>
                    <Tooltip title="选择适合您应用的构建模板">
                      <QuestionCircleOutlined style={{ color: '#999' }} />
                    </Tooltip>
                  </Space>
                }
                name="template_type"
                rules={[{ required: true, message: '请选择构建模板' }]}
              >
                <Select 
                  onChange={handleTemplateChange}
                  placeholder="选择构建模板"
                >
                  {selectedLanguage === 'java' && (
                    <>
                      <Option value="smart-detect">智能检测</Option>
                      <Option value="spring-boot-jar">Spring Boot JAR</Option>
                      <Option value="spring-boot-war">Spring Boot WAR</Option>
                      <Option value="tomcat-war">Tomcat WAR</Option>
                      <Option value="custom">自定义</Option>
                    </>
                  )}
                  {selectedLanguage === 'go' && (
                    <>
                      <Option value="gin-api">Gin API</Option>
                      <Option value="echo-api">Echo API</Option>
                      <Option value="standard-go">标准Go应用</Option>
                      <Option value="custom">自定义</Option>
                    </>
                  )}
                  {selectedLanguage === 'nodejs' && (
                    <>
                      <Option value="express-api">Express API</Option>
                      <Option value="nest-api">NestJS API</Option>
                      <Option value="next-app">Next.js应用</Option>
                      <Option value="custom">自定义</Option>
                    </>
                  )}
                  {selectedLanguage === 'python' && (
                    <>
                      <Option value="flask-api">Flask API</Option>
                      <Option value="django-app">Django应用</Option>
                      <Option value="fastapi">FastAPI</Option>
                      <Option value="custom">自定义</Option>
                    </>
                  )}
                  {selectedLanguage === 'php' && (
                    <>
                      <Option value="laravel">Laravel</Option>
                      <Option value="symfony">Symfony</Option>
                      <Option value="wordpress">WordPress</Option>
                      <Option value="custom">自定义</Option>
                    </>
                  )}
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item 
                label={
                  <Space>
                    <span>主端口</span>
                    <Tooltip title="应用暴露的主要端口">
                      <QuestionCircleOutlined style={{ color: '#999' }} />
                    </Tooltip>
                  </Space>
                }
                name={['container_params', 'port']}
              >
                <InputNumber 
                  min={1} 
                  max={65535} 
                  placeholder="8080" 
                  style={{ width: '100%' }} 
                />
              </Form.Item>
            </Col>
          </Row>
        </Card>

        {/* 构建配置 */}
        <Card 
          title={
            <Space>
              <ToolOutlined />
              <Text strong>构建配置</Text>
            </Space>
          }
          size="small" 
          style={{ marginBottom: 16 }}
          variant="borderless"
        >
          {selectedLanguage === 'java' && (
            <>
              <Row gutter={16}>
                <Col span={8}>
                  <Form.Item
                    label="Java版本"
                    name={['build_params', 'java_version']}
                    rules={[{ required: true, message: '请选择Java版本' }]}
                  >
                    <Select placeholder="选择版本">
                      <Option value="8">Java 8</Option>
                      <Option value="11">Java 11</Option>
                      <Option value="17">Java 17</Option>
                      <Option value="21">Java 21</Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    label="构建工具"
                    name={['build_params', 'build_tool']}
                  >
                    <Radio.Group buttonStyle="solid">
                      <Radio.Button value="auto">自动检测</Radio.Button>
                      <Radio.Button value="maven">Maven</Radio.Button>
                      <Radio.Button value="gradle">Gradle</Radio.Button>
                    </Radio.Group>
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item
                        label="跳过测试"
                        name={['build_params', 'skip_tests']}
                        valuePropName="checked"
                      >
                        <Switch />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        label="并行构建"
                        name={['build_params', 'parallel_build']}
                        valuePropName="checked"
                      >
                        <Switch />
                      </Form.Item>
                    </Col>
                  </Row>
                </Col>
              </Row>
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    label="主类"
                    name={['build_params', 'main_class']}
                  >
                    <Input placeholder="com.example.Application" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="Maven Profiles"
                    name={['build_params', 'maven_profiles']}
                  >
                    <Input placeholder="prod,docker" />
                  </Form.Item>
                </Col>
              </Row>
            </>
          )}

          {selectedLanguage === 'go' && (
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item
                  label="Go版本"
                  name={['build_params', 'go_version']}
                  rules={[{ required: true, message: '请选择Go版本' }]}
                >
                  <Select>
                    <Option value="1.19">Go 1.19</Option>
                    <Option value="1.20">Go 1.20</Option>
                    <Option value="1.21">Go 1.21</Option>
                    <Option value="1.22">Go 1.22</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  label="构建工具"
                  name={['build_params', 'build_tool']}
                >
                  <Radio.Group buttonStyle="solid">
                    <Radio.Button value="go">Go Modules</Radio.Button>
                    <Radio.Button value="make">Makefile</Radio.Button>
                  </Radio.Group>
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  label="CGO启用"
                  name={['build_params', 'cgo_enabled']}
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
              </Col>
            </Row>
          )}

          {selectedLanguage === 'nodejs' && (
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item
                  label="Node.js版本"
                  name={['build_params', 'node_version']}
                  rules={[{ required: true, message: '请选择Node.js版本' }]}
                >
                  <Select>
                    <Option value="16">Node.js 16</Option>
                    <Option value="18">Node.js 18</Option>
                    <Option value="20">Node.js 20</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  label="包管理器"
                  name={['build_params', 'package_manager']}
                >
                  <Radio.Group buttonStyle="solid">
                    <Radio.Button value="npm">npm</Radio.Button>
                    <Radio.Button value="yarn">yarn</Radio.Button>
                    <Radio.Button value="pnpm">pnpm</Radio.Button>
                  </Radio.Group>
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  label="构建命令"
                  name={['build_params', 'build_command']}
                >
                  <Input placeholder="npm run build" />
                </Form.Item>
              </Col>
            </Row>
          )}

          {selectedLanguage === 'python' && (
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item
                  label="Python版本"
                  name={['build_params', 'python_version']}
                  rules={[{ required: true, message: '请选择Python版本' }]}
                >
                  <Select>
                    <Option value="3.8">Python 3.8</Option>
                    <Option value="3.9">Python 3.9</Option>
                    <Option value="3.10">Python 3.10</Option>
                    <Option value="3.11">Python 3.11</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  label="包管理器"
                  name={['build_params', 'package_manager']}
                >
                  <Radio.Group buttonStyle="solid">
                    <Radio.Button value="pip">pip</Radio.Button>
                    <Radio.Button value="poetry">Poetry</Radio.Button>
                    <Radio.Button value="pipenv">Pipenv</Radio.Button>
                  </Radio.Group>
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  label="依赖文件"
                  name={['build_params', 'requirements_file']}
                >
                  <Input placeholder="requirements.txt" />
                </Form.Item>
              </Col>
            </Row>
          )}

          {selectedLanguage === 'php' && (
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item
                  label="PHP版本"
                  name={['build_params', 'php_version']}
                  rules={[{ required: true, message: '请选择PHP版本' }]}
                >
                  <Select>
                    <Option value="7.4">PHP 7.4</Option>
                    <Option value="8.0">PHP 8.0</Option>
                    <Option value="8.1">PHP 8.1</Option>
                    <Option value="8.2">PHP 8.2</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  label="包管理器"
                  name={['build_params', 'package_manager']}
                >
                  <Radio.Group buttonStyle="solid">
                    <Radio.Button value="composer">Composer</Radio.Button>
                  </Radio.Group>
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  label="启用扩展"
                  name={['build_params', 'php_extensions']}
                >
                  <Input placeholder="pdo,mysqli,gd" />
                </Form.Item>
              </Col>
            </Row>
          )}
          
          {/* 自定义构建脚本区域 */}
          {selectedTemplate === 'custom' && (
            <>
              <Divider orientation="left" style={{ margin: '16px 0 8px' }}>
                <Space>
                  <SettingOutlined />
                  <Text>自定义构建脚本</Text>
                </Space>
              </Divider>
              
              <Form.Item
                label="构建前脚本"
                name="pre_build_script"
                extra="在构建开始前执行的脚本"
              >
                <TextArea 
                  placeholder="echo 'Starting build...'" 
                  autoSize={{ minRows: 2, maxRows: 3 }}
                />
              </Form.Item>
              
              <Form.Item
                label="自定义构建脚本"
                name="custom_build_script"
                extra="完全自定义的构建脚本，会覆盖默认构建流程"
              >
                <TextArea 
                  placeholder="#!/bin/bash&#10;mvn clean package" 
                  autoSize={{ minRows: 2, maxRows: 4 }}
                />
              </Form.Item>
              
              <Form.Item
                label="构建后脚本"
                name="post_build_script"
                extra="在构建完成后执行的脚本"
              >
                <TextArea 
                  placeholder="echo 'Build completed!'" 
                  autoSize={{ minRows: 2, maxRows: 3 }}
                />
              </Form.Item>
            </>
          )}
        </Card>

        {/* 运行时配置 */}
        <Card 
          title={
            <Space>
              <ThunderboltOutlined />
              <Text strong>运行时配置</Text>
            </Space>
          }
          size="small" 
          style={{ marginBottom: 16 }}
          variant="borderless"
        >
          {selectedLanguage === 'java' && (
            <>
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item label="内存配置" name={['runtime_params', 'jvm_memory']}>
                    <Radio.Group 
                      buttonStyle="solid"
                      onChange={(e) => {
                        setMemoryMode(e.target.value.includes('custom:') ? 'custom' : 'preset');
                        handleFormChange();
                      }}
                    >
                      <Radio.Button value="512m">512MB</Radio.Button>
                      <Radio.Button value="1g">1GB</Radio.Button>
                      <Radio.Button value="2g">2GB</Radio.Button>
                      <Radio.Button value="4g">4GB</Radio.Button>
                      <Radio.Button value="8g">8GB</Radio.Button>
                    </Radio.Group>
                    <div style={{ marginTop: 8 }}>
                      <Radio
                        value="custom"
                        checked={memoryMode === 'custom'}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setMemoryMode('custom');
                            form.setFieldValue(['runtime_params', 'jvm_memory'], 'custom:');
                          }
                        }}
                      >
                        自定义：
                      </Radio>
                      {memoryMode === 'custom' && (
                        <Input
                          placeholder="-Xmx2g -Xms1g"
                          style={{ width: '60%', marginLeft: 8 }}
                          onChange={(e) => {
                            form.setFieldValue(['runtime_params', 'jvm_memory'], `custom:${e.target.value}`);
                            handleFormChange();
                          }}
                        />
                      )}
                    </div>
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item label="垃圾回收" name={['runtime_params', 'gc_type']}>
                    <Select 
                      style={{ width: '100%' }}
                      defaultValue="G1GC"
                      onChange={(value) => {
                        setGcMode(value.toString().includes('custom:') ? 'custom' : 'preset');
                        handleFormChange();
                      }}
                    >
                      <Option value="G1GC">G1GC（推荐）</Option>
                      <Option value="ParallelGC">ParallelGC</Option>
                      <Option value="ZGC">ZGC</Option>
                      <Option value="ShenandoahGC">ShenandoahGC</Option>
                    </Select>
                    <div style={{ marginTop: 8 }}>
                      <Radio
                        value="custom"
                        checked={gcMode === 'custom'}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setGcMode('custom');
                            form.setFieldValue(['runtime_params', 'gc_type'], 'custom:');
                          }
                        }}
                      >
                        自定义：
                      </Radio>
                      {gcMode === 'custom' && (
                        <Input
                          placeholder="-XX:+UseG1GC -XX:MaxGCPauseMillis=200"
                          style={{ width: '60%', marginLeft: 8 }}
                          onChange={(e) => {
                            form.setFieldValue(['runtime_params', 'gc_type'], `custom:${e.target.value}`);
                            handleFormChange();
                          }}
                        />
                      )}
                    </div>
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    label="启动命令"
                    name={['runtime_params', 'startup_command']}
                  >
                    <Input placeholder="java -jar app.jar" onChange={handleFormChange} />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="其他JVM选项"
                    name={['runtime_params', 'jvm_options']}
                  >
                    <Input placeholder="-Dspring.profiles.active=prod" onChange={handleFormChange} />
                  </Form.Item>
                </Col>
              </Row>

              {previewCommand && (
                <Alert
                  message={
                    <div>
                      <Text strong>💡 预览命令:</Text>
                      <div style={{ 
                        marginTop: 4, 
                        padding: '8px 12px', 
                        background: '#f5f5f5', 
                        borderRadius: '4px',
                        fontFamily: 'Monaco, Consolas, monospace',
                        fontSize: '13px',
                        wordBreak: 'break-all'
                      }}>
                        {previewCommand}
                      </div>
                    </div>
                  }
                  type="info"
                  showIcon
                />
              )}
            </>
          )}

          {selectedLanguage !== 'java' && (
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item
                  label="内存分配"
                  name={['runtime_params', 'jvm_memory']}
                >
                  <Select>
                    <Option value="auto">自动分配</Option>
                    <Option value="512m">512MB</Option>
                    <Option value="1g">1GB</Option>
                    <Option value="2g">2GB</Option>
                    <Option value="4g">4GB</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col span={16}>
                <Form.Item
                  label="启动命令"
                  name={['runtime_params', 'startup_command']}
                >
                  <Input placeholder="启动命令" />
                </Form.Item>
              </Col>
            </Row>
          )}

          <Form.Item
            label="环境变量"
            name={['runtime_params', 'environment_variables']}
            tooltip="每行一个环境变量，格式为 KEY=VALUE"
          >
            <TextArea 
              placeholder="SPRING_PROFILES_ACTIVE=prod&#10;DATABASE_URL=******************************" 
              autoSize={{ minRows: 2, maxRows: 4 }}
            />
          </Form.Item>
        </Card>

        {/* 容器配置 */}
        <Card 
          title={
            <Space>
              <ContainerOutlined />
              <Text strong>容器配置</Text>
            </Space>
          }
          size="small" 
          style={{ marginBottom: 16 }}
          variant="borderless"
        >
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                label="副本数"
                name={['container_params', 'replicas']}
              >
                <InputNumber min={1} max={20} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="命名空间"
                name={['container_params', 'namespace']}
              >
                <Select>
                  <Option value="default">default (development)</Option>
                  <Option value="production">production</Option>
                  <Option value="staging">staging</Option>
                  <Option value="testing">testing</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="基础镜像"
                name={['container_params', 'base_image']}
              >
                <Select placeholder="选择基础镜像">
                  {selectedLanguage === 'java' && (
                    <>
                      <Option value="openjdk:17-jre-slim">openjdk:17-jre-slim</Option>
                      <Option value="openjdk:11-jre-slim">openjdk:11-jre-slim</Option>
                      <Option value="openjdk:8-jre-slim">openjdk:8-jre-slim</Option>
                      <Option value="eclipse-temurin:17-jre">eclipse-temurin:17-jre</Option>
                    </>
                  )}
                  {selectedLanguage === 'nodejs' && (
                    <>
                      <Option value="node:18-alpine">node:18-alpine</Option>
                      <Option value="node:16-alpine">node:16-alpine</Option>
                      <Option value="node:20-alpine">node:20-alpine</Option>
                    </>
                  )}
                  {selectedLanguage === 'python' && (
                    <>
                      <Option value="python:3.11-slim">python:3.11-slim</Option>
                      <Option value="python:3.10-slim">python:3.10-slim</Option>
                      <Option value="python:3.9-slim">python:3.9-slim</Option>
                    </>
                  )}
                  {selectedLanguage === 'go' && (
                    <>
                      <Option value="golang:1.21-alpine">golang:1.21-alpine</Option>
                      <Option value="golang:1.20-alpine">golang:1.20-alpine</Option>
                      <Option value="alpine:latest">alpine:latest</Option>
                    </>
                  )}
                  {selectedLanguage === 'php' && (
                    <>
                      <Option value="php:8.2-fpm-alpine">php:8.2-fpm-alpine</Option>
                      <Option value="php:8.1-fpm-alpine">php:8.1-fpm-alpine</Option>
                      <Option value="nginx:alpine">nginx:alpine</Option>
                    </>
                  )}
                  <Option value="custom">自定义镜像</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
          
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="CPU配置"
              >
                <Space align="center" style={{ width: '100%' }}>
                  <Form.Item 
                    name={['container_params', 'cpu_request']} 
                    style={{ marginBottom: 0, width: '45%' }}
                    noStyle
                  >
                    <Input 
                      placeholder="Request" 
                      suffix="m"
                      style={{ 
                        borderTop: 'none', 
                        borderLeft: 'none', 
                        borderRight: 'none',
                        borderRadius: 0
                      }} 
                    />
                  </Form.Item>
                  <span style={{ color: '#8c8c8c', padding: '0 8px' }}>/</span>
                  <Form.Item 
                    name={['container_params', 'cpu_limit']} 
                    style={{ marginBottom: 0, width: '45%' }}
                    noStyle
                  >
                    <Input 
                      placeholder="Limit" 
                      suffix="m"
                      style={{ 
                        borderTop: 'none', 
                        borderLeft: 'none', 
                        borderRight: 'none',
                        borderRadius: 0
                      }} 
                    />
                  </Form.Item>
                </Space>
              </Form.Item>
            </Col>
            
            <Col span={12}>
              <Form.Item
                label="内存配置"
              >
                <Space align="center" style={{ width: '100%' }}>
                  <Form.Item 
                    name={['container_params', 'memory_request']} 
                    style={{ marginBottom: 0, width: '45%' }}
                    noStyle
                  >
                    <Input 
                      placeholder="Request" 
                      suffix="Mi"
                      style={{ 
                        borderTop: 'none', 
                        borderLeft: 'none', 
                        borderRight: 'none',
                        borderRadius: 0
                      }} 
                    />
                  </Form.Item>
                  <span style={{ color: '#8c8c8c', padding: '0 8px' }}>/</span>
                  <Form.Item 
                    name={['container_params', 'memory_limit']} 
                    style={{ marginBottom: 0, width: '45%' }}
                    noStyle
                  >
                    <Input 
                      placeholder="Limit" 
                      suffix="Mi"
                      style={{ 
                        borderTop: 'none', 
                        borderLeft: 'none', 
                        borderRight: 'none',
                        borderRadius: 0
                      }} 
                    />
                  </Form.Item>
                </Space>
              </Form.Item>
            </Col>
          </Row>
        </Card>
      </Form>
    </Modal>
  );
};

export default BuildConfigEditor; 