import React, { useState } from 'react';
import { 
  Button, message, Space, Form, Input, 
  InputNumber, Tooltip, Divider, Card, Tag, Typography,
  Row, Col, Select
} from 'antd';
import { 
  CopyOutlined, EditOutlined, SaveOutlined, CloseOutlined,
  CodeOutlined, ApiOutlined,
  FolderOutlined, FileTextOutlined,
  SettingOutlined, RocketOutlined,
  CloudServerOutlined, CloudOutlined, GlobalOutlined,
  AppstoreOutlined, PartitionOutlined,
  BuildOutlined, ToolOutlined
} from '@ant-design/icons';
import { useModel } from '@umijs/max';

const { Title, Text } = Typography;
const { Option } = Select;

interface BuildConfigTooltipProps {
  onEdit?: () => void;
  editable?: boolean;
  onSave?: (updatedSettings: any) => Promise<boolean>;
}

// 可复制的内容组件
const CopyableContent: React.FC<{text: string, label: string}> = ({text, label}) => {
  const handleCopy = () => {
    if (navigator.clipboard) {
      navigator.clipboard.writeText(text)
        .then(() => message.success(`${label}已复制到剪贴板`))
        .catch(() => message.error('复制失败，请手动复制'));
    } else {
      // 兼容方案
      const textarea = document.createElement('textarea');
      textarea.value = text;
      document.body.appendChild(textarea);
      textarea.select();
      try {
        document.execCommand('copy');
        message.success(`${label}已复制到剪贴板`);
      } catch (err) {
        message.error('复制失败，请手动复制');
      }
      document.body.removeChild(textarea);
    }
  };

  return (
    <div style={{ display: 'flex', alignItems: 'center' }}>
      <div style={{ flex: 1, overflow: 'hidden', textOverflow: 'ellipsis' }}>
        {text || <Text type="secondary">未设置</Text>}
      </div>
      {text && (
        <Button 
          type="link" 
          icon={<CopyOutlined />} 
          onClick={handleCopy}
          style={{ padding: 0, marginLeft: 4 }}
        />
      )}
    </div>
  );
};

const BuildConfigTooltip: React.FC<BuildConfigTooltipProps> = ({
  onEdit,
  editable = false,
  onSave
}) => {
  const { deploy } = useModel('deploy');
  const [isEditing, setIsEditing] = useState(false);
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  // 模拟build配置数据，实际应该从deploy model或API获取
  const buildConfig = {
    language: deploy.appData?.application?.language || 'java',
    template_type: 'smart-detect',
    java_version: '17',
    build_tool: 'maven',
    skip_tests: true,
    parallel_build: true,
    jvm_memory: 'auto',
    main_class: '',
    maven_profiles: ''
  };

  if (!deploy.appData?.application) {
    return <div>应用数据加载中...</div>;
  }

  const handleEdit = () => {
    if (onEdit) {
      onEdit();
    } else {
      setIsEditing(true);
      form.setFieldsValue(buildConfig);
    }
  };

  // 查看模式
  return (
    <Card 
      bordered={false}
      className="build-config-card"
      style={{ backgroundColor: '#fcfcfc' }}
      styles={{ body: { padding: '0px' }  }}
    >
      <div style={{ padding: '8px 12px', display: 'flex', justifyContent: 'space-between', alignItems: 'center', borderBottom: '1px solid #f0f0f0' }}>
        <Title level={5} style={{ margin: 0 }}>
          Build配置
        </Title>
        {editable && (
          <Button 
            type="primary" 
            size="small" 
            icon={<EditOutlined />} 
            onClick={handleEdit}
          >
            编辑
          </Button>
        )}
      </div>
      
      <div style={{ padding: '12px' }}>
        <Row gutter={[16, 16]}>
          {/* 左侧列 */}
          <Col span={24} lg={12}>
            {/* 语言配置部分 */}
            <Card
              size="small"
              title={<Text strong><BuildOutlined /> 语言配置</Text>}
              style={{ marginBottom: '16px' }}
            >
              <Row gutter={[16, 16]}>
                <Col span={12}>
                  <div className="config-item">
                    <div className="config-label">
                      <CodeOutlined /> 编程语言
                    </div>
                    <div className="config-value">
                      <Tag color="blue">{buildConfig.language.toUpperCase()}</Tag>
                    </div>
                  </div>
                </Col>
                <Col span={12}>
                  <div className="config-item">
                    <div className="config-label">
                      <ToolOutlined /> 模板类型
                    </div>
                    <div className="config-value">
                      <Tag color="green">{buildConfig.template_type}</Tag>
                    </div>
                  </div>
                </Col>
                <Col span={12}>
                  <div className="config-item">
                    <div className="config-label">
                      <CodeOutlined /> Java版本
                    </div>
                    <div className="config-value">
                      <Tag color="orange">Java {buildConfig.java_version}</Tag>
                    </div>
                  </div>
                </Col>
                <Col span={12}>
                  <div className="config-item">
                    <div className="config-label">
                      <ToolOutlined /> 构建工具
                    </div>
                    <div className="config-value">
                      <Tag color="purple">{buildConfig.build_tool}</Tag>
                    </div>
                  </div>
                </Col>
              </Row>
            </Card>

            {/* 构建选项部分 */}
            <Card
              size="small"
              title={<Text strong><RocketOutlined /> Build选项</Text>}
              style={{ marginBottom: '16px' }}
            >
              <Row gutter={[16, 16]}>
                <Col span={12}>
                  <div className="config-item">
                    <div className="config-label">
                      <ApiOutlined /> 跳过测试
                    </div>
                    <div className="config-value">
                      <Tag color={buildConfig.skip_tests ? 'green' : 'red'}>
                        {buildConfig.skip_tests ? '是' : '否'}
                      </Tag>
                    </div>
                  </div>
                </Col>
                <Col span={12}>
                  <div className="config-item">
                    <div className="config-label">
                      <AppstoreOutlined /> 并行构建
                    </div>
                    <div className="config-value">
                      <Tag color={buildConfig.parallel_build ? 'green' : 'red'}>
                        {buildConfig.parallel_build ? '是' : '否'}
                      </Tag>
                    </div>
                  </div>
                </Col>
                <Col span={24}>
                  <div className="config-item">
                    <div className="config-label">
                      <FileTextOutlined /> 主类
                    </div>
                    <div className="config-value">
                      <CopyableContent text={buildConfig.main_class || '自动检测'} label="主类" />
                    </div>
                  </div>
                </Col>
              </Row>
            </Card>
          </Col>
          
          <Col span={24} lg={12}>
            {/* 运行时配置部分 */}
            <Card
              size="small"
              title={<Text strong><CloudOutlined /> 运行时配置</Text>}
              style={{ marginBottom: '16px' }}
            >
              <Row gutter={[16, 16]}>
                <Col span={24}>
                  <div className="config-item">
                    <div className="config-label">
                      <PartitionOutlined /> JVM内存
                    </div>
                    <div className="config-value">
                      <Tag color="blue">{buildConfig.jvm_memory}</Tag>
                    </div>
                  </div>
                </Col>
                <Col span={24}>
                  <div className="config-item">
                    <div className="config-label">
                      <FileTextOutlined /> Maven Profiles
                    </div>
                    <div className="config-value">
                      <CopyableContent text={buildConfig.maven_profiles || '默认'} label="Maven Profiles" />
                    </div>
                  </div>
                </Col>
              </Row>
            </Card>
          </Col>
        </Row>
      </div>

      <style dangerouslySetInnerHTML={{
        __html: `
          .config-item {
            display: flex;
            flex-direction: column;
            padding: 8px;
            border-radius: 4px;
            background-color: #fafafa;
            height: 100%;
          }
          .config-label {
            font-size: 12px;
            color: #777;
            margin-bottom: 4px;
            font-weight: 500;
          }
          .config-value {
            font-size: 14px;
          }
        `
      }} />
    </Card>
  );
};

export default BuildConfigTooltip; 