import styles from '@/pages/Deploy/index.less';
import {
  getFirstTwoLevelKeys,
  getUserSrvTree,
  transformSrvTreeData
} from '@/services/api/SrvTreeController';
import {
  ClusterOutlined,
  MinusOutlined,
  PlusOutlined,
  ReloadOutlined,
  SearchOutlined,
  SwapOutlined
} from '@ant-design/icons';
import {
  Badge,
  Button,
  Col,
  Divider,
  Input,
  message,
  Popover,
  Row,
  Tooltip,
  Tree
} from 'antd';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useModel } from 'umi';
import { TreeNode } from './types';

interface ApplicationSelectorProps {
  loading?: boolean;
}

const ApplicationSelector: React.FC<ApplicationSelectorProps> = ({ loading: appDataLoading = false }) => {
  const [treeData, setTreeData] = useState<TreeNode[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [appSearchText, setAppSearchText] = useState<string>('');
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);
  const [selectedAppId, setSelectedAppId] = useState<number>(0);
  const [popoverVisible, setPopoverVisible] = useState<boolean>(false);
  const { deploy, updateDeploy } = useModel('deploy');
  // 直接从deploy model中获取数据，不再使用useAppDataLoader
  const { appData } = deploy;

  // 高亮标题的渲染函数 - 用useCallback包装避免重复创建
  const renderHighlightedTitle = useCallback((title: string, searchText: string) => {
    if (!searchText.trim()) {
      return title;
    }
    
    const index = title.toLowerCase().indexOf(searchText.toLowerCase());
    if (index === -1) {
      return title;
    }
    
    // 将标题分成三部分：匹配前、匹配部分、匹配后
    const beforeMatch = title.substring(0, index);
    const match = title.substring(index, index + searchText.length);
    const afterMatch = title.substring(index + searchText.length);
    
    // 使用React元素包装高亮部分，只高亮文字，不改变背景色
    return (
      <span>
        {beforeMatch}
        <span style={{ color: '#1890ff', fontWeight: 'bold' }}>
          {match}
        </span>
        {afterMatch}
      </span>
    );
  }, []);
  
  // 获取服务树数据
  const fetchSrvTreeData = async () => {
    setLoading(true);
    try {
      const response = await getUserSrvTree();
      if (response && response.code === 0 && response.data) {
        const transformedData = transformSrvTreeData(response.data);
        setTreeData(transformedData);
        
        // 获取第一层和第二层节点的key，只展开前两层
        const twoLevelKeys = getFirstTwoLevelKeys(transformedData);
        setExpandedKeys(twoLevelKeys);
      } else {
        message.error('获取服务树失败: ' + (response.message || '未知错误'));
      }
    } catch (error) {
      console.error('获取服务树出错:', error);
      message.error('获取服务树出错');
    } finally {
      setLoading(false);
    }
  };


  // 加载应用数据的包装函数
  const handleLoadAppData = useCallback(async (nodeId: number, envId: number) => {
    // 只更新nodeId和envId，让useAppDataLoader的useEffect自动处理数据加载
    updateDeploy({nodeId: nodeId, envId: envId});
  }, [updateDeploy]);

  // 组件挂载时获取服务树数据
  useEffect(() => {
    fetchSrvTreeData();
  }, []);

  // 自动展开包含搜索结果的父节点
  useEffect(() => {
    if (appSearchText.trim()) {
      // 收集所有匹配节点的父节点路径
      const parentKeys: Set<number> = new Set();
      
      const collectParentKeys = (nodes: TreeNode[], parentPath: number[] = []) => {
        nodes.forEach(node => {
          const currentPath = [...parentPath, node.key];
          
          if (node.nodeType === 'srv' && 
              node.title.toString().toLowerCase().includes(appSearchText.toLowerCase())) {
            // 将该节点的所有父节点添加到展开列表
            parentPath.forEach(key => parentKeys.add(key));
          }
          
          if (node.children) {
            collectParentKeys(node.children, currentPath);
          }
        });
      };
      
      collectParentKeys(treeData);
      
      if (parentKeys.size > 0) {
        // 使用函数式状态更新，避免依赖项循环
        setExpandedKeys(prevKeys => {
          const newKeys = Array.from(new Set([...prevKeys, ...Array.from(parentKeys)]));
          // 只有当键真的发生变化时才更新
          if (newKeys.length !== prevKeys.length || 
              !newKeys.every(key => prevKeys.includes(key))) {
            return newKeys;
          }
          return prevKeys;
        });
      }
    }
  }, [appSearchText, treeData]);

  const filteredTreeData = useMemo(() => {
    if (!appSearchText.trim()) {
      return treeData;
    }
    
    // 修改过滤树函数，只搜索srv类型节点
    const filterTree = (nodes: any[]): any[] => {
      return nodes.reduce((acc, node) => {
        // 判断是否为srv节点且匹配搜索条件
        const isSrvNode = node.nodeType === 'srv';
        const searchTextLower = appSearchText.toLowerCase();
        const nodeTitleLower = node.title.toLowerCase();
        const matchesSearch = isSrvNode && nodeTitleLower.includes(searchTextLower);
        
        // 如果是srv节点且匹配，添加高亮属性
        let modifiedNode = { ...node };
        if (matchesSearch) {
          // 为匹配的srv节点添加高亮渲染属性
          modifiedNode.title = renderHighlightedTitle(node.title, appSearchText);
        }
        
        // 处理子节点
        let children = node.children ? filterTree(node.children) : [];
        
        // 如果当前节点匹配或者有匹配的子节点，则保留
        if (matchesSearch || children.length > 0) {
          acc.push({
            ...modifiedNode,
            children: children.length > 0 ? children : undefined,
          });
        }
        
        return acc;
      }, []);
    };
    
    const data = filterTree(treeData);
    return data;
  }, [appSearchText, treeData]);
  
  const handleExpand = (newExpandedKeys: React.Key[]) => {
    setExpandedKeys(newExpandedKeys);
  };

  const handleTreeSelect = (selectedKeys: React.Key[], info: any) => {
    if (selectedKeys.length > 0) {
      const key = selectedKeys[0] as number;
      const node = info.node;
      if (node.nodeType === 'srv') {
        setSelectedAppId(key);
        
        handleLoadAppData(key, deploy.envId);
        setPopoverVisible(false);
      } else {
        const isExpanded = expandedKeys.includes(key);
        const newExpandedKeys = isExpanded
          ? expandedKeys.filter(k => k !== key)
          : [...expandedKeys, key];
        handleExpand(newExpandedKeys);
      }
    }
  };

  const appSelectorContent = (
    <div className={styles.appSelectorPopover} style={{ width: '100%' }}>
      <div className={styles.popoverHeader}>
        <Input
          placeholder="输入关键字进行过滤..."
          value={appSearchText}
          onChange={(e) => setAppSearchText(e.target.value)}
          prefix={<SearchOutlined style={{ color: '#1890ff' }} />}
          allowClear
          autoFocus
        />
        <Tooltip title="刷新服务树" placement="top">
          <Button
            type="primary"
            ghost
            icon={<ReloadOutlined />}
            onClick={fetchSrvTreeData}
            size="middle"
            loading={loading}
          />
        </Tooltip>
      </div>
      <Divider style={{ margin: '0' }} />
      
      <Tree
        showIcon
        showLine={{ showLeafIcon: false }}
        switcherIcon={(props) => {
          if (props.isLeaf) {
            return null;
          }
          
          const iconStyle = {
            fontSize: '9px',
            color: props.expanded ? '#1890ff' : '#595959',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            width: '100%',
            height: '100%',
          };
          
          const boxStyle: React.CSSProperties = {
            display: 'inline-flex',
            justifyContent: 'center',
            alignItems: 'center',
            width: '14px',
            height: '14px',
            border: `1px solid ${props.expanded ? '#91d5ff' : '#d9d9d9'}`,
            borderRadius: '2px',
            backgroundColor: props.expanded ? '#e6f7ff' : '#fafafa',
            transition: 'all 0.3s',
            cursor: 'pointer',
            pointerEvents: 'auto',
          };
          
          return (
            <div 
              style={boxStyle} 
              className="tree-switcher-box"
              onClick={(e) => {
                e.stopPropagation();
                const nodeKey = props.data?.key;
                if (nodeKey) {
                  const isExpanded = expandedKeys.includes(nodeKey);
                  const newExpandedKeys = isExpanded
                    ? expandedKeys.filter(k => k !== nodeKey)
                    : [...expandedKeys, nodeKey];
                  handleExpand(newExpandedKeys);
                }
              }}
            >
              {props.expanded ? (
                <MinusOutlined style={iconStyle} />
              ) : (
                <PlusOutlined style={iconStyle} />
              )}
            </div>
          );
        }}
        treeData={filteredTreeData}
        onSelect={handleTreeSelect}
        selectedKeys={[selectedAppId]}
        expandedKeys={expandedKeys}
        onExpand={handleExpand}
        className={styles.popoverTree}
        blockNode={false}
        virtual={true}
        motion={null}
      />
    </div>
  );

  return (
    <div className={styles.appSection}>
      <Row gutter={[8, 0]} align="middle">
        <Col flex="auto">
          <Popover
            content={appSelectorContent}
            trigger="click"
            open={popoverVisible}
            onOpenChange={setPopoverVisible}
            placement="bottomLeft"
            classNames={{ root: styles.appSelectorPopoverOverlay }}
            styles={{ root: { width: '380px' } }}
            align={{
              offset: [-12, 4],
            }}
            mouseEnterDelay={0}
            mouseLeaveDelay={0.1}
            destroyOnHidden={false}
          >
            <Button
              className={styles.appSelectorTrigger}
              icon={<ClusterOutlined />}
              loading={appDataLoading}
            >
              <Badge 
                color={appData?.application?.id ? "#52c41a" : "#d9d9d9"} 
                status={appData?.application?.id ? "success" : "default"} 
                style={{ marginRight: 4 }} 
              />
              <span className={styles.appName}>{appData?.application?.name || '请选择应用'}</span>
              <SwapOutlined />
            </Button>
          </Popover>
        </Col>
      </Row>
    </div>
  );
}

export default ApplicationSelector; 