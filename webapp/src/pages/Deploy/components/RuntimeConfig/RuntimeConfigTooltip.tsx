import React, { useState } from 'react';
import { 
  Button, message, Space, Form, Input, 
  InputNumber, Tooltip, Divider, Card, Tag, Typography,
  Row, Col
} from 'antd';
import { 
  CopyOutlined, EditOutlined, SaveOutlined, CloseOutlined,
  CodeOutlined, ApiOutlined,
  FolderOutlined, FileTextOutlined,
  SettingOutlined, RocketOutlined,
  CloudServerOutlined, CloudOutlined, GlobalOutlined,
  AppstoreOutlined, PartitionOutlined
} from '@ant-design/icons';
import { useModel } from '@umijs/max';

const { Title, Text } = Typography;

interface RuntimeConfigTooltipProps {
  onEdit?: () => void;
  editable?: boolean;
  onSave?: (updatedSettings: Partial<API.RuntimeSetting>) => Promise<boolean>;
}

// 可复制的内容组件
const CopyableContent: React.FC<{text: string, label: string}> = ({text, label}) => {
  const handleCopy = () => {
    if (navigator.clipboard) {
      navigator.clipboard.writeText(text)
        .then(() => message.success(`${label}已复制到剪贴板`))
        .catch(() => message.error('复制失败，请手动复制'));
    } else {
      // 兼容方案
      const textarea = document.createElement('textarea');
      textarea.value = text;
      document.body.appendChild(textarea);
      textarea.select();
      try {
        document.execCommand('copy');
        message.success(`${label}已复制到剪贴板`);
      } catch (err) {
        message.error('复制失败，请手动复制');
      }
      document.body.removeChild(textarea);
    }
  };

  return (
    <div style={{ display: 'flex', alignItems: 'center' }}>
      <div style={{ flex: 1, overflow: 'hidden', textOverflow: 'ellipsis' }}>
        {text || <Text type="secondary">未设置</Text>}
      </div>
      {text && (
        <Button 
          type="link" 
          icon={<CopyOutlined />} 
          onClick={handleCopy}
          style={{ padding: 0, marginLeft: 4 }}
        />
      )}
    </div>
  );
};

const RuntimeConfigTooltip: React.FC<RuntimeConfigTooltipProps> = ({
  onEdit,
  editable = false,
  onSave
}) => {
  const { runtime } = useModel('runtime');
  const [isEditing, setIsEditing] = useState(false);
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  if (!runtime.applicationRuntimeSetting) {
    return <div>应用数据加载中...</div>;
  }

  const handleEdit = () => {
    if (onEdit) {
      onEdit();
    } else {
      setIsEditing(true);
      form.setFieldsValue(runtime.applicationRuntimeSetting || {});
    }
  };

  // 查看模式
  return (
    <Card 
      variant="borderless"
      className="runtime-config-card"
      style={{ backgroundColor: '#fcfcfc' }}
      styles={{ body: { padding: '0px' }  }}
    >
      <div style={{ padding: '8px 12px', display: 'flex', justifyContent: 'space-between', alignItems: 'center', borderBottom: '1px solid #f0f0f0' }}>
        <Title level={5} style={{ margin: 0 }}>
          运行时配置
        </Title>
        {editable && (
          <Button 
            type="primary" 
            size="small" 
            icon={<EditOutlined />} 
            onClick={handleEdit}
          >
            编辑
          </Button>
        )}
      </div>
      
      <div style={{ padding: '12px' }}>
        <Row gutter={[16, 16]}>
          {/* 左侧列 */}
          <Col span={24} lg={12}>
            {/* 命令配置部分 */}
            <Card
              size="small"
              title={<Text strong><RocketOutlined /> 命令配置</Text>}
              style={{ marginBottom: '16px' }}
            >
              <Row gutter={[16, 16]}>
                <Col span={12}>
                  <div className="config-item">
                    <div className="config-label">
                      <CodeOutlined /> 启动命令
                    </div>
                    <div className="config-value">
                      <CopyableContent text={runtime.applicationRuntimeSetting.start_command || ''} label="启动命令" />
                    </div>
                  </div>
                </Col>
                <Col span={12}>
                  <div className="config-item">
                    <div className="config-label">
                      <CodeOutlined /> 停止命令
                    </div>
                    <div className="config-value">
                      <CopyableContent text={runtime.applicationRuntimeSetting.stop_command || ''} label="停止命令" />
                    </div>
                  </div>
                </Col>
              </Row>
            </Card>

            {/* 路径配置部分 */}
            <Card
              size="small"
              title={<Text strong><FolderOutlined /> 路径配置</Text>}
              style={{ marginBottom: '16px' }}
            >
              <Row gutter={[16, 16]}>
                <Col span={8}>
                  <div className="config-item">
                    <div className="config-label">
                      <FolderOutlined /> 工作目录
                    </div>
                    <div className="config-value">
                      <CopyableContent text={runtime.applicationRuntimeSetting.work_dir || ''} label="工作目录" />
                    </div>
                  </div>
                </Col>
                <Col span={8}>
                  <div className="config-item">
                    <div className="config-label">
                      <FileTextOutlined /> 日志目录
                    </div>
                    <div className="config-value">
                      <CopyableContent text={runtime.applicationRuntimeSetting.log_dir || ''} label="日志目录" />
                    </div>
                  </div>
                </Col>
              </Row>
            </Card>
            
            {/* 其他配置 */}
            <Card
              size="small"
              title={<Text strong><CloudServerOutlined /> 其他配置</Text>}
              style={{ marginBottom: '16px' }}
            >
              <Row gutter={[16, 16]}>
                {/* 此处可扩展其他配置 */}
                <Col span={24}>
                  <div className="config-item">
                    <div className="config-label">
                      <SettingOutlined /> 环境标识
                    </div>
                    <div className="config-value">
                      <Tag color="blue">{runtime.applicationRuntimeSetting.env_name || '测试环境'}</Tag>
                    </div>
                  </div>
                </Col>
              </Row>
            </Card>
          </Col>
          
          <Col span={24} lg={12}>
            {/* 资源配置部分 */}
            <Card
              size="small"
              title={<Text strong><CloudOutlined /> 资源配置</Text>}
              style={{ marginBottom: '16px' }}
            >
              <Row gutter={[16, 16]}>
                <Col span={8}>
                  <div className="config-item">
                    <div className="config-label">
                      <AppstoreOutlined /> 预设副本
                    </div>
                    <div className="config-value">
                      <Tag color="blue">{runtime.applicationRuntimeSetting.replicas || 1}</Tag>
                    </div>
                  </div>
                </Col>
                <Col span={16}>
                  <div className="config-item">
                    <div className="config-label">
                      <PartitionOutlined /> 预设资源
                    </div>
                    <div className="config-value">
                      <Tag color="green">{runtime.applicationRuntimeSetting.request_cpu || '100m'} / {runtime.applicationRuntimeSetting.request_memory || '256Mi'}</Tag>
                    </div>
                  </div>
                </Col>
                <Col span={24}>
                  <div className="config-item">
                    <div className="config-label">
                      <PartitionOutlined /> 限制资源
                    </div>
                    <div className="config-value">
                      <Tag color="orange">{runtime.applicationRuntimeSetting.cpu || '3000m'} / {runtime.applicationRuntimeSetting.memory || '2048Mi'}</Tag>
                    </div>
                  </div>
                </Col>
              </Row>
            </Card>
            
            {/* 服务配置部分 */}
            <Card
              size="small"
              title={<Text strong><GlobalOutlined /> 服务配置</Text>}
            >
              <Row gutter={[16, 16]}>
                <Col span={12}>
                  <div className="config-item">
                    <div className="config-label">
                      <ApiOutlined /> SVC端口
                    </div>
                    <div className="config-value">
                      <Tag color="blue">{runtime.applicationRuntimeSetting.port || 80}</Tag>
                    </div>
                  </div>
                </Col>
                <Col span={12}>
                  <div className="config-item">
                    <div className="config-label">
                      <ApiOutlined /> POD端口
                    </div>
                    <div className="config-value">
                      <Tag color="purple">{runtime.applicationRuntimeSetting.port || 8089}</Tag>
                    </div>
                  </div>
                </Col>
                <Col span={24}>
                  <div className="config-item">
                    <div className="config-label">
                      <GlobalOutlined /> SVC地址
                    </div>
                    <div className="config-value">
                      <CopyableContent 
                        text={runtime.applicationRuntimeSetting.address || 'cmdb-service-k8s-env.default.svc.cluster.local'} 
                        label="SVC地址"
                      />
                    </div>
                  </div>
                </Col>
              </Row>
            </Card>
          </Col>
        </Row>
      </div>

      <style dangerouslySetInnerHTML={{
        __html: `
          .config-item {
            display: flex;
            flex-direction: column;
            padding: 8px;
            border-radius: 4px;
            background-color: #fafafa;
            height: 100%;
          }
          .config-label {
            font-size: 12px;
            color: #777;
            margin-bottom: 4px;
            font-weight: 500;
          }
          .config-value {
            font-size: 14px;
          }
        `
      }} />
    </Card>
  );
};

export default RuntimeConfigTooltip; 