import React, { useState } from 'react';
import { 
  Button, message, Space, Form, Input, Select,
  InputNumber, Tooltip, Divider, Card, Typography,
  Row, Col, Switch, Alert, Collapse
} from 'antd';
import { 
  CopyOutlined, EditOutlined, 
  CodeOutlined, ApiOutlined,
  FolderOutlined, FileTextOutlined,
  SettingOutlined, RocketOutlined,
  CloudOutlined, GlobalOutlined,
  AppstoreOutlined, PartitionOutlined,
  InfoCircleOutlined, CaretRightOutlined,
  ArrowRightOutlined
} from '@ant-design/icons';
import { useModel } from '@umijs/max';
import useDeploy from '@/models/deploy';
import styles from '../ConfigArea/style.less';

const { Title, Text } = Typography;
const { Option } = Select;
const { Panel } = Collapse;

// 扩展RuntimeSetting类型，以支持额外字段
interface ExtendedRuntimeSetting extends API.RuntimeSetting {
  base_image?: string;
}

// 使用Record<string, any>作为空对象的替代类型
interface RuntimeSettingLike {
  id?: number;
  replicas?: number;
  namespace?: string;
  request_cpu?: string;
  request_memory?: string;
  cpu?: string;
  memory?: string;
  base_image?: string;
  start_command?: string;
  stop_command?: string;
  work_dir?: string;
  log_dir?: string;
  [key: string]: any;
}

interface GroupRuntimeConfigProps {
  editMode: boolean;
  form: any;
  toggleEditMode: () => void;
}

// 可复制的内容组件
const CopyableContent: React.FC<{text: string, label: string}> = ({text, label}) => {
  const handleCopy = () => {
    if (navigator.clipboard) {
      navigator.clipboard.writeText(text)
        .then(() => message.success(`${label}已复制到剪贴板`))
        .catch(() => message.error('复制失败，请手动复制'));
    } else {
      // 兼容方案
      const textarea = document.createElement('textarea');
      textarea.value = text;
      document.body.appendChild(textarea);
      textarea.select();
      try {
        document.execCommand('copy');
        message.success(`${label}已复制到剪贴板`);
      } catch (err) {
        message.error('复制失败，请手动复制');
      }
      document.body.removeChild(textarea);
    }
  };

  return (
    <div style={{ display: 'flex', alignItems: 'center' }}>
      <div style={{ flex: 1, overflow: 'hidden', textOverflow: 'ellipsis' }}>
        {text || <Text type="secondary" style={{fontSize: '12px'}}>未设置</Text>}
      </div>
      {text && (
        <Button 
          type="link" 
          icon={<CopyOutlined />} 
          onClick={handleCopy}
          style={{ padding: 0, marginLeft: 4 }}
          size="small"
        />
      )}
    </div>
  );
};

// 显示运行时配置的值，同时显示应用继承和分组覆盖值
const RuntimeValue: React.FC<{
  appValue: string | number | undefined; 
  groupValue: string | number | undefined; 
  color?: string;
}> = ({appValue, groupValue, color = "blue"}) => {
  // 检查分组值是否存在，并且与应用值不同
  const isDifferent = groupValue !== undefined && groupValue !== null && groupValue !== appValue;

  if (isDifferent) {
    // 分组值与应用值不同，显示分组值并标记为自定义
    return (
      <div style={{whiteSpace: 'nowrap', display: 'flex', alignItems: 'center', gap: '4px'}}>
        <Text style={{fontSize: '12px', color: '#52c41a', fontWeight: 500}}>{groupValue}</Text>
        <Text type="secondary" style={{fontSize: '11px'}}>({appValue})</Text>
      </div>
    );
  } else {
    // 使用应用值
    return (
      <div style={{whiteSpace: 'nowrap'}}>
        <Text style={{fontSize: '12px'}}>{appValue || '-'}</Text>
      </div>
    );
  }
};

// 简洁的配置项显示组件
const ConfigItem: React.FC<{
  label: string;
  icon: React.ReactNode;
  appValue: string | number | undefined;
  groupValue?: string | number | undefined;
  color?: string;
}> = ({label, icon, appValue, groupValue, color = "blue"}) => (
  <div style={{
    display: 'flex',
    alignItems: 'center',
    padding: '2px 0',
    margin: '0 2px 4px 0',
    borderBottom: '1px dashed #f0f0f0'
  }}>
    <div style={{
      width: '60px',
      fontSize: '11px',
      color: '#666',
      display: 'flex',
      alignItems: 'center'
    }}>
      {icon} <span style={{marginLeft: 4}}>{label}</span>
    </div>
    <div style={{flex: 1, overflow: 'hidden', textOverflow: 'ellipsis'}}>
      <RuntimeValue 
        appValue={appValue} 
        groupValue={groupValue}
        color={color}
      />
    </div>
  </div>
);

const GroupRuntimeConfig: React.FC<GroupRuntimeConfigProps> = ({
  editMode,
  form,
  toggleEditMode,
}) => {
  const { runtime } = useModel('runtime');
  const { deploy } = useDeploy();
  const [inheritMode, setInheritMode] = useState(true);
  const [namespaces, setNamespaces] = useState<string[]>([
    'default',
    'kube-system',
    'production',
    'staging',
    'testing',
    'dev',
    'monitor',
    'logging',
  ]);
  
  // 获取当前分组的运行时配置
  const groupId = deploy.groupId;
  const appSetting = runtime.applicationRuntimeSetting as RuntimeSettingLike || {};
  // 从runtime对象获取group设置
  const groupSetting = runtime.runtimeSettings.find((setting: API.RuntimeSetting) => 
    setting.group_id === Number(groupId)
  ) as RuntimeSettingLike || {};
  
  if (!appSetting) {
    return <div>应用数据加载中...</div>;
  }
  
  // 切换配置继承模式
  const toggleInheritMode = (checked: boolean) => {
    setInheritMode(checked);
    if (checked) {
      // 切换到继承模式，重置表单为应用配置
      form.setFieldsValue(appSetting);
    } else {
      // 切换到自定义模式，保持当前表单值
    }
  };
  
  // 编辑模式下的表单渲染
  const renderEditForm = () => {
    return (
      <>
        <div style={{ marginBottom: 8 }}>
          <Space>
            <Switch 
              checked={inheritMode} 
              onChange={toggleInheritMode} 
              size="small"
            />
            <Text style={{fontSize: '12px'}}>使用应用运行时配置</Text>
            <Tooltip title="开启后将使用应用级别的运行时配置，关闭后可自定义分组配置">
              <InfoCircleOutlined style={{fontSize: '12px', color: '#999'}} />
            </Tooltip>
          </Space>
          
          {inheritMode && (
            <Alert 
              message="当前使用应用运行时配置，绿色值表示自定义配置" 
              type="info" 
              showIcon 
              style={{marginTop: 8, padding: '4px 8px', fontSize: '12px'}}
            />
          )}
        </div>
        
        <Form
          form={form}
          layout="vertical"
          initialValues={inheritMode ? appSetting : (groupSetting || appSetting)}
          className={styles.runtimeConfigForm || ""}
          style={{ maxWidth: '100%' }}
          disabled={inheritMode}
          size="small"
        >
          <Collapse 
            defaultActiveKey={['resource']}
            bordered={false}
            expandIcon={({ isActive }) => (
              <CaretRightOutlined rotate={isActive ? 90 : 0} style={{fontSize: '12px'}} />
            )}
            style={{backgroundColor: 'transparent'}}
          >
            <Panel header={<Text style={{fontSize: '13px'}}><CloudOutlined /> 资源配置</Text>} key="resource">
              <Row gutter={[8, 0]}>
                <Col span={6}>
                  <Form.Item label={<span style={{fontSize: '12px'}}>副本数量</span>} name="replicas" style={{marginBottom: 8}}>
                    <InputNumber min={1} max={10} style={{ width: '100%' }} size="small" />
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item label={<span style={{fontSize: '12px'}}>命名空间</span>} name="namespace" style={{marginBottom: 8}}>
                    <Select
                      placeholder="选择命名空间"
                      showSearch
                      filterOption={(input: string, option?: { value: string }) =>
                        option?.value?.toString().toLowerCase().includes(input.toLowerCase()) || false
                      }
                      size="small"
                    >
                      {namespaces.map(namespace => (
                        <Option key={namespace} value={namespace}>
                          {namespace}
                        </Option>
                      ))}
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Row gutter={[4, 0]}>
                    <Col span={11}>
                      <Form.Item 
                        label={<span style={{fontSize: '12px'}}>CPU配置</span>} 
                        name="request_cpu" 
                        style={{marginBottom: 8}}
                      >
                        <Input placeholder="请求CPU" size="small" />
                      </Form.Item>
                    </Col>
                    <Col span={2} style={{display: 'flex', alignItems: 'center', justifyContent: 'center', marginTop: 22}}>
                      <ArrowRightOutlined style={{color: '#999', fontSize: '12px'}} />
                    </Col>
                    <Col span={11}>
                      <Form.Item 
                        label={<span style={{fontSize: '12px', opacity: 0}}>.</span>} 
                        name="cpu" 
                        style={{marginBottom: 8}}
                      >
                        <Input placeholder="限制CPU" size="small" />
                      </Form.Item>
                    </Col>
                  </Row>
                </Col>
                <Col span={12}>
                  <Row gutter={[4, 0]}>
                    <Col span={11}>
                      <Form.Item 
                        label={<span style={{fontSize: '12px'}}>内存配置</span>} 
                        name="request_memory" 
                        style={{marginBottom: 8}}
                      >
                        <Input placeholder="请求内存" size="small" />
                      </Form.Item>
                    </Col>
                    <Col span={2} style={{display: 'flex', alignItems: 'center', justifyContent: 'center', marginTop: 22}}>
                      <ArrowRightOutlined style={{color: '#999', fontSize: '12px'}} />
                    </Col>
                    <Col span={11}>
                      <Form.Item 
                        label={<span style={{fontSize: '12px', opacity: 0}}>.</span>} 
                        name="memory" 
                        style={{marginBottom: 8}}
                      >
                        <Input placeholder="限制内存" size="small" />
                      </Form.Item>
                    </Col>
                  </Row>
                </Col>
                <Col span={12}>
                  <Form.Item label={<span style={{fontSize: '12px'}}>基础镜像</span>} name="base_image" style={{marginBottom: 8}}>
                    <Input placeholder="请输入镜像地址" size="small" />
                  </Form.Item>
                </Col>
              </Row>
            </Panel>
            
            <Panel header={<Text style={{fontSize: '13px'}}><RocketOutlined /> 命令和路径配置</Text>} key="command">
              <Row gutter={[8, 0]}>
                <Col span={6}>
                  <Form.Item label={<span style={{fontSize: '12px'}}>工作目录</span>} name="work_dir" style={{marginBottom: 8}}>
                    <Input placeholder="例如: /app" size="small" />
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item label={<span style={{fontSize: '12px'}}>日志目录</span>} name="log_dir" style={{marginBottom: 8}}>
                    <Input placeholder="例如: /app/logs" size="small" />
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item label={<span style={{fontSize: '12px'}}>启动命令</span>} name="start_command" style={{marginBottom: 8}}>
                    <Input placeholder="例如: npm start" size="small" />
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item label={<span style={{fontSize: '12px'}}>停止命令</span>} name="stop_command" style={{marginBottom: 8}}>
                    <Input placeholder="例如: kill -15 1" size="small" />
                  </Form.Item>
                </Col>
              </Row>
            </Panel>
          </Collapse>
        </Form>
      </>
    );
  };
  
  // 查看模式下的配置展示
  const renderViewMode = () => {    
    const formatCpuPair = (request: string | undefined, limit: string | undefined) => {
      return `${request || '100m'} → ${limit || '3000m'}`;
    };
    
    const formatMemoryPair = (request: string | undefined, limit: string | undefined) => {
      return `${request || '256Mi'} → ${limit || '2048Mi'}`;
    };
    
    return (
      <>
        <Collapse 
          defaultActiveKey={['resource']}
          bordered={false}
          expandIcon={({ isActive }) => (
            <CaretRightOutlined rotate={isActive ? 90 : 0} style={{fontSize: '12px'}} />
          )}
          style={{backgroundColor: 'transparent', marginBottom: 0}}
        >
          <Panel 
            header={<Text style={{fontSize: '13px'}}><CloudOutlined /> 资源配置</Text>} 
            key="resource"
            style={{borderBottom: 0}}
          >
            <Row gutter={[8, 0]} style={{margin: 0}}>
              <Col span={6}>
                <ConfigItem 
                  label="副本数" 
                  icon={<AppstoreOutlined style={{fontSize: '11px'}} />} 
                  appValue={appSetting.replicas || 1} 
                  groupValue={groupSetting?.replicas} 
                  color="blue"
                />
              </Col>
              <Col span={6}>
                <ConfigItem 
                  label="命名空间" 
                  icon={<FolderOutlined style={{fontSize: '11px'}} />} 
                  appValue={appSetting.namespace || 'default'} 
                  groupValue={groupSetting?.namespace} 
                  color="blue"
                />
              </Col>
              <Col span={6}>
                <ConfigItem 
                  label="CPU配置" 
                  icon={<PartitionOutlined style={{fontSize: '11px'}} />} 
                  appValue={formatCpuPair(appSetting.request_cpu, appSetting.cpu)}
                  groupValue={groupSetting?.request_cpu ? formatCpuPair(groupSetting.request_cpu, groupSetting.cpu) : undefined}
                  color="green"
                />
              </Col>
              <Col span={6}>
                <ConfigItem 
                  label="内存配置" 
                  icon={<PartitionOutlined style={{fontSize: '11px'}} />} 
                  appValue={formatMemoryPair(appSetting.request_memory, appSetting.memory)}
                  groupValue={groupSetting?.request_memory ? formatMemoryPair(groupSetting.request_memory, groupSetting.memory) : undefined}
                  color="orange"
                />
              </Col>
              <Col span={12}>
                <ConfigItem 
                  label="基础镜像" 
                  icon={<CodeOutlined style={{fontSize: '11px'}} />} 
                  appValue={appSetting.base_image || '-'}
                  groupValue={groupSetting?.base_image}
                  color="purple"
                />
              </Col>
            </Row>
          </Panel>
          
          <Panel 
            header={<Text style={{fontSize: '13px'}}><RocketOutlined /> 命令和路径配置</Text>} 
            key="command"
          >
            <Row gutter={[8, 0]} style={{margin: 0}}>
              <Col span={6}>
                <ConfigItem 
                  label="启动命令" 
                  icon={<CodeOutlined style={{fontSize: '11px'}} />} 
                  appValue={appSetting.start_command || '-'}
                  groupValue={groupSetting?.start_command}
                />
              </Col>
              <Col span={6}>
                <ConfigItem 
                  label="停止命令" 
                  icon={<CodeOutlined style={{fontSize: '11px'}} />} 
                  appValue={appSetting.stop_command || '-'}
                  groupValue={groupSetting?.stop_command}
                />
              </Col>
              <Col span={6}>
                <ConfigItem 
                  label="工作目录" 
                  icon={<FolderOutlined style={{fontSize: '11px'}} />} 
                  appValue={appSetting.work_dir || '/app'}
                  groupValue={groupSetting?.work_dir}
                />
              </Col>
              <Col span={6}>
                <ConfigItem 
                  label="日志目录" 
                  icon={<FileTextOutlined style={{fontSize: '11px'}} />} 
                  appValue={appSetting.log_dir || '/app/logs'}
                  groupValue={groupSetting?.log_dir}
                />
              </Col>
            </Row>
          </Panel>
        </Collapse>
      </>
    );
  };

  return (
    <div className="group-runtime-config" style={{padding: '0 4px'}}>
      <div style={{ 
        marginBottom: 8, 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        borderBottom: '1px solid #f0f0f0',
        paddingBottom: 8
      }}>
        <div style={{display: 'flex', alignItems: 'center'}}>
          <SettingOutlined style={{fontSize: '14px', marginRight: 6}} />
          <Text strong style={{fontSize: '14px', margin: 0}}>
            分组运行时配置
          </Text>
        </div>
        <div>
          <Tooltip title="绿色文字表示分组自定义配置，括号中显示应用默认配置">
            <InfoCircleOutlined style={{fontSize: '12px', color: '#999'}} />
          </Tooltip>
        </div>
      </div>
      
      {editMode ? renderEditForm() : renderViewMode()}
    </div>
  );
};

export default GroupRuntimeConfig; 