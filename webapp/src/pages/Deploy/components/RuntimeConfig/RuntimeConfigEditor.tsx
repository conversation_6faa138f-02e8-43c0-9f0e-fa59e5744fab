import React, { useState, useEffect } from 'react';
import { 
  Modal, Form, Input, InputNumber, Button, Space, 
  Card, Divider, Row, Col, message, Select,
  Typography, Tooltip
} from 'antd';
import {
  CodeOutlined, ApiOutlined,
  FolderOutlined, FileTextOutlined,
  SettingOutlined, RocketOutlined,
  CloudServerOutlined, CloudOutlined, GlobalOutlined,
  AppstoreOutlined, PartitionOutlined,
  QuestionCircleOutlined
} from '@ant-design/icons';

const { Title, Text } = Typography;
const { Option } = Select;

export interface RuntimeConfigEditorProps {
  visible: boolean;
  appData: API.AppData | null;
  onCancel: () => void;
  onSave: (updatedSettings: Partial<API.AppSettings>) => Promise<boolean>;
}

const RuntimeConfigEditor: React.FC<RuntimeConfigEditorProps> = ({
  visible,
  appData,
  onCancel,
  onSave
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (visible && appData?.app_settings) {
      form.setFieldsValue({
        // 命令配置
        start_command: appData.app_settings.start_command,
        stop_command: appData.app_settings.stop_command,
        
        // 路径配置
        work_dir: appData.app_settings.work_dir,
        log_dir: appData.app_settings.log_dir,
        config_dir: appData.app_settings.config_dir,
        
        // 资源配置
        replicas: appData.app_settings.replicas || 1,
        request_cpu: appData.app_settings.request_cpu || '100m',
        request_memory: appData.app_settings.request_memory || '256Mi',
        limit_cpu: appData.app_settings.limit_cpu || '500m',
        limit_memory: appData.app_settings.limit_memory || '512Mi',
        
        // 服务配置
        svc_port: appData.app_settings.svc_port || 80,
        pod_port: appData.app_settings.pod_port || 8080,
        svc_address: appData.app_settings.svc_address,
      });
    }
  }, [visible, appData, form]);

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);
      
      const success = await onSave(values);
      if (success) {
        message.success('运行时配置已成功保存');
        onCancel();
      }
    } catch (error) {
      console.error('表单验证失败:', error);
    } finally {
      setLoading(false);
    }
  };

  if (!appData) {
    return null;
  }

  return (
    <Modal
      title={
        <Space>
          <SettingOutlined />
          <span>编辑运行时配置</span>
        </Space>
      }
      open={visible}
      width={800}
      onCancel={onCancel}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          取消
        </Button>,
        <Button 
          key="submit" 
          type="primary" 
          loading={loading} 
          onClick={handleSubmit}
        >
          保存配置
        </Button>
      ]}
      destroyOnHidden={true}
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={appData?.app_settings || {}}
      >
        {/* 命令配置 */}
        <Card 
          title={
            <Space>
              <RocketOutlined />
              <Text strong>命令配置</Text>
            </Space>
          }
          size="small" 
          style={{ marginBottom: 16 }}
          variant="borderless"
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label={
                  <Space>
                    <CodeOutlined />
                    <span>启动命令</span>
                    <Tooltip title="应用启动时执行的命令">
                      <QuestionCircleOutlined style={{ color: '#999' }} />
                    </Tooltip>
                  </Space>
                }
                name="start_command"
              >
                <Input.TextArea 
                  placeholder="例如: java -jar app.jar" 
                  autoSize={{ minRows: 2, maxRows: 4 }} 
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label={
                  <Space>
                    <CodeOutlined />
                    <span>停止命令</span>
                    <Tooltip title="应用停止时执行的命令">
                      <QuestionCircleOutlined style={{ color: '#999' }} />
                    </Tooltip>
                  </Space>
                }
                name="stop_command"
              >
                <Input.TextArea 
                  placeholder="例如: kill -15 $PID" 
                  autoSize={{ minRows: 2, maxRows: 4 }} 
                />
              </Form.Item>
            </Col>
          </Row>
        </Card>

        {/* 路径配置 */}
        <Card 
          title={
            <Space>
              <FolderOutlined />
              <Text strong>路径配置</Text>
            </Space>
          }
          size="small" 
          style={{ marginBottom: 16 }}
          variant="borderless"
        >
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                label={
                  <Space>
                    <FolderOutlined />
                    <span>工作目录</span>
                  </Space>
                }
                name="work_dir"
              >
                <Input placeholder="/app/workspace" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label={
                  <Space>
                    <FileTextOutlined />
                    <span>日志目录</span>
                  </Space>
                }
                name="log_dir"
              >
                <Input placeholder="/app/logs" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label={
                  <Space>
                    <FileTextOutlined />
                    <span>配置目录</span>
                  </Space>
                }
                name="config_dir"
              >
                <Input placeholder="/app/config" />
              </Form.Item>
            </Col>
          </Row>
        </Card>
          
        {/* 资源配置 */}
        <Card 
          title={
            <Space>
              <CloudOutlined />
              <Text strong>资源配置</Text>
            </Space>
          }
          size="small" 
          style={{ marginBottom: 16 }}
          variant="borderless"
        >
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                label={
                  <Space>
                    <AppstoreOutlined />
                    <span>副本数量</span>
                  </Space>
                }
                name="replicas"
                rules={[
                  { required: true, message: '请输入副本数量' },
                  { type: 'number', min: 1, max: 10, message: '副本数量必须在1-10之间' }
                ]}
              >
                <InputNumber min={1} max={10} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label={
                  <Space>
                    <PartitionOutlined />
                    <span>请求CPU</span>
                    <Tooltip title="容器请求的CPU资源量，例如: 100m 表示0.1核心">
                      <QuestionCircleOutlined style={{ color: '#999' }} />
                    </Tooltip>
                  </Space>
                }
                name="request_cpu"
                rules={[{ required: true, message: '请输入CPU请求量' }]}
              >
                <Input placeholder="例如: 100m" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label={
                  <Space>
                    <PartitionOutlined />
                    <span>请求内存</span>
                    <Tooltip title="容器请求的内存资源量，例如: 256Mi 表示256兆字节">
                      <QuestionCircleOutlined style={{ color: '#999' }} />
                    </Tooltip>
                  </Space>
                }
                name="request_memory"
                rules={[{ required: true, message: '请输入内存请求量' }]}
              >
                <Input placeholder="例如: 256Mi" />
              </Form.Item>
            </Col>
          </Row>
          
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label={
                  <Space>
                    <PartitionOutlined />
                    <span>CPU限制</span>
                    <Tooltip title="容器可使用的最大CPU资源量">
                      <QuestionCircleOutlined style={{ color: '#999' }} />
                    </Tooltip>
                  </Space>
                }
                name="limit_cpu"
              >
                <Input placeholder="例如: 500m" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label={
                  <Space>
                    <PartitionOutlined />
                    <span>内存限制</span>
                    <Tooltip title="容器可使用的最大内存资源量">
                      <QuestionCircleOutlined style={{ color: '#999' }} />
                    </Tooltip>
                  </Space>
                }
                name="limit_memory"
              >
                <Input placeholder="例如: 512Mi" />
              </Form.Item>
            </Col>
          </Row>
        </Card>
        
        {/* 服务配置 */}
        <Card 
          title={
            <Space>
              <GlobalOutlined />
              <Text strong>服务配置</Text>
            </Space>
          }
          size="small"
          variant="borderless"
        >
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                label={
                  <Space>
                    <ApiOutlined />
                    <span>Service端口</span>
                  </Space>
                }
                name="svc_port"
                rules={[
                  { required: true, message: '请输入Service端口' },
                  { type: 'number', min: 1, max: 65535, message: '端口号必须在1-65535之间' }
                ]}
              >
                <InputNumber min={1} max={65535} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label={
                  <Space>
                    <ApiOutlined />
                    <span>Pod端口</span>
                  </Space>
                }
                name="pod_port"
                rules={[
                  { required: true, message: '请输入Pod端口' },
                  { type: 'number', min: 1, max: 65535, message: '端口号必须在1-65535之间' }
                ]}
              >
                <InputNumber min={1} max={65535} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label={
                  <Space>
                    <GlobalOutlined />
                    <span>服务地址</span>
                  </Space>
                }
                name="svc_address"
              >
                <Input placeholder="service-name.namespace.svc.cluster.local" />
              </Form.Item>
            </Col>
          </Row>
        </Card>

        <div style={{ marginTop: 16, backgroundColor: '#f9f9f9', padding: '8px 12px', borderRadius: 2 }}>
          <Space>
            <QuestionCircleOutlined style={{ color: '#1890ff' }} />
            <Text type="secondary" style={{ fontSize: '12px' }}>
              运行时配置决定了应用在Kubernetes集群中的部署和运行方式
            </Text>
          </Space>
        </div>
      </Form>
    </Modal>
  );
};

export default RuntimeConfigEditor; 