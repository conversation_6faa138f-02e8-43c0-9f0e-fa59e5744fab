/* ConfigArea 组件样式 */

/* 卡片容器 */
.contentCard {
  border-radius: 8px;
}

.primaryCard {
  border-top: 3px solid #1890ff;
}

/* 配置分组相关样式 */
.configWrapper {
  margin-top: 4px;
}

.runtimeConfigWrapper {
  padding: 8px 0;
}

.runtimeConfigForm {
  max-width: 100%;
}

/* 配置文件列表 */
.configList {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-top: 4px;
}

/* 配置文件项 */
.configFileItem {
  margin-bottom: 8px;
  background-color: white;
  border-radius: 6px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
  border: 1px solid #f0f0f0;
  overflow: hidden;
  transition: all 0.2s ease;
  
  &:hover {
    box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08);
    transform: translateY(-1px);
  }
}

.configFileHeader {
  padding: 6px 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px dashed #f5f5f5;
}

.configFileContent {
  display: flex;
  align-items: center;
  flex: 1;
  overflow: hidden;
  height: 100%;
}

.configFileIcon {
  min-width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  margin-right: 6px;
  font-size: 12px;
}

.configFileInfo {
  flex: 1;
  overflow: hidden;
  display: flex;
  align-items: center;
  height: 100%;
}

.configFileName {
  font-weight: 500;
  font-size: 13px;
  margin-right: 6px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
}

.configFilePath {
  font-size: 11px;
  color: #999;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.configFileTag {
  margin-right: 3px;
  font-size: 10px;
  padding: 0 4px;
  line-height: 16px;
  height: 16px;
}

.configFileActions {
  display: flex;
  margin-left: 6px;
}

.configFileActionButton {
  padding: 2px 4px;
  height: 22px;
  min-width: 22px;
  margin-left: 2px;
  
  &:hover {
    background-color: #f5f5f5;
    border-radius: 4px;
  }
}

/* 空状态 */
.emptyState {
  padding: 16px 0;
  text-align: center;
}

.emptyStateText {
  margin-bottom: 8px;
  font-size: 14px;
  color: #8c8c8c;
}

/* 加载状态 */
.loadingState {
  text-align: center;
  padding: 20px 0;
} 