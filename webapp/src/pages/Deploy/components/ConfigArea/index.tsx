import {
  EditOutlined,
  EyeOutlined,
} from '@ant-design/icons';
import {
  <PERSON>ton,
  Card,
  Col,
  Descriptions,
  Form,
  Input,
  InputNumber,
  Row,
  Select,
  Space,
  Tabs,
} from 'antd';
import React, { useEffect, useState } from 'react';
import styles from './style.less';
import GroupConfigFiles from '../ConfigFiles/GroupConfigFiles';
import useDeploy from '@/models/deploy';
import { GroupRuntimeConfig } from '../RuntimeConfig';

const { Option } = Select;

export interface ConfigAreaProps {
  editMode: boolean;
  form: any;
  toggleEditMode: () => void;
  healthCheckForm: any;
}

// 定义组件内部使用的样式
const componentStyles = {
  contentCard: {
    borderRadius: 8,
  },
  primaryCard: {
    borderTop: '3px solid #1890ff'
  },
};

const ConfigArea: React.FC<ConfigAreaProps> = ({
  editMode,
  form,
  toggleEditMode,
  healthCheckForm,
}) => {
  const { deploy } = useDeploy();
  const [namespaces, setNamespaces] = useState<string[]>([
    'default',
    'kube-system',
    'production',
    'staging',
    'testing',
    'dev',
    'monitor',
    'logging',
  ]);
  const [activeKey, setActiveKey] = useState<string>("business");

  // 渲染分组探活配置
  const renderHealthCheckConfig = () => {
    if (!deploy.groupId) return null;

    return (
      <div>
        <Descriptions column={2} size="small" bordered>
          <Descriptions.Item label="健康检查路径">
            /health
          </Descriptions.Item>
          <Descriptions.Item label="检查端口">
            8080
          </Descriptions.Item>
          <Descriptions.Item label="启动探测延迟">
            30秒
          </Descriptions.Item>
          <Descriptions.Item label="存活探测间隔">
            10秒
          </Descriptions.Item>
          <Descriptions.Item label="就绪探测间隔">
            5秒
          </Descriptions.Item>
          <Descriptions.Item label="失败重试次数">
            3次
          </Descriptions.Item>
          <Descriptions.Item label="检查超时时间">
            2秒
          </Descriptions.Item>
          <Descriptions.Item label="配置操作">
            <Button 
              type="link" 
              icon={<EditOutlined />} 
              onClick={() => {
                healthCheckForm.setFieldsValue({});
              }}
              size="small"
            >
              编辑探活配置
            </Button>
          </Descriptions.Item>
        </Descriptions>
      </div>
    );
  };

  // 渲染Tab栏右侧的按钮组
  const renderExtraButtons = () => {
    return (
      <Space>
        {activeKey === 'runtime' && (
          <>
            {!editMode && (
              <Button
                size="small"
                type="primary"
                icon={<EditOutlined />}
                onClick={toggleEditMode}
              >
                编辑
              </Button>
            )}
          </>
        )}
      </Space>
    );
  };

  return (
    <Card 
      className={`${styles.contentCard || ""} ${styles.primaryCard || ""}`}
      style={{...componentStyles.contentCard, ...componentStyles.primaryCard}}
    >
      <div style={{ marginTop: "-24px" }}>
        <Tabs
          defaultActiveKey="business"
          activeKey={activeKey}
          onChange={setActiveKey}
          size="small"
          tabBarExtraContent={renderExtraButtons()}
          items={[
            {
              key: 'business',
              label: '分组配置文件',
              children: (
                <GroupConfigFiles />
              ),
            },
            {
              key: 'runtime',
              label: '分组运行时配置',
              children: (
                <div className={styles.runtimeConfigWrapper}>
                  <GroupRuntimeConfig 
                    editMode={editMode}
                    form={form}
                    toggleEditMode={toggleEditMode}
                  />
                </div>
              ),
            },
            {
              key: 'health',
              label: '分组探活配置',
              children: (
                <div className={styles.runtimeConfigWrapper}>
                  {renderHealthCheckConfig()}
                </div>
              ),
            },
          ]}
        />
      </div>
    </Card>
  );
};

export default ConfigArea; 