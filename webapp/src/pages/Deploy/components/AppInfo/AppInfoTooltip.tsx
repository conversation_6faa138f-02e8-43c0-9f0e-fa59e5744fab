import React from 'react';
import { Descriptions, Button, message, Space, Tooltip } from 'antd';
import { CopyOutlined, InfoCircleOutlined } from '@ant-design/icons';

interface AppInfoTooltipProps {
  appData: API.AppData | null;
  inline?: boolean; // 添加参数控制是否为行内显示模式
}

const AppInfoTooltip: React.FC<AppInfoTooltipProps> = ({
  appData,
  inline = false,
}) => {
  if (!appData) {
    return null;
  }

  // 拼接节点信息
  const getNodeInfo = () => {
    const { corp, owt, pdl, sg, srv } = appData.application;
    // 去掉srv结尾的下划线
    const srvValue = srv?.endsWith('_') ? srv.slice(0, -1) : srv;
    return `${corp}${owt}${pdl}${sg}${srvValue}`;
  };

  // 复制功能
  const handleCopy = async (text: string, label: string) => {
    try {
      await navigator.clipboard.writeText(text);
      message.success(`${label}已复制到剪贴板`);
    } catch (err) {
      // 降级处理，使用传统方法
      const textArea = document.createElement('textarea');
      textArea.value = text;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      message.success(`${label}已复制到剪贴板`);
    }
  };

  // 带复制功能的内容组件
  const CopyableContent: React.FC<{ text: string; label: string }> = ({ text, label }) => (
    <Space>
      <span>{text}</span>
      <Button
        type="text"
        size="small"
        icon={<CopyOutlined />}
        onClick={() => handleCopy(text, label)}
        style={{ padding: '0 4px' }}
      />
    </Space>
  );

  // 如果是内联模式，只显示应用名称和复制按钮
  if (inline) {
    return (
      <Space>
        {appData.application.name && (
          <Tooltip title="应用名称">
            <span>{appData.application.name}</span>
          </Tooltip>
        )}
        {appData.app_settings?.code_repository && (
          <Tooltip title="复制代码库地址">
            <Button
              type="text"
              size="small"
              icon={<CopyOutlined />}
              onClick={() => handleCopy(appData.app_settings.code_repository, "代码库")}
            />
          </Tooltip>
        )}
        <Tooltip title="查看详细信息">
          <InfoCircleOutlined />
        </Tooltip>
      </Space>
    );
  }

  return (
    <Descriptions column={1} size="small" bordered style={{ backgroundColor: 'white' }}>
      <Descriptions.Item label="应用名称">{appData.application.name}</Descriptions.Item>
      <Descriptions.Item label="应用编码">{appData.application.code}</Descriptions.Item>
      <Descriptions.Item label="类型">{appData.application.app_type.name}</Descriptions.Item>
      <Descriptions.Item label="级别">{appData.application.level}</Descriptions.Item>
      <Descriptions.Item label="开发语言">{appData.application.language}</Descriptions.Item>
      <Descriptions.Item label="代码库">
        <CopyableContent text={appData.app_settings.code_repository} label="代码库" />
      </Descriptions.Item>
      <Descriptions.Item label="节点信息">
        <CopyableContent text={getNodeInfo()} label="节点信息" />
      </Descriptions.Item>
    </Descriptions>
  );
};

export default AppInfoTooltip; 