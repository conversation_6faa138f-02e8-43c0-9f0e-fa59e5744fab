// 部署状态组件样式 - 保持与原来一致
.contentCard {
  background: white;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.04);
  border: 1px solid #f0f2f5;
  margin-bottom: 6px;
  overflow: hidden;

  .ant-card-body {
    padding: 12px;
  }
}

.deployStatusLogCard {
  .ant-card-body {
    padding: 12px;
  }
}

.deployStatusLogHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .deployStatusTitle {
    display: flex;
    align-items: center;
    font-weight: 600;
    font-size: 14px;
  }
}

.deployStatusLogContent {
  display: flex;
  flex-direction: column;
}

.deployStatusSection {
  margin-bottom: 8px;

  .deployProgressInfo {
    display: flex;
    flex-direction: column;
    gap: 12px;

    .deployTimeInfo {
      display: flex;
      justify-content: space-between;
      font-size: 12px;

      .timeLabel {
        color: #8c8c8c;
        margin-right: 4px;
      }

      .timeValue {
        color: #333;
        font-weight: 500;
      }
    }

    .deployProgress {
      .ant-progress {
        margin-bottom: 4px;
      }

      .stepInfo {
        font-size: 12px;
        color: #8c8c8c;

        .currentStep {
          font-weight: 500;
        }
      }
    }
  }

  .deploySteps {
    display: flex;
    justify-content: space-between;
    margin-top: 8px;
    padding: 0 4px;

    .deployStep {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 18%;
      position: relative;

      &:not(:last-child)::after {
        content: '';
        position: absolute;
        top: 12px;
        right: -50%;
        width: 100%;
        height: 2px;
        background-color: #f0f0f0;
        z-index: 1;
      }

      &.stepCompleted {
        .stepIcon {
          background-color: #52c41a;
          color: white;
        }

        &:not(:last-child)::after {
          background-color: #52c41a;
        }
      }

      &.stepCurrent {
        .stepIcon {
          background-color: #1890ff;
          color: white;
          box-shadow: 0 0 0 4px rgba(24, 144, 255, 0.2);
        }
      }

      &.stepPending {
        .stepIcon {
          background-color: #f0f0f0;
          color: #8c8c8c;
        }
      }

      .stepIcon {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        margin-bottom: 8px;
        position: relative;
        z-index: 2;
        transition: all 0.3s ease;
      }

      .stepName {
        font-size: 12px;
        color: #333;
        text-align: center;
      }
    }
  }

  .confirmationArea {
    animation: fadeIn 0.3s ease-in-out;
  }
}

.deployLogSection {
  display: flex;
  flex-direction: column;
  gap: 12px;

  .deployLogHeader {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 8px;
  }

  .logContainer {
    height: 100%;
    max-height: 300px;
    overflow-y: auto;
    font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
    font-size: 12px;
    line-height: 1.5;
    background-color: #f5f5f5;
    border-radius: 4px;
    padding: 8px;

    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background: #ccc;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-track {
      background: #f0f0f0;
      border-radius: 3px;
    }

    .logItem {
      white-space: pre-wrap;
      word-break: break-all;
      padding: 2px 0;
      display: flex;
      align-items: flex-start;

      .logTimestamp {
        color: #888;
        margin-right: 6px;
        flex-shrink: 0;
      }

      .logType {
        width: 70px;
        margin-right: 8px;
        flex-shrink: 0;
        font-weight: 500;
      }

      .logContent {
        flex: 1;
      }

      &.log-info {
        color: #333;

        .logType {
          color: #1890ff;
        }
      }

      &.log-warning {
        color: #333;

        .logType {
          color: #faad14;
        }
      }

      &.log-error {
        color: #333;

        .logType {
          color: #f5222d;
        }
      }

      &.log-success {
        color: #333;

        .logType {
          color: #52c41a;
          font-weight: 600;
        }
      }
    }
  }

  .emptyLog {
    text-align: center;
    padding: 20px;
    color: #999;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .deployStatusLogCard {
    .deployStatusLogHeader {
      flex-direction: column;
      gap: 12px;
      align-items: flex-start;
    }
    
    .deployStatusSection {
      .deployProgressInfo {
        flex-direction: column;
        gap: 16px;
        
        .deployProgress {
          margin-left: 0;
        }
      }
      
      .deploySteps {
        justify-content: center;
      }
    }
  }
} 