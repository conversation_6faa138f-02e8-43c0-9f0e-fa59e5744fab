import React, { useEffect, useRef, useState } from 'react';
import {
  CheckCircleOutlined,
  CheckOutlined,
  ClearOutlined,
  ClockCircleOutlined,
  CloseCircleOutlined,
  InfoCircleOutlined,
  PauseCircleOutlined,
  PlayCircleOutlined,
  ReloadOutlined,
  WarningOutlined,
} from '@ant-design/icons';
import {
  Button,
  Card,
  Divider,
  Progress,
  Select,
  Space,
  Switch,
  Tag,
} from 'antd';

const { Option } = Select;
import { 
  DeployStatus as DeployStatusEnum,
  DEPLOY_STATUS_CONFIG,
  DeployLogItem,
  DeployStatusInfo,
  mapStatusNumberToString
} from '../../types/common';
import styles from './DeployStatus.less';

export interface DeployStatusProps {
  // 可选的外部传入的初始状态
  initialStatus?: Partial<DeployStatusInfo>;
}

const DeployStatus: React.FC<DeployStatusProps> = ({
  initialStatus
}) => {
  // 部署步骤
  const deploySteps = [
    '准备环境',
    '拉取代码',
    '构建镜像',
    '推送镜像',
    '部署应用',
    '功能确认',
    '完成部署',
  ];

  // 内部状态
  const [isDeploying, setIsDeploying] = useState<boolean>(true);
  
  // 初始化部署状态信息
  const [currentDeployStatus, setCurrentDeployStatus] = useState<DeployStatusInfo>({
    status: DeployStatusEnum.Running,
    progress: 45,
    startTime: Date.now(),
    estimatedEndTime: Date.now() + 5 * 60 * 1000, // 5分钟后
    currentStep: '构建镜像',
    totalSteps: deploySteps.length,
    currentStepIndex: 2,
    needsConfirmation: 0,
    ...(initialStatus || {})
  });

  // 初始化日志数据
  const [logContent, setLogContent] = useState<DeployLogItem[]>([
    { type: 'info', content: '开始部署流程', timestamp: Date.now() - 120000 },
    { type: 'info', content: '检查部署环境', timestamp: Date.now() - 115000 },
    { type: 'info', content: '环境检查通过', timestamp: Date.now() - 110000 },
    { type: 'info', content: '拉取代码仓库', timestamp: Date.now() - 100000 },
    { type: 'info', content: '代码拉取完成', timestamp: Date.now() - 90000 },
    { type: 'info', content: '开始构建镜像', timestamp: Date.now() - 80000 },
    {
      type: 'warning',
      content: '构建过程中有非阻塞警告，请检查',
      timestamp: Date.now() - 70000,
    },
    {
      type: 'info',
      content: '镜像构建完成: registry.sprucetec.com/dsr/api:v2.5.0-20231115',
      timestamp: Date.now() - 60000,
    }
  ]);
  
  // 日志过滤状态
  const [logFilter, setLogFilter] = React.useState<string>('all');
  const [autoScroll, setAutoScroll] = React.useState<boolean>(true);
  const logContainerRef = useRef<HTMLDivElement>(null);
  
  // 自动滚动到底部
  useEffect(() => {
    if (autoScroll && logContainerRef.current) {
      logContainerRef.current.scrollTop = logContainerRef.current.scrollHeight;
    }
  }, [logContent, autoScroll]);
  
  // 过滤日志
  const filteredLogs = React.useMemo(() => {
    if (logFilter === 'all') return logContent;
    return logContent.filter((log) => log.type === logFilter);
  }, [logContent, logFilter]);
  
  // 计算日志统计信息
  const logStats = React.useMemo(() => {
    const errorCount = logContent.filter((log) => log.type === 'error').length;
    const warningCount = logContent.filter((log) => log.type === 'warning').length;
    return { errorCount, warningCount };
  }, [logContent]);
  
  // 处理暂停部署
  const handlePauseDeployment = () => {
    setCurrentDeployStatus((prev) => ({
      ...prev,
      status: DeployStatusEnum.Paused,
    }));
    
    // 添加日志
    addNewLog('warning', '部署已暂停');
  };

  // 处理继续部署
  const handleResumeDeployment = () => {
    setCurrentDeployStatus((prev) => ({
      ...prev,
      status: DeployStatusEnum.Running,
    }));
    
    // 添加日志
    addNewLog('info', '部署已恢复');
  };

  // 处理刷新部署状态
  const handleRefreshStatus = () => {
    // 模拟刷新部署状态
    const newProgress = Math.min(
      currentDeployStatus.progress + 10,
      100,
    );
    let newStepIndex = Math.min(
      Math.floor(newProgress / 15),
      currentDeployStatus.totalSteps - 1,
    );

    // 特殊处理：当到达第5步（部署应用）时，进入等待确认状态
    if (newStepIndex === 5 && currentDeployStatus.currentStepIndex < 5) {
      proceedToConfirmation();
      return;
    }

    setCurrentDeployStatus((prev) => ({
      ...prev,
      progress: newProgress,
      currentStepIndex: newStepIndex,
      currentStep: deploySteps[newStepIndex],
      status: newProgress >= 100 ? DeployStatusEnum.Completed : prev.status,
    }));

    if (newProgress >= 100) {
      setIsDeploying(false);
    }
    
    // 添加日志
    addNewLog('info', `部署状态已更新: ${newProgress}%`);
  };

  // 处理切换部署状态模拟
  const handleToggleDeploying = (checked: boolean) => {
    setIsDeploying(checked);
    if (checked) {
      setCurrentDeployStatus((prev) => ({
        ...prev,
        status: DeployStatusEnum.Running,
        progress: 45,
      }));
      
      // 添加日志
      addNewLog('info', '部署模拟已启动');
    } else {
      setCurrentDeployStatus((prev) => ({
        ...prev,
        status: DeployStatusEnum.Completed,
        progress: 100,
        currentStepIndex: currentDeployStatus.totalSteps - 1,
        currentStep: deploySteps[currentDeployStatus.totalSteps - 1],
      }));
      
      // 添加日志
      addNewLog('success', '部署模拟已完成');
    }
  };

  // 模拟添加新日志
  const addNewLog = (type: 'info' | 'warning' | 'error' | 'success', content: string) => {
    const newLog: DeployLogItem = {
      type,
      content,
      timestamp: Date.now(),
    };
    
    setLogContent((prev) => [...prev, newLog]);
  };

  // 添加随机日志（用于测试）
  const addRandomLog = () => {
    const types: Array<'info' | 'warning' | 'error' | 'success'> = [
      'info',
      'warning',
      'error',
      'success',
    ];
    const randomType = types[Math.floor(Math.random() * types.length)];
    
    const content = `这是一条${
      randomType === 'info'
        ? '信息'
        : randomType === 'warning'
        ? '警告'
        : randomType === 'error'
        ? '错误'
        : '成功'
    }日志`;
    
    addNewLog(randomType, content);
  };

  // 清空日志
  const clearLogs = () => {
    setLogContent([]);
  };
  
  // 模拟部署到确认步骤
  const proceedToConfirmation = () => {
    setCurrentDeployStatus((prev) => ({
      ...prev,
      status: DeployStatusEnum.Waiting,
      progress: 85,
      currentStep: '功能确认',
      currentStepIndex: 5,
      needsConfirmation: 1,
    }));
    
    // 添加日志
    addNewLog('info', '应用部署完成，等待功能确认');
    addNewLog('warning', '请验证应用功能是否正常，确认后点击"确认功能正常"按钮继续');
  };

  // 确认功能正常
  const confirmFunctionality = () => {
    setCurrentDeployStatus((prev) => ({
      ...prev,
      status: DeployStatusEnum.Running,
      progress: 95,
      currentStep: '完成部署',
      currentStepIndex: 6,
      needsConfirmation: 0,
    }));
    
    // 添加日志
    addNewLog('success', '功能确认完成，继续部署流程');

    // 模拟最终完成
    setTimeout(() => {
      setCurrentDeployStatus((prev) => ({
        ...prev,
        status: DeployStatusEnum.Completed,
        progress: 100,
        currentStep: '完成部署',
        currentStepIndex: 6,
      }));
      
      addNewLog('success', '部署流程全部完成！');
    }, 2000);
  };

  // 取消部署（功能异常）
  const cancelDeployment = () => {
    setCurrentDeployStatus((prev) => ({
      ...prev,
      status: DeployStatusEnum.Cancelled,
      progress: 85,
      needsConfirmation: 0,
    }));
    
    // 添加日志
    addNewLog('error', '功能确认失败，部署已取消');
  };

  // 渲染日志内容
  const renderLogContent = () => {
    return (
      <div className={styles.deployLogSection}>
        {/* 日志头部控制区域 */}
        <div className={styles.deployLogHeader}>
          <div
            style={{
              display: 'flex',
              justifyContent: 'space-between',
              width: '100%',
            }}
          >
            <Space>
              <Select
                value={logFilter}
                onChange={setLogFilter}
                size="small"
                style={{ width: 100 }}
                dropdownMatchSelectWidth={false}
              >
                <Option value="all">全部</Option>
                <Option value="info">信息</Option>
                <Option value="warning">警告</Option>
                <Option value="error">错误</Option>
                <Option value="success">成功</Option>
              </Select>
              <Button
                type="text"
                size="small"
                icon={
                  autoScroll ? (
                    <PauseCircleOutlined />
                  ) : (
                    <PlayCircleOutlined />
                  )
                }
                onClick={() => setAutoScroll(!autoScroll)}
                title={autoScroll ? '暂停自动滚动' : '启用自动滚动'}
              />
              <Button
                type="text"
                size="small"
                icon={<ClearOutlined />}
                onClick={clearLogs}
                title="清空日志"
              />
              <Button type="primary" size="small" onClick={addRandomLog}>
                模拟新日志
              </Button>
              <Button 
                size="small" 
                onClick={proceedToConfirmation}
                disabled={currentDeployStatus.status === DeployStatusEnum.Waiting}
              >
                模拟到确认
              </Button>
            </Space>
            <Space>
              <Tag color="red" icon={<WarningOutlined />}>
                错误: {logStats.errorCount}
              </Tag>
              <Tag color="gold" icon={<WarningOutlined />}>
                警告: {logStats.warningCount}
              </Tag>
            </Space>
          </div>
        </div>
        
        <div className={styles.logContainer} ref={logContainerRef}>
          {filteredLogs.length > 0 ? (
            filteredLogs.map((log, index) => (
              <div
                key={index}
                className={`${styles.logItem} ${styles[`log-${log.type}`]}`}
              >
                <span className={styles.logTimestamp}>[{new Date(log.timestamp).toLocaleString('zh-CN')}]</span>
                <span className={styles.logType}>[{log.type.toUpperCase()}]</span>
                <span className={styles.logContent}>{log.content}</span>
              </div>
            ))
          ) : (
            <div className={styles.emptyLog}>暂无部署日志</div>
          )}
        </div>
      </div>
    );
  };

  if (!isDeploying) {
    return null;
  }

  // 获取状态的字符串表示
  const statusString = mapStatusNumberToString(currentDeployStatus.status);

  return (
    <Card
      className={`${styles.contentCard} ${styles.deployStatusLogCard}`}
      title={
        <div className={styles.deployStatusLogHeader}>
          <div className={styles.deployStatusTitle}>
            <span>部署状态</span>
            <Tag
              style={{
                color: DEPLOY_STATUS_CONFIG[statusString].color,
                backgroundColor: 'white',
                border: `1px solid ${
                  DEPLOY_STATUS_CONFIG[statusString].color
                }`,
                marginLeft: 8,
                fontWeight: 500,
                fontSize: '11px',
                padding: '0 6px',
                lineHeight: '18px',
              }}
            >
              {statusString}
            </Tag>
          </div>
          <Space>
            {currentDeployStatus.status === DeployStatusEnum.Running && (
              <Button
                size="small"
                danger
                icon={<PauseCircleOutlined />}
                onClick={handlePauseDeployment}
              >
                暂停
              </Button>
            )}
            {currentDeployStatus.status === DeployStatusEnum.Paused && (
              <Button
                size="small"
                type="primary"
                icon={<PlayCircleOutlined />}
                onClick={handleResumeDeployment}
              >
                继续
              </Button>
            )}
            <Button
              size="small"
              icon={<ReloadOutlined />}
              onClick={handleRefreshStatus}
            >
              刷新
            </Button>
            <Switch
              checkedChildren="模拟部署中"
              unCheckedChildren="模拟已完成"
              checked={isDeploying}
              onChange={handleToggleDeploying}
            />
          </Space>
        </div>
      }
    >
      <div className={styles.deployStatusLogContent}>
        {/* 部署状态部分 */}
        <div className={styles.deployStatusSection}>
          <div className={styles.deployProgressInfo}>
            <div className={styles.deployTimeInfo}>
              <div>
                <span className={styles.timeLabel}>开始时间：</span>
                <span className={styles.timeValue}>
                  {new Date(currentDeployStatus.startTime).toLocaleString('zh-CN')}
                </span>
              </div>
              <div>
                <span className={styles.timeLabel}>预计完成：</span>
                <span className={styles.timeValue}>
                  {new Date(currentDeployStatus.estimatedEndTime).toLocaleString('zh-CN')}
                </span>
              </div>
            </div>

            <div className={styles.deployProgress}>
              <Progress
                percent={currentDeployStatus.progress}
                status={
                  currentDeployStatus.status === DeployStatusEnum.Running
                    ? 'active'
                    : currentDeployStatus.status === DeployStatusEnum.Failed
                    ? 'exception'
                    : currentDeployStatus.status === DeployStatusEnum.Completed ||
                      currentDeployStatus.status === DeployStatusEnum.Confirmed
                    ? 'success'
                    : currentDeployStatus.status === DeployStatusEnum.Waiting
                    ? 'active'
                    : 'normal'
                }
                strokeColor={
                  DEPLOY_STATUS_CONFIG[statusString].color
                }
                format={(percent) => `${percent}%`}
              />
              <div className={styles.stepInfo}>
                <span className={styles.currentStep}>
                  当前步骤：{currentDeployStatus.currentStep} (
                  {currentDeployStatus.currentStepIndex + 1}/
                  {currentDeployStatus.totalSteps})
                </span>
              </div>
            </div>
          </div>

          <div className={styles.deploySteps}>
            {deploySteps.map((step, index) => (
              <div
                key={index}
                className={`${styles.deployStep} ${
                  index < currentDeployStatus.currentStepIndex
                    ? styles.stepCompleted
                    : index === currentDeployStatus.currentStepIndex
                    ? styles.stepCurrent
                    : styles.stepPending
                }`}
              >
                <div className={styles.stepIcon}>
                  {index < currentDeployStatus.currentStepIndex ? (
                    <CheckCircleOutlined />
                  ) : index === currentDeployStatus.currentStepIndex ? (
                    currentDeployStatus.status === DeployStatusEnum.Waiting && step === '功能确认' ? (
                      <WarningOutlined style={{ color: '#fa8c16' }} />
                    ) : (
                      <CheckOutlined
                        spin={currentDeployStatus.status === DeployStatusEnum.Running}
                      />
                    )
                  ) : (
                    <ClockCircleOutlined />
                  )}
                </div>
                <div className={styles.stepName}>{step}</div>
              </div>
            ))}
          </div>

          {/* 功能确认操作区域 */}
          {currentDeployStatus.status === DeployStatusEnum.Waiting && (
            <div className={styles.confirmationArea} style={{
              marginTop: '16px',
              padding: '16px',
              background: '#fff2e8',
              border: '1px solid #faad14',
              borderRadius: '6px'
            }}>
              <div style={{ marginBottom: '12px' }}>
                <span style={{ color: '#fa8c16', fontWeight: 'bold' }}>
                  ⚠️ 请确认应用功能是否正常
                </span>
              </div>
              <div style={{ 
                color: '#666', 
                fontSize: '12px', 
                marginBottom: '16px',
                lineHeight: 1.5
              }}>
                应用已成功部署，请测试核心功能是否正常运行。<br/>
                如功能正常，请点击"确认功能正常"继续部署；如有异常，请点击"功能异常"取消部署。
              </div>
              <Space>
                <Button
                  type="primary"
                  icon={<CheckCircleOutlined />}
                  onClick={confirmFunctionality}
                  style={{ background: '#52c41a', borderColor: '#52c41a' }}
                >
                  确认功能正常
                </Button>
                <Button
                  danger
                  icon={<CloseCircleOutlined />}
                  onClick={cancelDeployment}
                >
                  功能异常
                </Button>
              </Space>
            </div>
          )}
        </div>

        {/* 分隔线 */}
        <Divider style={{ margin: '16px 0' }}>部署日志</Divider>

        {/* 部署日志部分 */}
        {renderLogContent()}
      </div>
    </Card>
  );
};

export default DeployStatus; 