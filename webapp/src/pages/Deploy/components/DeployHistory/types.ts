import { DeployStatusNumber } from '../../types/common';

export type DeployStatus = DeployStatusNumber;

export interface DeployHistory {
  id: string;
  version: string;
  deployTime: string;
  deployType: '部署' | '编译部署' | '回滚部署';
  deployStrategy: '普通' | '蓝绿' | '金丝雀';
  operator: string;
  status: DeployStatus;
  duration: string;
  description: string;
  commitId: string;
  targetGroups: string[];
  groupName: string;
  config: {
    runtime: any;
  };
  logs: Array<{
    type: 'info' | 'warning' | 'error' | 'success';
    content: string;
    timestamp: string;
  }>;
}

export interface DeployHistoryProps {
  deployHistory: DeployHistory[];
  deployHistoryVisible: boolean;
  deployHistoryFilter: string;
  deployHistoryStatusFilter: string;
  deployHistoryTypeFilter: string;
  deployHistoryStrategyFilter: string;
  deployHistoryGroupFilter: string;
  deployHistoryGroups: string[];
  onToggleVisible: () => void;
  onFilterChange: (value: string) => void;
  onStatusFilterChange: (value: string) => void;
  onTypeFilterChange: (value: string) => void;
  onStrategyFilterChange: (value: string) => void;
  onGroupFilterChange: (value: string) => void;
  onViewDetail: (history: DeployHistory) => void;
  onRedeploy: (history: DeployHistory) => void;
  onRollback: (history: DeployHistory) => void;
} 