.deployHistoryDetailCard {
  margin-top: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  border-radius: 8px;
}

.header {
  margin-bottom: 24px;
}

.titleArea {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;

  h4 {
    margin-bottom: 0;
  }
}

.statusArea {
  display: flex;
  align-items: center;
}

.description {
  color: #666;
  font-size: 14px;
  margin-top: 4px;
}

.actionButtons {
  margin-left: 16px;
}

.infoDescriptions {
  margin-bottom: 24px;
}

.deploySteps {
  margin: 32px 0;
  padding: 16px;
  background-color: #fafafa;
  border-radius: 4px;
}

.subTasksWrapper {
  margin-top: 32px;
  padding-top: 16px;
  border-top: 1px dashed #e8e8e8;

  h5 {
    margin-bottom: 16px;
  }
} 