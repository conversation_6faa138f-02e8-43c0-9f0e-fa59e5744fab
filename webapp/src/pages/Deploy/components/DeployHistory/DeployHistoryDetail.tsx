import React, { useState, useEffect } from 'react';
import { Card, Descriptions, Steps, Table, Tag, Typography, Button, Tooltip, Badge } from 'antd';
import { 
  CheckCircleOutlined, 
  ClockCircleOutlined, 
  CloseCircleOutlined, 
  ExclamationCircleOutlined, 
  InfoCircleOutlined,
  LoadingOutlined,
  SyncOutlined,
  TeamOutlined,
} from '@ant-design/icons';
import { getDeployHistoryById, getDeploySubTasks, confirmDeploy, cancelDeploy } from '@/services/api/DeployTaskController';
import { useParams } from '@umijs/max';
import styles from './DeployHistoryDetail.less';

const { Text, Title } = Typography;

// 部署状态配置
const STATUS_CONFIG = {
  '排队中': { color: 'default', icon: <ClockCircleOutlined /> },
  '进行中': { color: 'processing', icon: <SyncOutlined spin /> },
  '暂停中': { color: 'warning', icon: <ExclamationCircleOutlined /> },
  '等待确认': { color: 'warning', icon: <ExclamationCircleOutlined /> },
  '已完成': { color: 'success', icon: <CheckCircleOutlined /> },
  '已确认': { color: 'success', icon: <CheckCircleOutlined /> },
  '已取消': { color: 'default', icon: <CloseCircleOutlined /> },
  '失败': { color: 'error', icon: <CloseCircleOutlined /> },
  '部分失败': { color: 'error', icon: <ExclamationCircleOutlined /> },
};

const DeployHistoryDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const [loading, setLoading] = useState<boolean>(true);
  const [historyDetail, setHistoryDetail] = useState<any>(null);
  const [subTasks, setSubTasks] = useState<any[]>([]);
  const [refreshKey, setRefreshKey] = useState<number>(0);

  // 加载部署历史详情
  useEffect(() => {
    const fetchDeployHistory = async () => {
      try {
        setLoading(true);
        const response = await getDeployHistoryById(Number(id));
        if (response.code === 0 && response.data) {
          setHistoryDetail(response.data);
          
          // 如果是多分组部署，获取子任务
          if (response.data.is_parent) {
            const subTasksResponse = await getDeploySubTasks(Number(id));
            if (subTasksResponse.code === 0) {
              setSubTasks(subTasksResponse.data || []);
            }
          }
        }
      } catch (error) {
        console.error('获取部署历史详情失败:', error);
      } finally {
        setLoading(false);
      }
    };

    if (id) {
      fetchDeployHistory();
    }

    // 定时刷新数据 - 每5秒刷新一次
    const intervalId = setInterval(() => {
      if (historyDetail && !['已完成', '已确认', '已取消', '失败'].includes(historyDetail.status)) {
        setRefreshKey(prevKey => prevKey + 1);
      }
    }, 5000);

    return () => clearInterval(intervalId);
  }, [id, refreshKey]);

  // 确认部署
  const handleConfirmDeploy = async () => {
    if (!historyDetail?.id) return;
    
    try {
      const response = await confirmDeploy(historyDetail.id);
      if (response.code === 0) {
        // 刷新数据
        setRefreshKey(prevKey => prevKey + 1);
      }
    } catch (error) {
      console.error('确认部署失败:', error);
    }
  };

  // 取消部署
  const handleCancelDeploy = async () => {
    if (!historyDetail?.id) return;
    
    try {
      const response = await cancelDeploy(historyDetail.id);
      if (response.code === 0) {
        // 刷新数据
        setRefreshKey(prevKey => prevKey + 1);
      }
    } catch (error) {
      console.error('取消部署失败:', error);
    }
  };

  // 渲染部署状态标签
  const renderStatusTag = (status: string) => {
    const config = STATUS_CONFIG[status] || { color: 'default', icon: <InfoCircleOutlined /> };
    return (
      <Tag color={config.color} icon={config.icon}>
        {status}
      </Tag>
    );
  };

  // 渲染部署步骤
  const renderDeploySteps = () => {
    if (!historyDetail) return null;
    
    const steps = [
      { title: '初始化', description: '准备部署环境' },
      { title: '拉取镜像', description: '从仓库拉取部署镜像' },
      { title: '停止旧服务', description: '停止正在运行的服务' },
      { title: '启动新服务', description: '启动新版本服务' },
      { title: '健康检查', description: '验证服务是否正常' },
    ];
    
    const currentStep = historyDetail.current_step_index || 0;
    
    return (
      <Steps 
        current={currentStep} 
        status={
          historyDetail.status === '失败' ? 'error' : 
          historyDetail.status === '已完成' || historyDetail.status === '已确认' ? 'finish' :
          'process'
        }
        progressDot
        direction="horizontal"
        className={styles.deploySteps}
      >
        {steps.map((step, index) => (
          <Steps.Step 
            key={index} 
            title={step.title} 
            description={step.description}
            icon={currentStep === index && historyDetail.status === '进行中' ? <LoadingOutlined /> : null} 
          />
        ))}
      </Steps>
    );
  };

  // 渲染子任务表格
  const renderSubTasksTable = () => {
    if (!historyDetail?.is_parent || !subTasks.length) return null;
    
    const columns = [
      {
        title: '分组名称',
        dataIndex: 'group_name',
        key: 'group_name',
      },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        render: (status: string) => renderStatusTag(status),
      },
      {
        title: '进度',
        dataIndex: 'progress',
        key: 'progress',
        render: (progress: number) => `${progress}%`,
      },
      {
        title: '开始时间',
        dataIndex: 'start_time',
        key: 'start_time',
        render: (startTime: number) => new Date(startTime * 1000).toLocaleString(),
      },
      {
        title: '结束时间',
        dataIndex: 'end_time',
        key: 'end_time',
        render: (endTime: number) => endTime ? new Date(endTime * 1000).toLocaleString() : '-',
      },
      {
        title: '操作',
        key: 'action',
        render: (_, record) => (
          <Button type="link" onClick={() => window.open(`/deploy/history/${record.id}`)}>
            详情
          </Button>
        ),
      },
    ];
    
    return (
      <div className={styles.subTasksWrapper}>
        <Title level={5}>
          <TeamOutlined /> 分组部署进度
        </Title>
        <Table 
          columns={columns} 
          dataSource={subTasks} 
          rowKey="id" 
          pagination={false}
          size="small"
        />
      </div>
    );
  };
  
  if (loading) {
    return <Card loading />;
  }
  
  if (!historyDetail) {
    return <Card>未找到部署历史记录</Card>;
  }

  return (
    <Card className={styles.deployHistoryDetailCard}>
      <div className={styles.header}>
        <div className={styles.titleArea}>
          <Title level={4}>
            {historyDetail.version} 
            {historyDetail.is_multi_group && (
              <Badge count="多分组部署" style={{ backgroundColor: '#1890ff', marginLeft: 8 }} />
            )}
          </Title>
          <div className={styles.statusArea}>
            {renderStatusTag(historyDetail.status)}
            
            {/* 操作按钮区域 */}
            {historyDetail.status === '等待确认' && (
              <div className={styles.actionButtons}>
                <Button type="primary" onClick={handleConfirmDeploy}>
                  确认部署
                </Button>
                <Button danger onClick={handleCancelDeploy} style={{ marginLeft: 8 }}>
                  取消部署
                </Button>
              </div>
            )}
          </div>
        </div>
        
        <div className={styles.description}>
          {historyDetail.description}
        </div>
      </div>
      
      <Descriptions bordered column={2} size="small" className={styles.infoDescriptions}>
        <Descriptions.Item label="部署类型">{historyDetail.deploy_type}</Descriptions.Item>
        <Descriptions.Item label="部署策略">{historyDetail.deploy_strategy}</Descriptions.Item>
        <Descriptions.Item label="Commit ID">{historyDetail.commit_id}</Descriptions.Item>
        <Descriptions.Item label="操作人">{historyDetail.operator}</Descriptions.Item>
        <Descriptions.Item label="开始时间">
          {historyDetail.start_time ? new Date(historyDetail.start_time * 1000).toLocaleString() : '-'}
        </Descriptions.Item>
        <Descriptions.Item label="结束时间">
          {historyDetail.end_time ? new Date(historyDetail.end_time * 1000).toLocaleString() : '-'}
        </Descriptions.Item>
        <Descriptions.Item label="持续时间">
          {historyDetail.duration ? `${historyDetail.duration}秒` : '-'}
        </Descriptions.Item>
        <Descriptions.Item label="当前进度">
          {`${historyDetail.progress || 0}%`}
        </Descriptions.Item>
      </Descriptions>
      
      {renderDeploySteps()}
      
      {renderSubTasksTable()}
    </Card>
  );
};

export default DeployHistoryDetail; 