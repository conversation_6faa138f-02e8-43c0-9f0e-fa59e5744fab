import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Tag,
  Button,
  Space,
  Tooltip,
  DatePicker,
  Select,
  Form,
  Input,
  Row,
  Col,
  message,
  Typography,
} from 'antd';
import {
  SearchOutlined,
  ReloadOutlined,
  EyeOutlined,
  DeleteOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  SyncOutlined,
  ClockCircleOutlined,
} from '@ant-design/icons';
import moment from 'moment';
import { getDeployHistoryList } from '@/services/api/DeployTaskController';
import useDeploy from '@/models/deploy';

import styles from './style.less';

const { RangePicker } = DatePicker;
const { Option } = Select;
const { Text } = Typography;

export const DeployHistory: React.FC = () => {
  const { deploy } = useDeploy();
  const [form] = Form.useForm();

  const [loading, setLoading] = useState(false);
  const [deployHistoryList, setDeployHistoryList] = useState<API.DeployHistory[]>([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  // 获取状态对应的颜色和图标
  const getStatusConfig = (status: string) => {
    switch (status) {
      case '排队中':
        return { color: 'default', icon: <ClockCircleOutlined /> };
      case '进行中':
        return { color: 'processing', icon: <SyncOutlined spin /> };
      case '等待确认':
        return { color: 'warning', icon: <ClockCircleOutlined /> };
      case '已完成':
        return { color: 'success', icon: <CheckCircleOutlined /> };
      case '已确认':
        return { color: 'success', icon: <CheckCircleOutlined /> };
      case '已取消':
        return { color: 'default', icon: <CloseCircleOutlined /> };
      case '失败':
        return { color: 'error', icon: <CloseCircleOutlined /> };
      default:
        return { color: 'default', icon: null };
    }
  };

  // 格式化持续时间
  const formatDuration = (seconds: number | undefined) => {
    if (!seconds) return '-';
    
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;
    
    if (hours > 0) {
      return `${hours}小时${minutes}分钟${remainingSeconds}秒`;
    }
    if (minutes > 0) {
      return `${minutes}分钟${remainingSeconds}秒`;
    }
    return `${remainingSeconds}秒`;
  };

  // 加载部署历史数据
  const loadDeployHistory = async (params: any = {}) => {
    try {
      setLoading(true);
      
      const queryParams = {
        app_id: deploy.appData.application.id,
        env_id: deploy.envId,
        page: pagination.current,
        page_size: pagination.pageSize,
        ...params,
      };
      
      const res = await getDeployHistoryList(queryParams);
      
      if (res.code === 0 && res.data) {
        setDeployHistoryList(res.data.list || []);
        setPagination({
          ...pagination,
          total: res.data.total || 0,
        });
      } else {
        message.error('获取部署历史列表失败');
      }
    } catch (error) {
      console.error('加载部署历史失败:', error);
      message.error('加载部署历史失败');
    } finally {
      setLoading(false);
    }
  };

  // 处理表格分页、排序、筛选变化
  const handleTableChange = (pagination: any, filters: any, sorter: any) => {
    setPagination(pagination);
    loadDeployHistory({
      page: pagination.current,
      page_size: pagination.pageSize,
      ...form.getFieldsValue(),
    });
  };

  // 处理搜索表单提交
  const handleSearch = (values: any) => {
    // 处理日期范围
    let params: any = { ...values };
    if (values.dateRange) {
      params.start_time_from = values.dateRange[0].unix();
      params.start_time_to = values.dateRange[1].unix();
      delete params.dateRange;
    }
    
    setPagination({ ...pagination, current: 1 }); // 重置为第一页
    loadDeployHistory({ ...params, page: 1 });
  };

  // 重置搜索条件
  const handleReset = () => {
    form.resetFields();
    setPagination({ ...pagination, current: 1 });
    loadDeployHistory({ page: 1 });
  };

  // 初始化加载数据
  useEffect(() => {
    if (deploy.appData.application.id) {
      loadDeployHistory();
    }
  }, [deploy.appData.application.id, deploy.envId]);

  // 表格列定义
  const columns = [
    {
      title: '版本',
      dataIndex: 'version',
      key: 'version',
      width: 120,
      render: (text: string, record: API.DeployHistory) => (
        <div>
          <Tag color="blue">{text}</Tag>
          {record.is_multi_group && (
            <Tag color="green" style={{ marginLeft: '4px' }}>多分组</Tag>
          )}
          {record.is_child && (
            <Tag color="orange" style={{ marginLeft: '4px' }}>子任务</Tag>
          )}
        </div>
      ),
    },
    {
      title: '部署说明',
      dataIndex: 'description',
      key: 'description',
      ellipsis: {
        showTitle: false,
      },
      render: (description: string) => (
        <Tooltip placement="topLeft" title={description}>
          <span>{description || '-'}</span>
        </Tooltip>
      ),
    },
    {
      title: '部署类型',
      dataIndex: 'deploy_type',
      key: 'deploy_type',
      width: 100,
    },
    {
      title: '部署策略',
      dataIndex: 'deploy_strategy',
      key: 'deploy_strategy',
      width: 100,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 110,
      render: (status: string) => {
        const { color, icon } = getStatusConfig(status);
        return (
          <Tag color={color as any} icon={icon}>
            {status}
          </Tag>
        );
      },
    },
    {
      title: '操作人',
      dataIndex: 'operator',
      key: 'operator',
      width: 100,
    },
    {
      title: '开始时间',
      dataIndex: 'start_time',
      key: 'start_time',
      width: 150,
      render: (startTime: number) => (
        startTime ? moment(startTime * 1000).format('YYYY-MM-DD HH:mm:ss') : '-'
      ),
    },
    {
      title: '持续时间',
      dataIndex: 'duration',
      key: 'duration',
      width: 150,
      render: (duration: number) => formatDuration(duration),
    },
    {
      title: '目标分组',
      dataIndex: 'target_groups',
      key: 'target_groups',
      width: 180,
      render: (targetGroups: string[], record: API.DeployHistory) => {
        if (!targetGroups || targetGroups.length === 0) return '-';
        
        if (record.is_multi_group) {
          return (
            <Tooltip title={targetGroups.join(', ')}>
              <div>
                {targetGroups.slice(0, 2).map((group) => (
                  <Tag key={group}>{group}</Tag>
                ))}
                {targetGroups.length > 2 && <Tag>+{targetGroups.length - 2}</Tag>}
              </div>
            </Tooltip>
          );
        }
        
        return targetGroups[0];
      },
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_, record: API.DeployHistory) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => {
              // 查看部署历史详情
              window.open(`/deploy/history/${record.id}`, '_blank');
            }}
          >
            详情
          </Button>
          <Button
            type="link"
            size="small"
            danger
            icon={<DeleteOutlined />}
            onClick={() => {
              // 删除部署历史
              message.info('删除功能暂未实现');
            }}
          >
            删除
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <Card className={styles.historyCard} title="部署历史" extra={
      <Button 
        icon={<ReloadOutlined />} 
        onClick={() => loadDeployHistory()}
        loading={loading}
      >
        刷新
      </Button>
    }>
      <div className={styles.tableListForm}>
        <Form
          form={form}
          layout="horizontal"
          onFinish={handleSearch}
          initialValues={{
            status: undefined,
            deploy_type: undefined,
            deploy_strategy: undefined,
          }}
        >
          <Row gutter={16}>
            <Col xs={24} sm={12} md={8} lg={6} xl={6}>
              <Form.Item name="version" label="版本">
                <Input placeholder="请输入版本号" allowClear />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={8} lg={6} xl={6}>
              <Form.Item name="operator" label="操作人">
                <Input placeholder="请输入操作人" allowClear />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={8} lg={6} xl={6}>
              <Form.Item name="status" label="状态">
                <Select placeholder="请选择状态" allowClear>
                  <Option value="排队中">排队中</Option>
                  <Option value="进行中">进行中</Option>
                  <Option value="等待确认">等待确认</Option>
                  <Option value="已完成">已完成</Option>
                  <Option value="已确认">已确认</Option>
                  <Option value="已取消">已取消</Option>
                  <Option value="失败">失败</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={24} md={16} lg={12} xl={6}>
              <Form.Item name="dateRange" label="时间范围">
                <RangePicker
                  style={{ width: '100%' }}
                  showTime
                  format="YYYY-MM-DD HH:mm:ss"
                />
              </Form.Item>
            </Col>
          </Row>
          <Row>
            <Col span={24} style={{ textAlign: 'right' }}>
              <Space>
                <Button onClick={handleReset}>重置</Button>
                <Button type="primary" htmlType="submit" icon={<SearchOutlined />}>
                  查询
                </Button>
              </Space>
            </Col>
          </Row>
        </Form>
      </div>

      <Table
        rowKey="id"
        columns={columns}
        dataSource={deployHistoryList}
        pagination={pagination}
        onChange={handleTableChange}
        loading={loading}
        scroll={{ x: 'max-content' }}
      />
    </Card>
  );
};

export default DeployHistory; 