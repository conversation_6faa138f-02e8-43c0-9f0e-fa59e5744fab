import React, { useState } from 'react';
import {
  Al<PERSON>,
  But<PERSON>,
  Card,
  Checkbox,
  Dropdown,
  Form,
  Input,
  Modal,
  Radio,
  Select,
  Space,
  Table,
  Tag,
  Tooltip,
  Typography,
  message
} from 'antd';
import {
  BranchesOutlined,
  CheckCircleOutlined,
  CheckOutlined,
  ClockCircleOutlined,
  CloseCircleOutlined,
  DiffOutlined,
  DownOutlined,
  EllipsisOutlined,
  EyeOutlined,
  HistoryOutlined,
  PauseCircleOutlined,
  ReloadOutlined,
  RetweetOutlined,
  SearchOutlined,
  SwapRightOutlined,
  UpOutlined,
  UserOutlined,
  WarningOutlined,
} from '@ant-design/icons';
import { DeployStatus } from '@/types/deploytask';
import type { DeployHistoryView } from '@/types/deploytask';
import { DEPLOY_STATUS_CONFIG, DeployStatusString, mapStatusNumberToString } from '../../types/common';
import styles from './style.less';
import useDeploy from '@/models/deploy';
import useDeployHistory from '@/hooks/useDeployHistory';
import dayjs from 'dayjs';

const { Option } = Select;
const { Text } = Typography;


export const DeployHistory: React.FC = () => {
  const { deploy } = useDeploy();
  
  // 使用自定义Hook替代直接在组件中管理状态和调用API
  const {
    paginatedHistoryList,
    currentHistory,
    historyGroups,
    loading,
    detailLoading,
    pagination,
    handlePaginationChange,
    searchFilter,
    setSearchFilter,
    statusFilter,
    setStatusFilter,
    typeFilter,
    setTypeFilter,
    strategyFilter,
    setStrategyFilter,
    groupFilter,
    setGroupFilter,
    fetchDeployHistory,
    fetchDeployHistoryDetail,
    setCurrentHistory,
    filteredHistoryList
  } = useDeployHistory();
  
  // 内部状态
  const [deployHistoryVisible, setDeployHistoryVisible] = useState<boolean>(true);
  const [deployHistoryDetailVisible, setDeployHistoryDetailVisible] = useState<boolean>(false);
  
  // 回滚相关状态
  const [rollbackModalVisible, setRollbackModalVisible] = useState<boolean>(false);
  const [rollbackForm] = Form.useForm();
  const [selectedRollbackVersion, setSelectedRollbackVersion] = useState<DeployHistoryView | null>(null);
  
  // 重发部署相关状态
  const [redeployModalVisible, setRedeployModalVisible] = useState<boolean>(false);
  const [redeployForm] = Form.useForm();

  // 查看部署历史详情
  const viewDeployHistoryDetail = async (history: DeployHistoryView) => {
    const detailHistory = await fetchDeployHistoryDetail(history.id);
    if (detailHistory) {
      setDeployHistoryDetailVisible(true);
    }
  };

  // 处理重发部署
  const handleRedeploy = (history: DeployHistoryView) => {
    // 设置重发表单的初始值
    redeployForm.setFieldsValue({
      description: `重发部署: ${history.description}`,
      targetGroups: history.targetGroups,
      deployStrategy:
        history.deployStrategy === '蓝绿'
          ? 'blue-green'
          : 'canary',
    });

    setCurrentHistory(history);
    setRedeployModalVisible(true);
  };

  // 处理回滚操作
  const handleRollback = (history: DeployHistoryView) => {
    setSelectedRollbackVersion(history);
    rollbackForm.setFieldsValue({
      semver: history.semver,
      targetGroups: [deploy.groupId.toString()], // 默认选择当前分组
      rollbackStrategy: 'normal',
      description: `回滚到版本 ${history.semver}: ${history.description}`,
      enableBackup: true,
    });
    setRollbackModalVisible(true);
  };

  // 提交重发部署表单
  const submitRedeployForm = () => {
    redeployForm
      .validateFields()
      .then((values) => {
        console.log('重发部署信息:', values);
        message.success('重发部署请求已提交');
        setRedeployModalVisible(false);

        // 在实际应用中，这里会调用API发起部署
        // 并可能添加一条新的部署历史记录
      })
      .catch((errorInfo) => {
        console.log('表单验证失败:', errorInfo);
      });
  };

  // 提交回滚表单
  const submitRollbackForm = () => {
    rollbackForm
      .validateFields()
      .then((values) => {
        console.log('回滚信息:', values);
        message.success('回滚请求已提交');
        setRollbackModalVisible(false);

        // 在实际应用中，这里会调用API执行回滚操作
        // 并添加一条回滚部署的历史记录
      })
      .catch((errorInfo) => {
        console.log('表单验证失败:', errorInfo);
      });
  };

  // 处理可见性切换
  const handleToggleVisible = () => {
    const newVisible = !deployHistoryVisible;
    setDeployHistoryVisible(newVisible);
  };

  // 过滤部署历史
  const filteredDeployHistory = filteredHistoryList;

  // 获取分组列表
  const deployHistoryGroups = historyGroups;

  const deployHistoryColumns = [
    {
      title: '版本信息',
      key: 'semver',
      width: 140,
      render: (_: any, record: DeployHistoryView) => (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <div style={{ fontWeight: 500 }}>
            {record.semver}
            <span style={{ fontSize: 11, color: '#8c8c8c', marginLeft: 5 }}>
              ({record.commitId.slice(0, 7)})
            </span>
          </div>
        </div>
      ),
    },
    {
      title: '部署信息',
      key: 'deployInfo',
      width: 140,
      render: (_: any, record: DeployHistoryView) => (
        <div style={{ display: 'flex', alignItems: 'center' }}>
            <Tag
              color={
                record.deployType === '部署'
                  ? 'blue'
                  : record.deployType === '编译部署'
                  ? 'green'
                  : 'orange'
              }
              style={{ marginRight: 4, fontSize: 11 }}
            >
              {record.deployType}
            </Tag>
            <Tag
              color={
                record.deployStrategy === '普通'
                  ? 'default'
                  : record.deployStrategy === '蓝绿'
                  ? 'cyan'
                  : record.deployStrategy === '金丝雀'
                  ? 'gold'
                  : 'purple'
              }
            style={{ fontSize: 11, marginRight: 4 }}
            >
              {record.deployStrategy}
            </Tag>
          </div>
      ),
    },
    {
      title: '分组',
      dataIndex: 'groupName',
      key: 'groupName',
      width: 80,
      render: (groupName: string) => (
        <div style={{ fontSize: 11, color: '#333' }}>
          {groupName}
        </div>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 90,
      render: (status: DeployStatus) => {
        const statusString = mapStatusNumberToString(status);
        const config = DEPLOY_STATUS_CONFIG[statusString];
        return (
          <Tag
            color={config.color}
            style={{
              backgroundColor: config.bgColor,
              border: `1px solid ${config.color}`,
              fontSize: 11,
              color: config.color,
              fontWeight: 500
            }}
          >
            {statusString}
          </Tag>
        );
      },
    },
    {
      title: '操作人',
      dataIndex: 'operator',
      key: 'operator',
      width: 80,
      render: (operator: string) => (
        <div style={{ fontSize: 11 }}>
          <UserOutlined style={{ marginRight: 4, color: '#8c8c8c' }} />
          {operator}
        </div>
      ),
    },
    {
      title: '时间/耗时',
      key: 'timeInfo',
      width: 160,
      render: (_: any, record: DeployHistoryView) => (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <span style={{ fontSize: 11 }}>{record.deployTime}</span>
          <span style={{ fontSize: 11, color: '#8c8c8c', marginLeft: 5 }}>
            ({record.duration})
          </span>
        </div>
      ),
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: { showTitle: false },
      render: (description: string) => (
        <Tooltip title={description}>
          <span style={{ fontSize: 11 }}>{description}</span>
        </Tooltip>
      ),
    },
    {
      title: '操作',
      key: 'actions',
      width: 160,
      render: (_: any, record: DeployHistoryView) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button
              type="text"
              size="small"
              icon={<EyeOutlined style={{ fontSize: 16, color: '#1890ff' }} />}
              onClick={() => viewDeployHistoryDetail(record)}
            />
          </Tooltip>
          <Tooltip title="重新部署">
            <Button
              type="text"
              size="small"
              icon={<ReloadOutlined style={{ fontSize: 16, color: '#52c41a' }} />}
              onClick={() => handleRedeploy(record)}
              disabled={mapStatusNumberToString(record.status) === '进行中' || mapStatusNumberToString(record.status) === '排队中'}
            />
          </Tooltip>
          <Tooltip title="回滚到此版本">
          <Button
            type="text"
            size="small"
              icon={<HistoryOutlined style={{ fontSize: 16, color: '#722ed1' }} />}
              onClick={() => handleRollback(record)}
              disabled={
                mapStatusNumberToString(record.status) === '进行中' || 
                mapStatusNumberToString(record.status) === '排队中' || 
                mapStatusNumberToString(record.status) === '失败'
              }
          />
          </Tooltip>
        </Space>
      ),
    },
  ];

  // 重发部署模态框
  const renderRedeployModal = () => {
    if (!currentHistory) return null;

    return (
      <Modal
        title={`重发部署 - ${currentHistory.semver}`}
        open={redeployModalVisible}
        onCancel={() => setRedeployModalVisible(false)}
        onOk={submitRedeployForm}
        width={600}
        okText="确认重发"
        cancelText="取消"
      >
        <div>
          <Alert
            message="重发部署将基于历史记录创建新的部署任务"
            description="您可以修改以下部署参数，或保持原有配置不变。"
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />

          <Form
            form={redeployForm}
            layout="vertical"
            initialValues={{
              deployStrategy: 'blue-green'
            }}
          >
            <Form.Item name="targetGroups" label="目标分组">
              <Checkbox.Group>
                {deploy.appData.groups.map((group) => (
                  <Checkbox key={group.id} value={group.id.toString()}>
                    {group.name}
                  </Checkbox>
                ))}
              </Checkbox.Group>
            </Form.Item>

            <Form.Item
              name="deployStrategy"
              label="部署策略"
              rules={[{ required: true, message: '请选择部署策略' }]}
            >
              <Radio.Group>
                <Radio value="blue-green">蓝绿部署</Radio>
                <Radio value="canary">金丝雀部署</Radio>
              </Radio.Group>
            </Form.Item>

            <Form.Item
              name="description"
              label="部署说明"
              rules={[{ required: true, message: '请输入部署说明' }]}
            >
              <Input.TextArea
                placeholder="请输入部署说明，描述此次部署的主要内容和目的"
                rows={3}
              />
            </Form.Item>
          </Form>
        </div>
      </Modal>
    );
  };

  // 回滚模态框
  const renderRollbackModal = () => {
    if (!selectedRollbackVersion) return null;

    return (
      <Modal
        title={`回滚到版本 - ${selectedRollbackVersion.semver}`}
        open={rollbackModalVisible}
        onCancel={() => setRollbackModalVisible(false)}
        onOk={submitRollbackForm}
        width={600}
        okText="确认回滚"
        cancelText="取消"
      >
        <div className={styles.modalContent}>
          <Alert
            message="回滚操作将会将当前服务回滚到指定的历史版本"
            description="请确认回滚的目标版本和配置信息，回滚操作不可逆。"
            type="warning"
            showIcon
            style={{ marginBottom: 16 }}
          />

          <div
            style={{
              marginBottom: 16,
              padding: 12,
              backgroundColor: '#f5f5f5',
              borderRadius: 6,
            }}
          >
            <Text strong>回滚版本信息：</Text>
            <div style={{ marginTop: 8 }}>
              <Space direction="vertical" size="small">
                <div>
                  <Text type="secondary">版本：</Text>
                  <Text code>{selectedRollbackVersion.version}</Text>
                </div>
                <div>
                  <Text type="secondary">部署时间：</Text>
                  <Text>{selectedRollbackVersion.deployTime}</Text>
                </div>
                <div>
                  <Text type="secondary">操作人：</Text>
                  <Text>{selectedRollbackVersion.operator}</Text>
                </div>
                <div>
                  <Text type="secondary">描述：</Text>
                  <Text>{selectedRollbackVersion.description}</Text>
                </div>
              </Space>
            </div>
          </div>

          <Form
            form={rollbackForm}
            layout="vertical"
          >
            <Form.Item name="version" label="回滚版本">
              <Input disabled />
            </Form.Item>

            <Form.Item name="targetGroups" label="目标分组">
              <Checkbox.Group>
                {deploy.appData.groups.map((group) => (
                  <Checkbox key={group.id} value={group.id.toString()}>
                    {group.name}
                  </Checkbox>
                ))}
              </Checkbox.Group>
            </Form.Item>

            <Form.Item
              name="description"
              label="回滚说明"
              rules={[{ required: true, message: '请输入回滚说明' }]}
            >
              <Input.TextArea placeholder="请输入回滚原因和说明" rows={3} />
            </Form.Item>
          </Form>
        </div>
      </Modal>
    );
  };

  // 部署历史详情模态框
  const renderDeployHistoryDetailModal = () => {
    if (!currentHistory) return null;

    return (
      <Modal
        title={`部署详情 - ${currentHistory.semver}`}
        open={deployHistoryDetailVisible}
        onCancel={() => setDeployHistoryDetailVisible(false)}
        footer={[
          <Button key="close" onClick={() => setDeployHistoryDetailVisible(false)}>
            关闭
          </Button>,
          <Button
            key="redeploy"
            type="primary"
            onClick={() => {
              setDeployHistoryDetailVisible(false);
              handleRedeploy(currentHistory);
            }}
            disabled={mapStatusNumberToString(currentHistory.status) === '进行中' || mapStatusNumberToString(currentHistory.status) === '排队中'}
          >
            重新部署
          </Button>,
        ]}
        width={800}
      >
        <div>
          <div style={{ marginBottom: 20 }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 16 }}>
              <div>
                <Text strong style={{ fontSize: 16 }}>
                  {currentHistory.semver}
                </Text>
                <Tag
                  color={
                    mapStatusNumberToString(currentHistory.status) === '已完成'
                      ? 'success'
                      : mapStatusNumberToString(currentHistory.status) === '进行中'
                      ? 'processing'
                      : mapStatusNumberToString(currentHistory.status) === '失败'
                      ? 'error'
                      : 'default'
                  }
                  style={{ marginLeft: 8 }}
                >
                  {mapStatusNumberToString(currentHistory.status)}
                </Tag>
              </div>
              <div>
                <Text type="secondary">
                  部署时间: {currentHistory.deployTime}
                </Text>
              </div>
            </div>

            <div style={{ marginBottom: 16 }}>
              <Text type="secondary">提交ID: </Text>
              <Text code>{currentHistory.commitId}</Text>
            </div>

            <div style={{ marginBottom: 16 }}>
              <Text type="secondary">部署类型: </Text>
              <Tag color="blue">{currentHistory.deployType}</Tag>
              <Text type="secondary" style={{ marginLeft: 16 }}>
                部署策略:
              </Text>
              <Tag color="cyan">{currentHistory.deployStrategy}</Tag>
            </div>

            <div style={{ marginBottom: 16 }}>
              <Text type="secondary">操作人: </Text>
              <Text>{currentHistory.operator}</Text>
              <Text type="secondary" style={{ marginLeft: 16 }}>
                耗时:
              </Text>
              <Text>{currentHistory.duration}</Text>
            </div>

            <div style={{ marginBottom: 16 }}>
              <Text type="secondary">目标分组: </Text>
              {currentHistory.targetGroups.map((groupId) => {
                const group = deploy.appData.groups.find(g => g.id.toString() === groupId);
                return group ? (
                  <Tag key={groupId}>{group.name}</Tag>
                ) : null;
              })}
            </div>

            <div style={{ marginBottom: 16 }}>
              <Text type="secondary">部署说明: </Text>
              <Text>{currentHistory.description}</Text>
            </div>
          </div>

          <div>
            <Text strong>部署日志:</Text>
            <div
              style={{
                maxHeight: 300,
                overflow: 'auto',
                border: '1px solid #d9d9d9',
                borderRadius: 2,
                padding: 8,
                marginTop: 8,
                backgroundColor: '#f5f5f5',
              }}
            >
              {currentHistory.logs.map((log, index) => (
                <div
                  key={index}
                  style={{
                    marginBottom: 4,
                    color:
                      log.type === 'error'
                        ? '#ff4d4f'
                        : log.type === 'warning'
                        ? '#faad14'
                        : log.type === 'success'
                        ? '#52c41a'
                        : 'inherit',
                  }}
                >
                  <Text type="secondary" style={{ marginRight: 8 }}>
                    [{log.timestamp}]
                  </Text>
                  {log.content}
                </div>
              ))}
            </div>
          </div>
        </div>
      </Modal>
    );
  };

  // 渲染部署历史表格
  const renderDeployHistoryTable = () => {
    return (
      <Table
        dataSource={paginatedHistoryList}
        columns={deployHistoryColumns}
        rowKey="id"
        loading={loading}
        size="small"
        pagination={{
          ...pagination,
          onChange: handlePaginationChange,
          showTotal: (total) => `共 ${total} 条`,
        }}
        expandable={{
          expandedRowRender: (record) => (
            <div style={{ padding: '8px 16px' }}>
              <div style={{ marginBottom: 8 }}>
                <Text type="secondary">详细描述: </Text>
                <Text>{record.description}</Text>
              </div>
              <div>
                <Text type="secondary">提交ID: </Text>
                <Text code>{record.commitId}</Text>
              </div>
            </div>
          ),
        }}
      />
    );
  };

  return (
    <>
      <Card
        className={styles.historyCard}
        title="部署历史"
        extra={
          <Space size={[8, 0]}>
            <Input
              placeholder="搜索版本/描述"
              value={searchFilter}
              onChange={(e) => setSearchFilter(e.target.value)}
              prefix={<SearchOutlined />}
              style={{ width: 150 }}
              size="small"
              allowClear
            />
            <Select
              value={statusFilter}
              onChange={(value) => setStatusFilter(value)}
              size="small"
              style={{ width: 90 }}
              dropdownMatchSelectWidth={false}
            >
              <Option value="all">全部状态</Option>
              <Option value="排队中">排队中</Option>
              <Option value="进行中">进行中</Option>
              <Option value="已完成">已完成</Option>
              <Option value="已确认">已确认</Option>
              <Option value="失败">失败</Option>
            </Select>
            <Select
              value={typeFilter}
              onChange={(value) => setTypeFilter(value)}
              size="small"
              style={{ width: 100 }}
              dropdownMatchSelectWidth={false}
            >
              <Option value="all">全部类型</Option>
              <Option value="部署">部署</Option>
              <Option value="编译部署">编译部署</Option>
              <Option value="回滚部署">回滚部署</Option>
            </Select>
            <Select
              value={strategyFilter}
              onChange={(value) => setStrategyFilter(value)}
              size="small"
              style={{ width: 100 }}
              dropdownMatchSelectWidth={false}
            >
              <Option value="all">全部策略</Option>
              <Option value="普通">普通</Option>
              <Option value="蓝绿">蓝绿</Option>
              <Option value="金丝雀">金丝雀</Option>
            </Select>
            <Select
              value={groupFilter}
              onChange={(value) => setGroupFilter(value)}
              size="small"
              style={{ width: 100 }}
              dropdownMatchSelectWidth={false}
            >
              <Option value="all">全部分组</Option>
              {deployHistoryGroups.map((group) => (
                <Option key={group} value={group}>
                  {group}
                </Option>
              ))}
            </Select>
            <Button
              type="text"
              size="small"
              icon={<ReloadOutlined />}
              onClick={fetchDeployHistory}
              loading={loading}
            />
            <Button
              type="text"
              size="small"
              icon={deployHistoryVisible ? <UpOutlined /> : <DownOutlined />}
              onClick={handleToggleVisible}
            />
          </Space>
        }
        style={{ marginBottom: 16 }}
      >
        {deployHistoryVisible && (
          <div>
            {renderDeployHistoryTable()}
          </div>
        )}
      </Card>

      {/* 模态框 */}
      {renderRedeployModal()}
      {renderRollbackModal()}
      {renderDeployHistoryDetailModal()}
    </>
  );
};