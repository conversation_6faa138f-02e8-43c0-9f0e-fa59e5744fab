import React, { useState, useEffect } from 'react';
import { 
  Form, 
  Input, 
  InputNumber, 
  Button, 
  Card, 
  Row, 
  Col, 
  Space, 
  Tooltip, 
  Modal,
  Typography,
  Divider,
  message,
  Select,
  Drawer,
  List,
  Collapse,
  Descriptions,
  Dropdown,
  Alert,
  Switch
} from 'antd';
import { 
  CheckCircleOutlined, 
  ThunderboltOutlined, 
  ClockCircleOutlined, 
  QuestionCircleOutlined,
  InfoCircleOutlined,
  CopyOutlined,
  AppstoreOutlined,
  ArrowLeftOutlined,
  CheckOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import { useModel } from '@umijs/max';
import useProbe from '@/hooks/useProbe';

import { probeTemplates, getProbeTemplateByKey, copyProbeConfig, ProbeTemplate } from './ProbeTemplates';

const { Text, Title } = Typography;
const { Option } = Select;

// 导入之前定义的探针配置接口
import type { ProbeConfig } from './ProbeTooltip';

// 修复表单验证规则的类型
import { Rule } from 'antd/es/form';

export interface ProbeEditorProps {
  visible: boolean;
  probeConfig: ProbeConfig;
  onSave: (config: ProbeConfig) => void;
  onCancel: () => void;
  saveProbes?: (config: ProbeConfig) => Promise<boolean>;
}

// 定义探针类型
const probeTypes = [
  { key: 'startup', name: '启动探针', icon: <ThunderboltOutlined />, color: '#fa8c16', bgColor: '#fff7e6' },
  { key: 'liveness', name: '存活探针', icon: <ClockCircleOutlined />, color: '#1890ff', bgColor: '#e6f7ff' },
  { key: 'readiness', name: '就绪探针', icon: <CheckCircleOutlined />, color: '#52c41a', bgColor: '#f6ffed' },
];

// 表单校验规则
const validationRules = {
  port: [
    { required: true, message: '请输入端口' } as Rule,
    { type: 'number', min: 1, max: 65535, message: '端口范围为1-65535' } as Rule
  ],
  delay: [
    { required: true, message: '请输入延迟' } as Rule,
    { type: 'number', min: 0, message: '延迟不能为负数' } as Rule
  ],
  timeout: [
    { required: true, message: '请输入超时' } as Rule,
    { type: 'number', min: 1, message: '超时必须大于0' } as Rule
  ],
  period: [
    { required: true, message: '请输入周期' } as Rule,
    { type: 'number', min: 1, message: '周期必须大于0' } as Rule
  ],
  threshold: [
    { required: true, message: '请输入阈值' } as Rule,
    { type: 'number', min: 1, message: '阈值必须大于0' } as Rule
  ]
};

const ProbeEditor: React.FC<ProbeEditorProps> = ({ 
  visible, 
  probeConfig, 
  onSave, 
  onCancel,
  saveProbes
}) => {
  const [form] = Form.useForm();
  const [saving, setSaving] = useState(false);
  const [formErrors, setFormErrors] = useState<string[]>([]);
  const { deploy } = useModel('deploy');
  const [templateDrawerVisible, setTemplateDrawerVisible] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<ProbeTemplate | null>(null);

  // 当modal显示状态改变时，初始化表单
  useEffect(() => {
    if (visible) {
      form.setFieldsValue({
        // 启动探针
        startupProbePath: probeConfig.startupProbePath,
        startupProbePort: probeConfig.startupProbePort,
        startupInitialDelaySeconds: probeConfig.startupInitialDelaySeconds,
        startupTimeoutSeconds: probeConfig.startupTimeoutSeconds,
        startupPeriodSeconds: probeConfig.startupPeriodSeconds,
        startupFailureThreshold: probeConfig.startupFailureThreshold,
        startupSuccessThreshold: probeConfig.startupSuccessThreshold,
        startupProbeEnabled: probeConfig.startupProbeEnabled,
        
        // 存活探针
        livenessProbePath: probeConfig.livenessProbePath,
        livenessProbePort: probeConfig.livenessProbePort,
        livenessInitialDelaySeconds: probeConfig.livenessInitialDelaySeconds,
        livenessTimeoutSeconds: probeConfig.livenessTimeoutSeconds,
        livenessPeriodSeconds: probeConfig.livenessPeriodSeconds,
        livenessFailureThreshold: probeConfig.livenessFailureThreshold,
        livenessSuccessThreshold: probeConfig.livenessSuccessThreshold,
        livenessProbeEnabled: probeConfig.livenessProbeEnabled,
        
        // 就绪探针
        readinessProbePath: probeConfig.readinessProbePath,
        readinessProbePort: probeConfig.readinessProbePort,
        readinessInitialDelaySeconds: probeConfig.readinessInitialDelaySeconds,
        readinessTimeoutSeconds: probeConfig.readinessTimeoutSeconds,
        readinessPeriodSeconds: probeConfig.readinessPeriodSeconds,
        readinessFailureThreshold: probeConfig.readinessFailureThreshold,
        readinessSuccessThreshold: probeConfig.readinessSuccessThreshold,
        readinessProbeEnabled: probeConfig.readinessProbeEnabled,
      });
      // 清空错误信息
      setFormErrors([]);
    }
  }, [visible, probeConfig, form]);
  
  // 修复错误处理的类型问题
  const handleSubmit = async () => {
    setSaving(true);
    setFormErrors([]);
    
    try {
      // 验证表单
      const values = await form.validateFields();
      
      // 使用useProbe钩子保存配置
      if (saveProbes) {
        const success = await saveProbes(values as ProbeConfig);
        if (success) {
          onSave(values as ProbeConfig);
        }
      } else {
        // 如果没有saveProbes函数，直接调用父组件回调
        onSave(values as ProbeConfig);
      }
    } catch (err: any) { // 使用any类型以处理错误对象
      // 表单验证失败
      console.error('表单验证失败:', err);
      if (err && err.errorFields) {
        const errors = err.errorFields.map((field: any) => field.errors[0]);
        setFormErrors(errors);
      } else {
        setFormErrors(['表单验证失败，请检查输入项']);
      }
    } finally {
      setSaving(false);
    }
  };

  // 应用模板
  const applyTemplate = (template: ProbeTemplate) => {
    // 确保所有字段都被设置，避免部分更新
    const newConfig = {
      ...form.getFieldsValue(),
      ...template.config
    };
    form.setFieldsValue(newConfig);
    setTemplateDrawerVisible(false);
    message.success(`已应用${template.name}模板`);
  };

  // 复制探针配置
  const handleCopyConfig = (source: 'startup' | 'liveness' | 'readiness', target: 'startup' | 'liveness' | 'readiness') => {
    const currentValues = form.getFieldsValue();
    const updatedConfig = copyProbeConfig(currentValues, source, target);
    form.setFieldsValue(updatedConfig);
    message.success(`已将${source === 'startup' ? '启动' : source === 'liveness' ? '存活' : '就绪'}探针配置复制到${target === 'startup' ? '启动' : target === 'liveness' ? '存活' : '就绪'}探针`);
  };

  // 检查是否有端口冲突
  const checkPortConflicts = () => {
    const values = form.getFieldsValue();
    const ports = new Set<number>();
    let conflict = false;
    
    // 检查所有已填写的端口是否冲突
    if (values.startupProbePort && ports.has(values.startupProbePort)) {
      conflict = true;
    } else if (values.startupProbePort) {
      ports.add(values.startupProbePort);
    }
    
    if (values.livenessProbePort && ports.has(values.livenessProbePort)) {
      conflict = true;
    } else if (values.livenessProbePort) {
      ports.add(values.livenessProbePort);
    }
    
    if (values.readinessProbePort && ports.has(values.readinessProbePort)) {
      conflict = true;
    }
    
    return conflict;
  };

  // 优化Input组件，使端口值实时更新
  const renderProbeConfigForm = (prefix: string, title: string, icon: React.ReactNode, color: string, type: 'startup' | 'liveness' | 'readiness') => {
    // 获取当前端口值，并使用Form.Item的dependencies确保值更新时重新渲染
    const currentPort = Form.useWatch(`${prefix}ProbePort`, form) || 8080;
    
    return (
      <div>
        <Row gutter={[8, 4]}>
          <Col span={24}>
            <Form.Item
              name={`${prefix}ProbePath`}
              label={
                <span>
                  检查路径
                  <Tooltip title="HTTP检查端点的路径，例如/health">
                    <QuestionCircleOutlined style={{ marginLeft: 4 }} />
                  </Tooltip>
                </span>
              }
              style={{ marginBottom: 8 }}
              dependencies={[`${prefix}ProbePort`]} // 添加依赖确保端口变更时更新
            >
              <Input placeholder="/health" addonBefore="http://服务:" addonAfter={`端口${currentPort}`} />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              name={`${prefix}ProbePort`}
              label={
                <span>
                  检查端口
                  <Tooltip title="暴露健康检查的端口">
                    <QuestionCircleOutlined style={{ marginLeft: 4 }} />
                  </Tooltip>
                </span>
              }
              rules={validationRules.port}
              style={{ marginBottom: 8 }}
            >
              <InputNumber min={1} max={65535} style={{ width: '100%' }} />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              name={`${prefix}InitialDelaySeconds`}
              label={
                <span>
                  初始延迟
                  <Tooltip title="容器启动后多久开始探活">
                    <QuestionCircleOutlined style={{ marginLeft: 4 }} />
                  </Tooltip>
                </span>
              }
              rules={validationRules.delay}
              style={{ marginBottom: 8 }}
            >
              <InputNumber min={0} style={{ width: '100%' }} />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              name={`${prefix}TimeoutSeconds`}
              label={
                <span>
                  超时时间
                  <Tooltip title="探针执行的超时时间">
                    <QuestionCircleOutlined style={{ marginLeft: 4 }} />
                  </Tooltip>
                </span>
              }
              rules={validationRules.timeout}
              style={{ marginBottom: 8 }}
            >
              <InputNumber min={1} style={{ width: '100%' }} />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              name={`${prefix}PeriodSeconds`}
              label={
                <span>
                  检查周期
                  <Tooltip title="两次探针执行的间隔时间">
                    <QuestionCircleOutlined style={{ marginLeft: 4 }} />
                  </Tooltip>
                </span>
              }
              rules={validationRules.period}
              style={{ marginBottom: 8 }}
            >
              <InputNumber min={1} style={{ width: '100%' }} />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              name={`${prefix}FailureThreshold`}
              label={
                <span>
                  失败阈值
                  <Tooltip title="探针连续失败多少次后被视为失败">
                    <QuestionCircleOutlined style={{ marginLeft: 4 }} />
                  </Tooltip>
                </span>
              }
              rules={validationRules.threshold}
              style={{ marginBottom: 8 }}
            >
              <InputNumber min={1} style={{ width: '100%' }} />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              name={`${prefix}SuccessThreshold`}
              label={
                <span>
                  成功阈值
                  <Tooltip title="探针连续成功多少次后被视为成功">
                    <QuestionCircleOutlined style={{ marginLeft: 4 }} />
                  </Tooltip>
                </span>
              }
              rules={validationRules.threshold}
              style={{ marginBottom: 8 }}
            >
              <InputNumber min={1} style={{ width: '100%' }} />
            </Form.Item>
          </Col>
        </Row>
      </div>
    );
  };

  // 渲染模板列表
  const renderTemplateList = () => {
    return (
      <List
        grid={{ gutter: 16, column: 2 }}
        dataSource={probeTemplates}
        renderItem={(template: ProbeTemplate) => (
          <List.Item>
            <Card 
              hoverable 
              style={{ height: '100%' }}
              onClick={() => {
                setSelectedTemplate(template);
              }}
              size="small"
            >
              <div style={{ display: 'flex', alignItems: 'center', marginBottom: 8 }}>
                <AppstoreOutlined style={{ fontSize: 18, marginRight: 8, color: '#1890ff' }} />
                <Typography.Title level={5} style={{ margin: 0 }}>{template.name}</Typography.Title>
              </div>
              <Typography.Paragraph type="secondary" style={{ marginBottom: 8, fontSize: '12px' }}>
                {template.description}
              </Typography.Paragraph>
              <div style={{ display: 'flex', justifyContent: 'space-between', marginTop: 'auto' }}>
                <Button 
                  type="primary" 
                  onClick={(e) => {
                    e.stopPropagation();
                    applyTemplate(template);
                  }}
                  size="small"
                >
                  应用模板
                </Button>
                <Button 
                  onClick={(e) => {
                    e.stopPropagation();
                    setSelectedTemplate(template);
                  }}
                  size="small"
                >
                  查看详情
                </Button>
              </div>
            </Card>
          </List.Item>
        )}
      />
    );
  };

  // 渲染模板详情
  const renderTemplateDetail = () => {
    if (!selectedTemplate) return null;
    
    return (
      <div>
        <div style={{ marginBottom: 16 }}>
          <Button 
            icon={<ArrowLeftOutlined />} 
            onClick={() => setSelectedTemplate(null)}
            style={{ marginRight: 8 }}
            size="small"
          >
            返回列表
          </Button>
          <Button 
            type="primary" 
            icon={<CheckOutlined />} 
            onClick={() => applyTemplate(selectedTemplate)}
            size="small"
          >
            应用此模板
          </Button>
        </div>
        
        <Card title={selectedTemplate.name} bordered={false} size="small">
          <Typography.Paragraph style={{ fontSize: '12px' }}>{selectedTemplate.description}</Typography.Paragraph>
          
          <Divider style={{ margin: '8px 0' }} />
          
          <Collapse defaultActiveKey={['startup', 'liveness', 'readiness']} size="small">
            {selectedTemplate.config.startupProbePath && (
              <Collapse.Panel 
                header={<><ThunderboltOutlined style={{ color: '#fa8c16' }} /> 启动探针配置</>} 
                key="startup"
              >
                <Descriptions column={2} size="small" bordered>
                  <Descriptions.Item label="检查路径">{selectedTemplate.config.startupProbePath}</Descriptions.Item>
                  <Descriptions.Item label="检查端口">{selectedTemplate.config.startupProbePort}</Descriptions.Item>
                  <Descriptions.Item label="初始延迟">{selectedTemplate.config.startupInitialDelaySeconds} 秒</Descriptions.Item>
                  <Descriptions.Item label="超时时间">{selectedTemplate.config.startupTimeoutSeconds} 秒</Descriptions.Item>
                  <Descriptions.Item label="检查周期">{selectedTemplate.config.startupPeriodSeconds} 秒</Descriptions.Item>
                  <Descriptions.Item label="失败阈值">{selectedTemplate.config.startupFailureThreshold}</Descriptions.Item>
                  <Descriptions.Item label="成功阈值">{selectedTemplate.config.startupSuccessThreshold}</Descriptions.Item>
                </Descriptions>
              </Collapse.Panel>
            )}
            
            {selectedTemplate.config.livenessProbePath && (
              <Collapse.Panel 
                header={<><ClockCircleOutlined style={{ color: '#1890ff' }} /> 存活探针配置</>} 
                key="liveness"
              >
                <Descriptions column={2} size="small" bordered>
                  <Descriptions.Item label="检查路径">{selectedTemplate.config.livenessProbePath}</Descriptions.Item>
                  <Descriptions.Item label="检查端口">{selectedTemplate.config.livenessProbePort}</Descriptions.Item>
                  <Descriptions.Item label="初始延迟">{selectedTemplate.config.livenessInitialDelaySeconds} 秒</Descriptions.Item>
                  <Descriptions.Item label="超时时间">{selectedTemplate.config.livenessTimeoutSeconds} 秒</Descriptions.Item>
                  <Descriptions.Item label="检查周期">{selectedTemplate.config.livenessPeriodSeconds} 秒</Descriptions.Item>
                  <Descriptions.Item label="失败阈值">{selectedTemplate.config.livenessFailureThreshold}</Descriptions.Item>
                  <Descriptions.Item label="成功阈值">{selectedTemplate.config.livenessSuccessThreshold}</Descriptions.Item>
                </Descriptions>
              </Collapse.Panel>
            )}
            
            {selectedTemplate.config.readinessProbePath && (
              <Collapse.Panel 
                header={<><CheckCircleOutlined style={{ color: '#52c41a' }} /> 就绪探针配置</>} 
                key="readiness"
              >
                <Descriptions column={2} size="small" bordered>
                  <Descriptions.Item label="检查路径">{selectedTemplate.config.readinessProbePath}</Descriptions.Item>
                  <Descriptions.Item label="检查端口">{selectedTemplate.config.readinessProbePort}</Descriptions.Item>
                  <Descriptions.Item label="初始延迟">{selectedTemplate.config.readinessInitialDelaySeconds} 秒</Descriptions.Item>
                  <Descriptions.Item label="超时时间">{selectedTemplate.config.readinessTimeoutSeconds} 秒</Descriptions.Item>
                  <Descriptions.Item label="检查周期">{selectedTemplate.config.readinessPeriodSeconds} 秒</Descriptions.Item>
                  <Descriptions.Item label="失败阈值">{selectedTemplate.config.readinessFailureThreshold}</Descriptions.Item>
                  <Descriptions.Item label="成功阈值">{selectedTemplate.config.readinessSuccessThreshold}</Descriptions.Item>
                </Descriptions>
              </Collapse.Panel>
            )}
          </Collapse>
        </Card>
      </div>
    );
  };

  // 渲染整合式视图
  const renderIntegratedView = () => {
    // 获取各探针的启用状态
    const startupEnabled = Form.useWatch('startupProbeEnabled', form) !== false; // 默认为true
    const livenessEnabled = Form.useWatch('livenessProbeEnabled', form) !== false;
    const readinessEnabled = Form.useWatch('readinessProbeEnabled', form) !== false;
    
    // 标题栏样式
    const titleStyle = {
      display: 'flex', 
      alignItems: 'center', 
      justifyContent: 'space-between',
      width: '100%'
    };
    
    // 标题文字样式
    const titleTextStyle = (color: string) => ({
      display: 'flex', 
      alignItems: 'center', 
      color, 
      fontSize: '14px',
      fontWeight: 'bold' as const
    });
    
    // 标题右侧控制区域样式
    const controlsStyle = {
      display: 'flex',
      alignItems: 'center',
      gap: '8px'
    };
    
    // 开关容器样式
    const switchContainerStyle = {
      display: 'flex', 
      alignItems: 'center', 
      marginRight: '2px'
    };
    
    return (
      <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
        <Card
          title={
            <div style={titleStyle}>
              <div style={titleTextStyle('#fa8c16')}>
                <ThunderboltOutlined /> <span style={{ marginLeft: 6 }}>启动探针 (Startup Probe)</span>
              </div>
              <div style={controlsStyle}>
                <div style={switchContainerStyle}>
                  <Form.Item 
                    name="startupProbeEnabled" 
                    valuePropName="checked" 
                    style={{ marginBottom: 0 }}
                    initialValue={true}
                  >
                    <Switch 
                      checkedChildren="启用" 
                      unCheckedChildren="禁用" 
                      size="default"
                    />
                  </Form.Item>
                </div>
                <Tooltip title="复制此探针配置到其他探针">
                  <Dropdown
                    menu={{
                      items: probeTypes
                        .filter(pt => pt.key !== 'startup')
                        .map(target => ({
                          key: target.key,
                          label: (
                            <span>
                              {target.icon} 复制到{target.name}
                            </span>
                          ),
                          onClick: () => handleCopyConfig('startup', target.key as 'liveness' | 'readiness')
                        })),
                    }}
                    placement="bottomRight"
                    disabled={!startupEnabled}
                  >
                    <Button 
                      type="text" 
                      size="small" 
                      icon={<CopyOutlined />}
                      style={{ padding: '0 8px', height: '22px', lineHeight: '22px' }}
                    >
                      复制
                    </Button>
                  </Dropdown>
                </Tooltip>
              </div>
            </div>
          }
          headStyle={{ backgroundColor: '#fff7e6', padding: '8px 12px', minHeight: '32px' }}
          bodyStyle={{ padding: '8px 12px' }}
          bordered
          size="small"
        >
          <div style={{ opacity: startupEnabled ? 1 : 0.5, pointerEvents: startupEnabled ? 'auto' : 'none' }}>
            {renderProbeConfigForm('startup', '启动探针', <ThunderboltOutlined />, '#fa8c16', 'startup')}
          </div>
        </Card>
        
        <Card
          title={
            <div style={titleStyle}>
              <div style={titleTextStyle('#1890ff')}>
                <ClockCircleOutlined /> <span style={{ marginLeft: 6 }}>存活探针 (Liveness Probe)</span>
              </div>
              <div style={controlsStyle}>
                <div style={switchContainerStyle}>
                  <Form.Item 
                    name="livenessProbeEnabled" 
                    valuePropName="checked" 
                    style={{ marginBottom: 0 }}
                    initialValue={true}
                  >
                    <Switch 
                      checkedChildren="启用" 
                      unCheckedChildren="禁用" 
                      size="default"
                    />
                  </Form.Item>
                </div>
                <Tooltip title="复制此探针配置到其他探针">
                  <Dropdown
                    menu={{
                      items: probeTypes
                        .filter(pt => pt.key !== 'liveness')
                        .map(target => ({
                          key: target.key,
                          label: (
                            <span>
                              {target.icon} 复制到{target.name}
                            </span>
                          ),
                          onClick: () => handleCopyConfig('liveness', target.key as 'startup' | 'readiness')
                        })),
                    }}
                    placement="bottomRight"
                    disabled={!livenessEnabled}
                  >
                    <Button 
                      type="text" 
                      size="small" 
                      icon={<CopyOutlined />}
                      style={{ padding: '0 8px', height: '22px', lineHeight: '22px' }}
                    >
                      复制
                    </Button>
                  </Dropdown>
                </Tooltip>
              </div>
            </div>
          }
          headStyle={{ backgroundColor: '#e6f7ff', padding: '8px 12px', minHeight: '32px' }}
          bodyStyle={{ padding: '8px 12px' }}
          bordered
          size="small"
        >
          <div style={{ opacity: livenessEnabled ? 1 : 0.5, pointerEvents: livenessEnabled ? 'auto' : 'none' }}>
            {renderProbeConfigForm('liveness', '存活探针', <ClockCircleOutlined />, '#1890ff', 'liveness')}
          </div>
        </Card>
        
        <Card
          title={
            <div style={titleStyle}>
              <div style={titleTextStyle('#52c41a')}>
                <CheckCircleOutlined /> <span style={{ marginLeft: 6 }}>就绪探针 (Readiness Probe)</span>
              </div>
              <div style={controlsStyle}>
                <div style={switchContainerStyle}>
                  <Form.Item 
                    name="readinessProbeEnabled" 
                    valuePropName="checked" 
                    style={{ marginBottom: 0 }}
                    initialValue={true}
                  >
                    <Switch 
                      checkedChildren="启用" 
                      unCheckedChildren="禁用" 
                      size="default"
                    />
                  </Form.Item>
                </div>
                <Tooltip title="复制此探针配置到其他探针">
                  <Dropdown
                    menu={{
                      items: probeTypes
                        .filter(pt => pt.key !== 'readiness')
                        .map(target => ({
                          key: target.key,
                          label: (
                            <span>
                              {target.icon} 复制到{target.name}
                            </span>
                          ),
                          onClick: () => handleCopyConfig('readiness', target.key as 'startup' | 'liveness')
                        })),
                    }}
                    placement="bottomRight"
                    disabled={!readinessEnabled}
                  >
                    <Button 
                      type="text" 
                      size="small" 
                      icon={<CopyOutlined />}
                      style={{ padding: '0 8px', height: '22px', lineHeight: '22px' }}
                    >
                      复制
                    </Button>
                  </Dropdown>
                </Tooltip>
              </div>
            </div>
          }
          headStyle={{ backgroundColor: '#f6ffed', padding: '8px 12px', minHeight: '32px' }}
          bodyStyle={{ padding: '8px 12px' }}
          bordered
          size="small"
        >
          <div style={{ opacity: readinessEnabled ? 1 : 0.5, pointerEvents: readinessEnabled ? 'auto' : 'none' }}>
            {renderProbeConfigForm('readiness', '就绪探针', <CheckCircleOutlined />, '#52c41a', 'readiness')}
          </div>
        </Card>

        {/* 端口冲突提示 */}
        {checkPortConflicts() && (
          <Alert
            message="警告：多个探针使用了相同的端口"
            description="多个探针配置使用相同的端口可能导致冲突，除非您确认这是有意的设计，否则建议使用不同的端口"
            type="warning"
            showIcon
            icon={<ExclamationCircleOutlined />}
            style={{ marginTop: 8 }}
          />
        )}
        
        {/* 表单错误提示 */}
        {formErrors.length > 0 && (
          <Alert
            message="表单验证错误"
            description={
              <ul style={{ margin: 0, paddingLeft: 18 }}>
                {formErrors.map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
            }
            type="error"
            showIcon
            style={{ marginTop: 8 }}
          />
        )}
      </div>
    );
  };

  return (
    <>
      <Modal
        title={
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <span style={{ fontSize: '16px' }}>探针配置编辑</span>
            <Space>
              <Tooltip title="选择预设的探针模板">
                <Button 
                  type="primary"
                  icon={<AppstoreOutlined />} 
                  onClick={() => setTemplateDrawerVisible(true)}
                  size="small"
                >
                  应用模板
                </Button>
              </Tooltip>
            </Space>
          </div>
        }
        open={visible}
        width={760}
        onCancel={onCancel}
        footer={[
          <Button key="cancel" onClick={onCancel} size="small">取消</Button>,
          <Button key="submit" type="primary" loading={saving} onClick={handleSubmit} size="small">保存</Button>
        ]}
        bodyStyle={{ padding: '12px' }}
      >
        <Form
          form={form} 
          layout="vertical"
          initialValues={probeConfig}
          size="small"
        >
          {renderIntegratedView()}
        </Form>
      </Modal>
      
      <Drawer
        title="探针配置模板"
        placement="right"
        width={560}
        onClose={() => {
          setTemplateDrawerVisible(false);
          setSelectedTemplate(null);
        }}
        open={templateDrawerVisible}
      >
        {selectedTemplate ? renderTemplateDetail() : renderTemplateList()}
      </Drawer>
    </>
  );
};

export default ProbeEditor; 