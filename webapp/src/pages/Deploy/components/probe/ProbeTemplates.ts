import { ProbeConfig } from './ProbeTooltip';

/**
 * 探针配置模板
 * 为不同类型的应用提供预设的探针配置
 */
export interface ProbeTemplate {
  key: string;
  name: string;
  description: string;
  icon: string;
  config: ProbeConfig;
}

/**
 * 预设模板列表
 */
export const probeTemplates: ProbeTemplate[] = [
  {
    key: 'spring-boot',
    name: 'Spring Boot',
    description: 'Spring Boot应用的标准探针配置，使用Actuator健康检查端点',
    icon: 'java',
    config: {
      // 启动探针
      startupProbePath: '/actuator/health/startup',
      startupProbePort: 8080,
      startupInitialDelaySeconds: 60,
      startupTimeoutSeconds: 5,
      startupPeriodSeconds: 10,
      startupFailureThreshold: 30,
      startupSuccessThreshold: 1,
      
      // 存活探针
      livenessProbePath: '/actuator/health/liveness',
      livenessProbePort: 8080,
      livenessInitialDelaySeconds: 30,
      livenessTimeoutSeconds: 5,
      livenessPeriodSeconds: 10,
      livenessFailureThreshold: 3,
      livenessSuccessThreshold: 1,
      
      // 就绪探针
      readinessProbePath: '/actuator/health/readiness',
      readinessProbePort: 8080,
      readinessInitialDelaySeconds: 30,
      readinessTimeoutSeconds: 5,
      readinessPeriodSeconds: 10,
      readinessFailureThreshold: 3,
      readinessSuccessThreshold: 1,
    }
  },
  {
    key: 'node-express',
    name: 'Node.js (Express)',
    description: 'Express框架的Node.js应用探针配置',
    icon: 'nodejs',
    config: {
      // 启动探针
      startupProbePath: '/health',
      startupProbePort: 3000,
      startupInitialDelaySeconds: 30,
      startupTimeoutSeconds: 3,
      startupPeriodSeconds: 10,
      startupFailureThreshold: 15,
      startupSuccessThreshold: 1,
      
      // 存活探针
      livenessProbePath: '/health/liveness',
      livenessProbePort: 3000,
      livenessInitialDelaySeconds: 20,
      livenessTimeoutSeconds: 3,
      livenessPeriodSeconds: 10,
      livenessFailureThreshold: 3,
      livenessSuccessThreshold: 1,
      
      // 就绪探针
      readinessProbePath: '/health/readiness',
      readinessProbePort: 3000,
      readinessInitialDelaySeconds: 15,
      readinessTimeoutSeconds: 3,
      readinessPeriodSeconds: 5,
      readinessFailureThreshold: 3,
      readinessSuccessThreshold: 1,
    }
  },
  {
    key: 'nginx',
    name: 'Nginx',
    description: 'Nginx服务器的探针配置',
    icon: 'global',
    config: {
      // 启动探针
      startupProbePath: '/health',
      startupProbePort: 80,
      startupInitialDelaySeconds: 10,
      startupTimeoutSeconds: 2,
      startupPeriodSeconds: 5,
      startupFailureThreshold: 10,
      startupSuccessThreshold: 1,
      
      // 存活探针
      livenessProbePath: '/',
      livenessProbePort: 80,
      livenessInitialDelaySeconds: 10,
      livenessTimeoutSeconds: 2,
      livenessPeriodSeconds: 10,
      livenessFailureThreshold: 3,
      livenessSuccessThreshold: 1,
      
      // 就绪探针
      readinessProbePath: '/',
      readinessProbePort: 80,
      readinessInitialDelaySeconds: 5,
      readinessTimeoutSeconds: 2,
      readinessPeriodSeconds: 5,
      readinessFailureThreshold: 3,
      readinessSuccessThreshold: 1,
    }
  },
  {
    key: 'python-flask',
    name: 'Python (Flask)',
    description: 'Flask框架的Python应用探针配置',
    icon: 'code',
    config: {
      // 启动探针
      startupProbePath: '/health',
      startupProbePort: 5000,
      startupInitialDelaySeconds: 20,
      startupTimeoutSeconds: 3,
      startupPeriodSeconds: 10,
      startupFailureThreshold: 15,
      startupSuccessThreshold: 1,
      
      // 存活探针
      livenessProbePath: '/health/live',
      livenessProbePort: 5000,
      livenessInitialDelaySeconds: 15,
      livenessTimeoutSeconds: 3,
      livenessPeriodSeconds: 10,
      livenessFailureThreshold: 3,
      livenessSuccessThreshold: 1,
      
      // 就绪探针
      readinessProbePath: '/health/ready',
      readinessProbePort: 5000,
      readinessInitialDelaySeconds: 10,
      readinessTimeoutSeconds: 3,
      readinessPeriodSeconds: 5,
      readinessFailureThreshold: 3,
      readinessSuccessThreshold: 1,
    }
  },
  {
    key: 'go-service',
    name: 'Go服务',
    description: 'Go语言开发的微服务探针配置',
    icon: 'code',
    config: {
      // 启动探针
      startupProbePath: '/health',
      startupProbePort: 8090,
      startupInitialDelaySeconds: 10,
      startupTimeoutSeconds: 2,
      startupPeriodSeconds: 5,
      startupFailureThreshold: 12,
      startupSuccessThreshold: 1,
      
      // 存活探针
      livenessProbePath: '/health/liveness',
      livenessProbePort: 8090,
      livenessInitialDelaySeconds: 15,
      livenessTimeoutSeconds: 2,
      livenessPeriodSeconds: 10,
      livenessFailureThreshold: 3,
      livenessSuccessThreshold: 1,
      
      // 就绪探针
      readinessProbePath: '/health/readiness',
      readinessProbePort: 8090,
      readinessInitialDelaySeconds: 8,
      readinessTimeoutSeconds: 2,
      readinessPeriodSeconds: 5,
      readinessFailureThreshold: 3,
      readinessSuccessThreshold: 1,
    }
  },
  {
    key: 'minimal',
    name: '极简配置',
    description: '最小化的探针配置，仅包含基本检查',
    icon: 'experiment',
    config: {
      // 存活探针
      livenessProbePath: '/health',
      livenessProbePort: 8080,
      livenessInitialDelaySeconds: 20,
      livenessTimeoutSeconds: 2,
      livenessPeriodSeconds: 20,
      livenessFailureThreshold: 3,
      livenessSuccessThreshold: 1,
      
      // 就绪探针
      readinessProbePath: '/health',
      readinessProbePort: 8080,
      readinessInitialDelaySeconds: 10,
      readinessTimeoutSeconds: 2,
      readinessPeriodSeconds: 10,
      readinessFailureThreshold: 3,
      readinessSuccessThreshold: 1,
    }
  },
  {
    key: 'custom',
    name: '自定义',
    description: '创建您自己的探针配置',
    icon: 'edit',
    config: {}
  }
];

/**
 * 根据模板Key获取探针配置模板
 * @param key 模板键值
 * @returns 探针配置模板
 */
export function getProbeTemplateByKey(key: string): ProbeTemplate | undefined {
  return probeTemplates.find(template => template.key === key);
}

/**
 * 复制配置：将源探针的配置复制到目标探针
 * @param config 原始配置
 * @param source 源探针类型 ('startup' | 'liveness' | 'readiness')
 * @param target 目标探针类型 ('startup' | 'liveness' | 'readiness')
 * @returns 更新后的配置
 */
export function copyProbeConfig(
  config: ProbeConfig, 
  source: 'startup' | 'liveness' | 'readiness', 
  target: 'startup' | 'liveness' | 'readiness'
): ProbeConfig {
  const result = { ...config };
  
  if (source === 'startup') {
    if (target === 'liveness') {
      result.livenessProbePath = config.startupProbePath;
      result.livenessProbePort = config.startupProbePort;
      result.livenessTimeoutSeconds = config.startupTimeoutSeconds;
      result.livenessPeriodSeconds = config.startupPeriodSeconds;
      result.livenessFailureThreshold = config.startupFailureThreshold;
      result.livenessSuccessThreshold = config.startupSuccessThreshold;
    } else if (target === 'readiness') {
      result.readinessProbePath = config.startupProbePath;
      result.readinessProbePort = config.startupProbePort;
      result.readinessTimeoutSeconds = config.startupTimeoutSeconds;
      result.readinessPeriodSeconds = config.startupPeriodSeconds;
      result.readinessFailureThreshold = config.startupFailureThreshold;
      result.readinessSuccessThreshold = config.startupSuccessThreshold;
    }
  } else if (source === 'liveness') {
    if (target === 'startup') {
      result.startupProbePath = config.livenessProbePath;
      result.startupProbePort = config.livenessProbePort;
      result.startupTimeoutSeconds = config.livenessTimeoutSeconds;
      result.startupPeriodSeconds = config.livenessPeriodSeconds;
      result.startupFailureThreshold = config.livenessFailureThreshold;
      result.startupSuccessThreshold = config.livenessSuccessThreshold;
    } else if (target === 'readiness') {
      result.readinessProbePath = config.livenessProbePath;
      result.readinessProbePort = config.livenessProbePort;
      result.readinessTimeoutSeconds = config.livenessTimeoutSeconds;
      result.readinessPeriodSeconds = config.livenessPeriodSeconds;
      result.readinessFailureThreshold = config.livenessFailureThreshold;
      result.readinessSuccessThreshold = config.livenessSuccessThreshold;
    }
  } else if (source === 'readiness') {
    if (target === 'startup') {
      result.startupProbePath = config.readinessProbePath;
      result.startupProbePort = config.readinessProbePort;
      result.startupTimeoutSeconds = config.readinessTimeoutSeconds;
      result.startupPeriodSeconds = config.readinessPeriodSeconds;
      result.startupFailureThreshold = config.readinessFailureThreshold;
      result.startupSuccessThreshold = config.readinessSuccessThreshold;
    } else if (target === 'liveness') {
      result.livenessProbePath = config.readinessProbePath;
      result.livenessProbePort = config.readinessProbePort;
      result.livenessTimeoutSeconds = config.readinessTimeoutSeconds;
      result.livenessPeriodSeconds = config.readinessPeriodSeconds;
      result.livenessFailureThreshold = config.readinessFailureThreshold;
      result.livenessSuccessThreshold = config.readinessSuccessThreshold;
    }
  }
  
  return result;
} 