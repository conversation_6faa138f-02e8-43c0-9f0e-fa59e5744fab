import React from 'react';
import { Tabs, Descriptions, Card, Typography, Tag, Space, Tooltip, Button } from 'antd';
import { 
  CheckCircleOutlined, 
  ThunderboltOutlined, 
  ClockCircleOutlined,
  QuestionCircleOutlined,
  InfoCircleOutlined,
  EditOutlined
} from '@ant-design/icons';

const { Text } = Typography;

export interface ProbeConfig {
  // 启动探针
  startupProbePath?: string;
  startupProbePort?: number;
  startupInitialDelaySeconds?: number;
  startupTimeoutSeconds?: number;
  startupPeriodSeconds?: number;
  startupFailureThreshold?: number;
  startupSuccessThreshold?: number;
  startupProbeEnabled?: boolean;
  // 存活探针
  livenessProbePath?: string;
  livenessProbePort?: number;
  livenessInitialDelaySeconds?: number;
  livenessTimeoutSeconds?: number;
  livenessPeriodSeconds?: number;
  livenessFailureThreshold?: number;
  livenessSuccessThreshold?: number;
  livenessProbeEnabled?: boolean;
  // 就绪探针
  readinessProbePath?: string;
  readinessProbePort?: number;
  readinessInitialDelaySeconds?: number;
  readinessTimeoutSeconds?: number;
  readinessPeriodSeconds?: number;
  readinessFailureThreshold?: number;
  readinessSuccessThreshold?: number;
  readinessProbeEnabled?: boolean;
}

export interface ProbeTooltipProps {
  probeConfig?: ProbeConfig;
  onEdit?: () => void;
  editable?: boolean;
}

// 帮助提示内容
const ProbeHelpInfo = {
  startup: (
    <div>
      <p>启动探针用于检测应用是否已启动完成。</p>
      <p>应用启动期间存活探针会被延迟执行，避免应用启动过程中被误杀。</p>
      <p>适用于启动较慢的应用。</p>
    </div>
  ),
  liveness: (
    <div>
      <p>存活探针用于检测应用是否存活。</p>
      <p>当存活探针失败时，Kubernetes 会重启容器。</p>
      <p>适用于检测应用是否处于异常状态需要重启。</p>
    </div>
  ),
  readiness: (
    <div>
      <p>就绪探针用于检测应用是否准备好接收流量。</p>
      <p>当就绪探针失败时，Kubernetes 会将容器从服务端点中移除。</p>
      <p>适用于检测应用是否能正常处理请求。</p>
    </div>
  )
};

// 探针类型配色方案
const probeColorScheme = {
  startup: {
    color: '#fa8c16',
    icon: <ThunderboltOutlined />,
    bgColor: '#fff7e6',
  },
  liveness: {
    color: '#1890ff',
    icon: <ClockCircleOutlined />,
    bgColor: '#e6f7ff',
  },
  readiness: {
    color: '#52c41a',
    icon: <CheckCircleOutlined />,
    bgColor: '#f6ffed',
  }
};

const ProbeTooltip: React.FC<ProbeTooltipProps> = ({
  probeConfig = {
    // 启动探针默认值
    startupProbePath: '/health/startupProbe',
    startupProbePort: 8089,
    startupInitialDelaySeconds: 60,
    startupTimeoutSeconds: 3,
    startupPeriodSeconds: 10,
    startupFailureThreshold: 100,
    startupSuccessThreshold: 1,
    startupProbeEnabled: true,
    // 存活探针默认值
    livenessProbePath: '/actuator/health/liveness',
    livenessProbePort: 7279,
    livenessInitialDelaySeconds: 30,
    livenessTimeoutSeconds: 5,
    livenessPeriodSeconds: 10,
    livenessFailureThreshold: 3,
    livenessSuccessThreshold: 1,
    livenessProbeEnabled: true,
    // 就绪探针默认值
    readinessProbePath: '/actuator/health/readiness',
    readinessProbePort: 7279,
    readinessInitialDelaySeconds: 20,
    readinessTimeoutSeconds: 5,
    readinessPeriodSeconds: 5,
    readinessFailureThreshold: 3,
    readinessSuccessThreshold: 1,
    readinessProbeEnabled: true,
  },
  onEdit,
  editable = false
}) => {
  const renderProbeEndpoint = (path?: string, port?: number) => {
    if (!path) return <Text type="secondary">未配置</Text>;
    return (
      <Text code>
        http://[服务]:{port || 80}{path}
      </Text>
    );
  };
  
  const renderProbeDetail = (
    type: 'startup' | 'liveness' | 'readiness',
    path?: string,
    port?: number,
    initialDelay?: number,
    timeout?: number,
    period?: number,
    failureThreshold?: number,
    successThreshold?: number
  ) => {
    const { color, icon, bgColor } = probeColorScheme[type];
    const title = type === 'startup' ? '启动探针' : 
                  type === 'liveness' ? '存活探针' : '就绪探针';
    const englishName = type === 'startup' ? 'Startup Probe' : 
                        type === 'liveness' ? 'Liveness Probe' : 'Readiness Probe';
                        
    return (
      <div style={{ padding: '12px', backgroundColor: bgColor, borderRadius: '4px' }}>
        <div style={{ 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'space-between', 
          marginBottom: '12px',
          borderBottom: `1px solid ${color}40`,
          paddingBottom: '8px'
        }}>
          <Space>
            <span style={{ color: color, fontWeight: 'bold' }}>
              {icon} {title} ({englishName})
            </span>
            <Tooltip title={ProbeHelpInfo[type]} color="#fff" styles={{ root: { color: 'rgba(0,0,0,0.85)' } }}>
              <InfoCircleOutlined style={{ color: '#8c8c8c', cursor: 'pointer' }} />
            </Tooltip>
          </Space>
          {path ? (
            <Tag color={color}>已配置</Tag>
          ) : (
            <Tag color="warning">未配置</Tag>
          )}
        </div>

        <div style={{ display: 'flex', flexWrap: 'wrap', gap: '16px' }}>
          <div style={{ flex: '1 0 100%' }}>
            <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>检查端点</div>
            <div>{renderProbeEndpoint(path, port)}</div>
          </div>
          
          <div style={{ flex: '1 0 30%', minWidth: '120px' }}>
            <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>检查端口</div>
            <div>{port || 80}</div>
          </div>
          
          <div style={{ flex: '1 0 30%', minWidth: '120px' }}>
            <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>初始延迟</div>
            <div>{initialDelay || 0} 秒</div>
          </div>
          
          <div style={{ flex: '1 0 30%', minWidth: '120px' }}>
            <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>超时时间</div>
            <div>{timeout || 1} 秒</div>
          </div>
          
          <div style={{ flex: '1 0 30%', minWidth: '120px' }}>
            <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>检查周期</div>
            <div>{period || 10} 秒</div>
          </div>
          
          <div style={{ flex: '1 0 30%', minWidth: '120px' }}>
            <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>失败阈值</div>
            <div>{failureThreshold || 3}</div>
          </div>
          
          <div style={{ flex: '1 0 30%', minWidth: '120px' }}>
            <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>成功阈值</div>
            <div>{successThreshold || 1}</div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <Card 
      variant="borderless"
      className="probe-tooltip-card"
      style={{ backgroundColor: '#fcfcfc' }}
      styles={{ body: { padding: '0px' } }}
    >
      <div style={{ padding: '8px 12px', display: 'flex', justifyContent: 'space-between', alignItems: 'center', borderBottom: '1px solid #f0f0f0' }}>
        <Typography.Title level={5} style={{ margin: 0 }}>
          容器探针配置
        </Typography.Title>
        {editable && onEdit && (
          <Button 
            type="primary" 
            size="small" 
            icon={<EditOutlined />} 
            onClick={onEdit}
          >
            编辑
          </Button>
        )}
      </div>

      <div style={{ padding: '12px' }}>
        <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
          {renderProbeDetail(
            'startup',
            probeConfig.startupProbePath,
            probeConfig.startupProbePort,
            probeConfig.startupInitialDelaySeconds,
            probeConfig.startupTimeoutSeconds,
            probeConfig.startupPeriodSeconds,
            probeConfig.startupFailureThreshold,
            probeConfig.startupSuccessThreshold
          )}
          
          {renderProbeDetail(
            'liveness',
            probeConfig.livenessProbePath,
            probeConfig.livenessProbePort,
            probeConfig.livenessInitialDelaySeconds,
            probeConfig.livenessTimeoutSeconds,
            probeConfig.livenessPeriodSeconds,
            probeConfig.livenessFailureThreshold,
            probeConfig.livenessSuccessThreshold
          )}
          
          {renderProbeDetail(
            'readiness',
            probeConfig.readinessProbePath,
            probeConfig.readinessProbePort,
            probeConfig.readinessInitialDelaySeconds,
            probeConfig.readinessTimeoutSeconds,
            probeConfig.readinessPeriodSeconds,
            probeConfig.readinessFailureThreshold,
            probeConfig.readinessSuccessThreshold
          )}
        </div>
      </div>
      
      <div style={{ padding: '8px 12px', borderTop: '1px solid #f0f0f0', backgroundColor: '#fafafa' }}>
        <Space>
          <QuestionCircleOutlined style={{ color: '#1890ff' }} />
          <Text type="secondary" style={{ fontSize: '13px' }}>
            探针配置将用于 Kubernetes 部署，确保应用稳定运行并正确处理流量
          </Text>
        </Space>
      </div>
    </Card>
  );
};

export { ProbeTooltip }; 