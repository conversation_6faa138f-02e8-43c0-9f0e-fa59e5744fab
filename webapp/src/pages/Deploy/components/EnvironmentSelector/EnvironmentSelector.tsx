import React, { useState } from 'react';
import { Button, Tooltip } from 'antd';
import styles from '@/pages/Deploy/index.less';
import { ENV_LABEL, ENV_NAME, getEnvIdByName } from '@/constants';
import { CloudOutlined, ExperimentOutlined, GlobalOutlined } from '@ant-design/icons';
import { useModel } from '@umijs/max';

const environments = [
  {
    value: ENV_NAME.PRODUCTION,
    label: ENV_LABEL[ENV_NAME.PRODUCTION],
    color: '#f5222d',
    icon: <GlobalOutlined />,
  },
  {
    value: ENV_NAME.STAGING,
    label: ENV_LABEL[ENV_NAME.STAGING],
    color: '#faad14',
    icon: <CloudOutlined />,
  },
  {
    value: ENV_NAME.TESTING,
    label: ENV_LABEL[ENV_NAME.TESTING],
    color: '#52c41a',
    icon: <ExperimentOutlined />,
  },
];

const EnvironmentSelector: React.FC = () => {
  const { deploy, updateDeploy } = useModel('deploy');
  const [selectedEnv, setSelectedEnv] = useState<string>(deploy.envName);
  return (
    <div className={styles.envSelectorWrapper}>
      <Button.Group className={styles.envButtonGroup}>
        {environments.map((env) => (
          <Tooltip key={env.value} title={`切换到${env.label}环境`} placement="bottom">
            <Button
              key={env.value}
              icon={env.icon}
              size="middle"
              type={selectedEnv === env.value ? 'primary' : 'default'}
              onClick={() => {
                setSelectedEnv(env.value);
                updateDeploy({envId: getEnvIdByName(env.value)!, envName: env.value});
              }}
              className={
                selectedEnv === env.value
                  ? styles.envButtonActive
                  : styles.envButton
              }
              style={{
                backgroundColor:
                  selectedEnv === env.value ? env.color : undefined,
                borderColor:
                  selectedEnv === env.value ? env.color : undefined,
              }}
            >
              {env.label}
            </Button>
          </Tooltip>
        ))}
      </Button.Group>
    </div>
  );
};

export default EnvironmentSelector; 