declare const styles: {
  readonly deployContainer: string;
  readonly sider: string;
  readonly content: string;
  readonly deploySider: string;
  readonly appCard: string;
  readonly groupCard: string;
  readonly deployCard: string;
  readonly appCardTitle: string;
  readonly groupCardTitle: string;
  readonly deployCardTitle: string;
  readonly appCardContent: string;
  readonly groupCardContent: string;
  readonly deployCardContent: string;
  readonly groupItem: string;
  readonly selected: string;
  readonly groupIcon: string;
  readonly groupContent: string;
  readonly groupTitle: string;
  readonly groupDescription: string;
  readonly groupTags: string;
  readonly deploymentTag: string;
  readonly mainTag: string;
  readonly versionTag: string;
  readonly statusTag: string;
  readonly inherit: string;
  readonly custom: string;
  readonly configSection: string;
  readonly sectionTitle: string;
  readonly sectionContent: string;
  readonly configItem: string;
  readonly configLabel: string;
  readonly configValue: string;
  readonly deployProgress: string;
  readonly progressItem: string;
  readonly progressTitle: string;
  readonly deployActions: string;
  readonly environmentSelector: string;
  readonly createGroupBtn: string;
  readonly tabContent: string;
  readonly podList: string;
  readonly podItem: string;
  readonly podHeader: string;
  readonly podName: string;
  readonly podStatus: string;
  readonly running: string;
  readonly pending: string;
  readonly failed: string;
  readonly podInfo: string;
  readonly podInfoItem: string;
  readonly label: string;
  readonly value: string;
  readonly podActions: string;
  readonly logViewer: string;
  readonly logLine: string;
  readonly info: string;
  readonly warning: string;
  readonly error: string;
  readonly success: string;
  readonly timestamp: string;
  readonly historyCard: string;
  readonly historyHeader: string;
  readonly historyVersion: string;
  readonly historyTime: string;
  readonly historyInfo: string;
  readonly historyInfoItem: string;
  readonly historyDescription: string;
  readonly historyActions: string;
  readonly appSelectorPopover: string;
  readonly popoverHeader: string;
  readonly popoverTree: string;
  readonly appInfoPopover: string;
  readonly infoSection: string;
  readonly infoSectionTitle: string;
  readonly infoTable: string;
  readonly infoRow: string;
  readonly infoItem: string;
  readonly infoLabel: string;
  readonly infoValue: string;
  readonly envSelectorWrapper: string;
  readonly envTitle: string;
  readonly envSelector: string;
  readonly envButtonGroup: string;
  readonly envButtonActive: string;
  readonly envButton: string;
  readonly appSection: string;
  readonly appTitle: string;
  readonly appSelectorTrigger: string;
  readonly appName: string;
  readonly appSelectorPopoverOverlay: string;
  readonly topSelectorArea: string;
  readonly topSelectorContainer: string;
  readonly topSelectorCard: string;
  readonly topNavSegmented: string;
  readonly topNavTabs: string;
  readonly customNavButtons: string;
  readonly appInfoInline: string;
  readonly appActionButtons: string;
  readonly topSelectorRight: string;
  readonly customNavButtons: string;
  readonly appInfoInline: string;
  readonly appActionButtons: string;
  readonly topSelectorLeft: string;
  readonly appSelectorWrapper: string;
  readonly envSelectorWrapper: string;
  readonly envTitle: string;
  readonly envSelector: string;
  readonly envButtonGroup: string;
  readonly envButtonActive: string;
  readonly envButton: string;
  readonly contentLayout: string;
  readonly arthasWebConsole: string;
  readonly webConsoleHeader: string;
  readonly webConsoleContent: string;
  readonly podLogsContainer: string;
  readonly podLogsToolbar: string;
};

export default styles; 