import services from '@/services/api';
import dayjs from 'dayjs';

import { 
  IMAGE_TYPE_OPTIONS, 
  IMAGE_TYPE_ENUM,
  IMAGE_STATUS_ENUM 
} from '@/constants/image';

import {
  ActionType,
  FooterToolbar,
  ProCard,
  ProColumns,
  ProForm,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
  ProTable
} from '@ant-design/pro-components';

import { Button, Divider, Drawer, message, Space, Tabs ,Modal} from 'antd';
import React, { useRef, useState } from 'react';

const {
  addImage,
  queryImageList,
  deleteImage,
  modifyImage,
  addComponent,
  queryComponentList,
  deleteComponent,
  modifyComponent,
} = services.ImageController;

// 定义标签页类型
type TabKey = 'images' | 'components';

/**
 * 添加镜像
 * @param fields
 */
const handleAddImage = async (fields: API.ImageInfoVO) => {
  const hide = message.loading('正在添加');
  try {
    await addImage({ ...fields });
    hide();
    message.success('添加成功');
    return true;
  } catch (error) {
    hide();
    message.error('添加失败请重试！');
    return false;
  }
};

/**
 * 更新镜像
 * @param fields
 */
const handleUpdateImage = async (fields: API.ImageInfo) => {
  const hide = message.loading('正在更新');
  try {
    await modifyImage(
      {
        imageId: fields.id || '',
      },
      {
        name: fields.name || '',
        image_name: fields.image_name || '',
        mc_image_name: fields.mc_image_name || '',
        type: fields.type,
        description: fields.description || '',
      },
    );
    hide();
    message.success('更新成功');
    return true;
  } catch (error) {
    hide();
    message.error('更新失败请重试！');
    return false;
  }
};

/**
 * 删除镜像
 * @param selectedRows
 */
const handleRemoveImage = async (selectedRows: API.ImageInfo[]) => {
  const hide = message.loading('正在删除');
  if (!selectedRows) return true;
  try {
    await deleteImage({
      imageId: selectedRows.find((row) => row.id)?.id || '',
    });
    hide();
    message.success('删除成功，即将刷新');
    return true;
  } catch (error) {
    hide();
    message.error('删除失败，请重试');
    return false;
  }
};

/**
 * 添加组件
 * @param fields
 */
const handleAddComponent = async (fields: API.ComponentInfoVO) => {
  const hide = message.loading('正在添加');
  try {
    await addComponent({ ...fields });
    hide();
    message.success('添加成功');
    return true;
  } catch (error) {
    hide();
    message.error('添加失败请重试！');
    return false;
  }
};

/**
 * 更新组件
 * @param fields
 */
const handleUpdateComponent = async (fields: API.ComponentInfo) => {
  const hide = message.loading('正在更新');
  try {
    await modifyComponent(
      {
        componentId: fields.id || '',
      },
      {
        name: fields.name || '',
        version: fields.version || '',
        type: fields.type,
        url: fields.url || '',
        description: fields.description || '',
      },
    );
    hide();
    message.success('更新成功');
    return true;
  } catch (error) {
    hide();
    message.error('更新失败请重试！');
    return false;
  }
};

/**
 * 删除组件
 * @param selectedRows
 */
const handleRemoveComponent = async (selectedRows: API.ComponentInfo[]) => {
  const hide = message.loading('正在删除');
  if (!selectedRows) return true;
  try {
    await deleteComponent({
      componentId: selectedRows.find((row) => row.id)?.id || '',
    });
    hide();
    message.success('删除成功，即将刷新');
    return true;
  } catch (error) {
    hide();
    message.error('删除失败，请重试');
    return false;
  }
};

// 镜像表单组件
const ImageForm: React.FC<{
  onCancel: () => void;
  onSubmit: (values: API.ImageInfoVO) => Promise<void>;
  visible: boolean;
  values?: Partial<API.ImageInfo>;
  title: string;
}> = (props) => {
  return (
    <Drawer
      title={props.title}
      width={600}
      open={props.visible}
      onClose={props.onCancel}
      destroyOnHidden={true}
    >
      <ProForm
        initialValues={props.values}
        onFinish={async (values) => {
          await props.onSubmit(values as API.ImageInfoVO);
        }}
      >
        <ProFormText
          name="name"
          label="名称"
          placeholder="请输入镜像名称"
          rules={[{ required: true, message: '请输入名称' }]}
        />
        <ProFormText
          name="image_name"
          label="官方镜像完整地址"
          placeholder="请输入镜像地址"
          rules={[{ required: true, message: '请输入镜像地址' }]}
        />
        <ProFormText
          name="mc_image_name"
          label="美菜镜像完整地址"
          placeholder="请输入镜像标签"
          rules={[{ required: true, message: '请输入镜像地址' }]}
        />
        <ProFormSelect
          name="type"
          label="镜像类型"
          options={IMAGE_TYPE_OPTIONS}
          placeholder="请选择镜像类型"
          rules={[{ required: true, message: '请选择镜像类型' }]}
        />
        <ProFormTextArea
          name="description"
          label="描述"
          placeholder="请输入描述"
        />
      </ProForm>
    </Drawer>
  );
};

// 组件表单
const ComponentForm: React.FC<{
  onCancel: () => void;
  onSubmit: (values: API.ComponentInfoVO) => Promise<void>;
  visible: boolean;
  values?: Partial<API.ComponentInfo>;
  title: string;
}> = (props) => {
  return (
    <Drawer
      title={props.title}
      width={600}
      open={props.visible}
      onClose={props.onCancel}
      destroyOnHidden={true}
    >
      <ProForm
        initialValues={props.values}
        onFinish={async (values) => {
          await props.onSubmit(values as API.ComponentInfoVO);
        }}
      >
        <ProFormText
          name="name"
          label="名称"
          placeholder="请输入组件名称"
          rules={[{ required: true, message: '请输入名称' }]}
        />
        <ProFormText
          name="version"
          label="版本"
          placeholder="请输入版本号"
          rules={[{ required: true, message: '请输入版本号' }]}
        />
        <ProFormSelect
          name="type"
          label="组件类型"
          options={[
            { label: 'JAR包', value: 'JAR' },
            { label: 'WAR包', value: 'WAR' },
            { label: 'ZIP包', value: 'ZIP' },
            { label: '配置文件', value: 'CONFIG' },
            { label: '其他', value: 'OTHER' },
          ]}
          placeholder="请选择组件类型"
          rules={[{ required: true, message: '请选择组件类型' }]}
        />
        <ProFormText
          name="url"
          label="组件地址"
          placeholder="请输入组件地址"
          rules={[{ required: true, message: '请输入组件地址' }]}
        />
        <ProFormTextArea
          name="description"
          label="描述"
          placeholder="请输入描述"
        />
      </ProForm>
    </Drawer>
  );
};

const ImageComponentPage: React.FC = () => {
  // 标签页状态
  const [activeTab, setActiveTab] = useState<TabKey>('images');

  // 镜像状态管理
  const [imageCreateModalVisible, setImageCreateModalVisible] =
    useState<boolean>(false);
  const [imageUpdateModalVisible, setImageUpdateModalVisible] =
    useState<boolean>(false);
  const [currentImage, setCurrentImage] = useState<API.ImageInfo>();
  const [selectedImageRows, setSelectedImageRows] = useState<API.ImageInfo[]>(
    [],
  );
  const imageActionRef = useRef<ActionType>();

  // 组件状态管理
  const [componentCreateModalVisible, setComponentCreateModalVisible] =
    useState<boolean>(false);
  const [componentUpdateModalVisible, setComponentUpdateModalVisible] =
    useState<boolean>(false);
  const [currentComponent, setCurrentComponent] = useState<API.ComponentInfo>();
  const [selectedComponentRows, setSelectedComponentRows] = useState<
    API.ComponentInfo[]
  >([]);
  const componentActionRef = useRef<ActionType>();

  // 镜像表格列定义
  const imageColumns: ProColumns<API.ImageInfo>[] = [
    {
      title: '名称',
      dataIndex: 'name',
      tip: '镜像名称',
    },
    {
      title: '官方镜像地址',
      dataIndex: 'image_name',
      search: false
    },
    {
      title: '美菜镜像地址',
      dataIndex: 'mc_image_name',
      search: false
    },
    // {
    //   title: '标签',
    //   dataIndex: 'tag',
    // },
    {
      title: '类型',
      dataIndex: 'type',
      valueEnum: IMAGE_TYPE_ENUM,
    },
    {
      title: '状态',
      dataIndex: 'status',
      valueEnum: IMAGE_STATUS_ENUM,
    },
    {
      title: '描述',
      dataIndex: 'description',
      ellipsis: true,
      search: false
    },
    {
      title: '创建时间',
      dataIndex: 'c_t',
      search: false,
      render: (text) => {
        // console.log('c_t:', text, typeof text);
        const ts = Number(text);
        if (!ts) return '-';
        return dayjs.unix(ts).format('YYYY-MM-DD HH:mm:ss');
      },    
    },
    {
      title: '最近更新时间',
      dataIndex: 'u_t',
      search: false,
      render: (text) => text ? dayjs.unix(Number(text)).format('YYYY-MM-DD HH:mm:ss') : '-',
    },
    {
      title: '操作',
      dataIndex: 'option',
      valueType: 'option',
      render: (_, record: API.ImageInfo) => (
        <Space split={<Divider type="vertical" />}>
          <a
            onClick={() => {
              setCurrentImage(record);
              setImageUpdateModalVisible(true);
            }}
          >
            编辑
          </a>
          <a
  onClick={() => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除该镜像吗？',
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        await handleRemoveImage([record]);
        imageActionRef.current?.reload();
      },
    });
  }}
>
  删除
</a>
        </Space>
      ),
    },
  ];

  // 组件表格列定义
  const componentColumns: ProColumns<API.ComponentInfo>[] = [
    {
      title: '名称',
      dataIndex: 'name',
      tip: '组件名称',
    },
    {
      title: '版本',
      dataIndex: 'version',
    },
    {
      title: '类型',
      dataIndex: 'type',
      valueEnum: {
        JAR: { text: 'JAR包' },
        WAR: { text: 'WAR包' },
        ZIP: { text: 'ZIP包' },
        CONFIG: { text: '配置文件' },
        OTHER: { text: '其他' },
      },
    },
    {
      title: '组件地址',
      dataIndex: 'url',
      ellipsis: true,
    },
    {
      title: '描述',
      dataIndex: 'description',
      ellipsis: true,
    },
    {
      title: '创建时间',
      dataIndex: 'c_t',
      valueType: 'dateTime',
    },
    {
      title: '操作',
      dataIndex: 'option',
      valueType: 'option',
      render: (_, record: API.ComponentInfo) => (
        <Space split={<Divider type="vertical" />}>
          <a
            onClick={() => {
              setCurrentComponent(record);
              setComponentUpdateModalVisible(true);
            }}
          >
            编辑
          </a>
          <a
            onClick={async () => {
              await handleRemoveComponent([record]);
              componentActionRef.current?.reloadAndRest?.();
            }}
          >
            删除
          </a>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: '0 24px 24px' }}>
      <Tabs
        activeKey={activeTab}
        onChange={(key) => setActiveTab(key as TabKey)}
        items={[
          {
            key: 'images',
            label: '镜像管理',
            children: (
              <ProCard>
                <ProTable<API.ImageInfo>
                  headerTitle="镜像列表"
                  actionRef={imageActionRef}
                  rowKey="id"
                  search={{
                    labelWidth: 120,
                  }}
                  toolBarRender={() => [
                    <Button
                      key="1"
                      type="primary"
                      onClick={() => setImageCreateModalVisible(true)}
                    >
                      新建镜像
                    </Button>,
                  ]}
                  request={async (params, sorter, filter) => {
                    try{
                      const {data, success } = await queryImageList({
                        ...params,
                        // @ts-ignore
                        sorter,
                        filter,
                
                      });
                      return {
                        data: data?.tasks || [],
                        success: success ?? true,
                        total: data?.total || 0,
                      };
                    }catch(error){
                      return {
                        data: [],
                        success: false,
                        total: 0,
                      }
                    }
                    
                  }}
                  columns={imageColumns}
                //   rowSelection={{
                //     onChange: (_, selectedRows) =>
                //       setSelectedImageRows(selectedRows),
                //   }
                // }
                />
                {/* {selectedImageRows?.length > 0 && (
                  <FooterToolbar
                    extra={
                      <div>
                        已选择{' '}
                        <a style={{ fontWeight: 600 }}>
                          {selectedImageRows.length}
                        </a>{' '}
                        项
                      </div>
                    }
                  >
                    <Button
                      onClick={async () => {
                        await handleRemoveImage(selectedImageRows);
                        setSelectedImageRows([]);
                        imageActionRef.current?.reloadAndRest?.();
                      }}
                    >
                      批量删除
                    </Button>
                  </FooterToolbar>
                )} */}
              </ProCard>
            ),
          },
          {
            key: 'components',
            label: '组件管理',
            children: (
              <ProCard>
                <ProTable<API.ComponentInfo>
                  headerTitle="组件列表"
                  actionRef={componentActionRef}
                  rowKey="id"
                  search={{
                    labelWidth: 120,
                  }}
                  toolBarRender={() => [
                    <Button
                      key="1"
                      type="primary"
                      onClick={() => setComponentCreateModalVisible(true)}
                    >
                      新建组件
                    </Button>,
                  ]}
                  request={async (params, sorter, filter) => {
                    const { data, success } = await queryComponentList({
                      ...params,
                      // FIXME: remove @ts-ignore
                      // @ts-ignore
                      sorter,
                      filter,
                    });
                    return {
                      data: data?.list || [],
                      success,
                    };
                  }}
                  columns={componentColumns}
                  // rowSelection={{
                  //   onChange: (_, selectedRows) =>
                  //     setSelectedComponentRows(selectedRows),
                  // }}
                />
                {/* {selectedComponentRows?.length > 0 && (
                  <FooterToolbar
                    extra={
                      <div>
                        已选择{' '}
                        <a style={{ fontWeight: 600 }}>
                          {selectedComponentRows.length}
                        </a>{' '}
                        项
                      </div>
                    }
                  >
                    <Button
                      onClick={async () => {
                        await handleRemoveComponent(selectedComponentRows);
                        setSelectedComponentRows([]);
                        componentActionRef.current?.reloadAndRest?.();
                      }}
                    >
                      批量删除
                    </Button>
                  </FooterToolbar>
                )} */}
              </ProCard>
            ),
          },
        ]}
      />

      {/* 镜像表单 */}
      <ImageForm
        title="新建镜像"
        visible={imageCreateModalVisible}
        onCancel={() => setImageCreateModalVisible(false)}
        onSubmit={async (values) => {
          await handleAddImage(values);
          setImageCreateModalVisible(false);
          imageActionRef.current?.reload();
        }}
      />
      <ImageForm
        title="编辑镜像"
        visible={imageUpdateModalVisible}
        values={currentImage || {}}
        onCancel={() => {
          setImageUpdateModalVisible(false);
          setCurrentImage(undefined);
        }}
        onSubmit={async (values) => {
          if (currentImage?.id) {
            await handleUpdateImage({
              ...values,
              id: currentImage.id,
            } as API.ImageInfo);
            setImageUpdateModalVisible(false);
            setCurrentImage(undefined);
            imageActionRef.current?.reload();
          }
        }}
      />

      {/* 组件表单 */}
      <ComponentForm
        title="新建组件"
        visible={componentCreateModalVisible}
        onCancel={() => setComponentCreateModalVisible(false)}
        onSubmit={async (values) => {
          await handleAddComponent(values);
          setComponentCreateModalVisible(false);
          componentActionRef.current?.reload();
        }}
      />
      <ComponentForm
        title="编辑组件"
        visible={componentUpdateModalVisible}
        values={currentComponent || {}}
        onCancel={() => {
          setComponentUpdateModalVisible(false);
          setCurrentComponent(undefined);
        }}
        onSubmit={async (values) => {
          if (currentComponent?.id) {
            await handleUpdateComponent({
              ...values,
              id: currentComponent.id,
            } as API.ComponentInfo);
            setComponentUpdateModalVisible(false);
            setCurrentComponent(undefined);
            componentActionRef.current?.reload();
          }
        }}
      />
    </div>
  );
};

export default ImageComponentPage;
