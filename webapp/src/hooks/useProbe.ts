import { useEffect, useState, useCallback, useRef } from 'react';
import { useModel } from '@umijs/max';
import { message } from 'antd';
import { getProbesByAppAndEnvID, saveProbeConfig } from '@/services/api/ProbeController';
import { ProbeConfig } from '@/pages/Deploy/components/probe/ProbeTooltip';

/**
 * 将API的RuntimeProbe模型转换为前端的ProbeConfig模型
 */
const convertToProbeConfig = (probes: API.RuntimeProbe[]): ProbeConfig => {
  const config: ProbeConfig = {};

  // 查找启动探针
  const startupProbe = probes.find(p => p.probe_type === 1);
  if (startupProbe) {
    config.startupProbePath = startupProbe.http_path;
    config.startupProbePort = startupProbe.http_port;
    config.startupInitialDelaySeconds = startupProbe.initial_delay_seconds;
    config.startupTimeoutSeconds = startupProbe.timeout_seconds;
    config.startupPeriodSeconds = startupProbe.period_seconds;
    config.startupFailureThreshold = startupProbe.failure_threshold;
    config.startupSuccessThreshold = startupProbe.success_threshold;
  }

  // 查找存活探针
  const livenessProbe = probes.find(p => p.probe_type === 3);
  if (livenessProbe) {
    config.livenessProbePath = livenessProbe.http_path;
    config.livenessProbePort = livenessProbe.http_port;
    config.livenessInitialDelaySeconds = livenessProbe.initial_delay_seconds;
    config.livenessTimeoutSeconds = livenessProbe.timeout_seconds;
    config.livenessPeriodSeconds = livenessProbe.period_seconds;
    config.livenessFailureThreshold = livenessProbe.failure_threshold;
    config.livenessSuccessThreshold = livenessProbe.success_threshold;
  }

  // 查找就绪探针
  const readinessProbe = probes.find(p => p.probe_type === 2);
  if (readinessProbe) {
    config.readinessProbePath = readinessProbe.http_path;
    config.readinessProbePort = readinessProbe.http_port;
    config.readinessInitialDelaySeconds = readinessProbe.initial_delay_seconds;
    config.readinessTimeoutSeconds = readinessProbe.timeout_seconds;
    config.readinessPeriodSeconds = readinessProbe.period_seconds;
    config.readinessFailureThreshold = readinessProbe.failure_threshold;
    config.readinessSuccessThreshold = readinessProbe.success_threshold;
  }

  return config;
};

/**
 * 将前端的ProbeConfig模型转换为API的RuntimeProbe模型
 */
const convertToRuntimeProbes = (config: ProbeConfig, appId: number, envId: number): Partial<API.RuntimeProbe>[] => {
  const probes: Partial<API.RuntimeProbe>[] = [];

  // 添加启动探针
  if (config.startupProbePath) {
    probes.push({
      app_id: appId,
      env_id: envId,
      probe_type: 1, // 启动探针
      probe_method: 1, // HTTP方法
      initial_delay_seconds: config.startupInitialDelaySeconds || 0,
      timeout_seconds: config.startupTimeoutSeconds || 1,
      period_seconds: config.startupPeriodSeconds || 10,
      failure_threshold: config.startupFailureThreshold || 3,
      success_threshold: config.startupSuccessThreshold || 1,
      http_path: config.startupProbePath,
      http_port: config.startupProbePort,
      http_scheme: 'HTTP',
    });
  }

  // 添加就绪探针
  if (config.readinessProbePath) {
    probes.push({
      app_id: appId,
      env_id: envId,
      probe_type: 2, // 就绪探针
      probe_method: 1, // HTTP方法
      initial_delay_seconds: config.readinessInitialDelaySeconds || 0,
      timeout_seconds: config.readinessTimeoutSeconds || 1,
      period_seconds: config.readinessPeriodSeconds || 10,
      failure_threshold: config.readinessFailureThreshold || 3,
      success_threshold: config.readinessSuccessThreshold || 1,
      http_path: config.readinessProbePath,
      http_port: config.readinessProbePort,
      http_scheme: 'HTTP',
    });
  }

  // 添加存活探针
  if (config.livenessProbePath) {
    probes.push({
      app_id: appId,
      env_id: envId,
      probe_type: 3, // 存活探针
      probe_method: 1, // HTTP方法
      initial_delay_seconds: config.livenessInitialDelaySeconds || 0,
      timeout_seconds: config.livenessTimeoutSeconds || 1,
      period_seconds: config.livenessPeriodSeconds || 10,
      failure_threshold: config.livenessFailureThreshold || 3,
      success_threshold: config.livenessSuccessThreshold || 1,
      http_path: config.livenessProbePath,
      http_port: config.livenessProbePort,
      http_scheme: 'HTTP',
    });
  }

  return probes;
};

/**
 * 探针配置Hook
 * 用于获取和管理应用的探针配置
 */
export default function useProbe() {
  const { updateDeploy, deploy } = useModel('deploy');
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const updateDeployRef = useRef(updateDeploy);

  // 保持ref最新
  useEffect(() => {
    updateDeployRef.current = updateDeploy;
  }, [updateDeploy]);

  // 将API探针数据转换为前端ProbeConfig格式
  const getProbeConfig = useCallback((): ProbeConfig => {
    if (!deploy.runtimeProbes || deploy.runtimeProbes.length === 0) {
      return {};
    }
    
    return convertToProbeConfig(deploy.runtimeProbes);
  }, [deploy.runtimeProbes]);

  // 加载探针配置的方法
  const loadProbes = useCallback(async (appNodeId: number, environment: number) => {
    if (!appNodeId || !environment) return null;
    
    setLoading(true);
    setError(null);
    
    try {
      // 调用API获取探针配置
      const response = await getProbesByAppAndEnvID(appNodeId, environment);
      
      if (response.code === 0 && response.data) {
        // 使用ref获取最新的updateDeploy函数
        updateDeployRef.current({ runtimeProbes: response.data });
        return response.data;
      } else {
        const errorMsg = response?.message || '获取探针配置失败';
        setError(errorMsg);
        message.error(errorMsg);
        return null;
      }
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : '获取探针配置失败';
      setError(errorMsg);
      message.error(errorMsg);
      return null;
    } finally {
      setLoading(false);
    }
  }, []); // 移除updateDeploy依赖

  // 保存探针配置
  const saveProbes = useCallback(async (config: ProbeConfig) => {
    if (!deploy.appData.application.id || !deploy.envId) {
      message.error('应用ID或环境ID未设置');
      return false;
    }
    
    setLoading(true);
    setError(null);
    
    try {
      // 转换为API格式
      const probes = convertToRuntimeProbes(
        config, 
        deploy.appData.application.id, 
        deploy.envId
      );
      
      // 保存每个探针配置
      let success = true;
      for (const probe of probes) {
        const response = await saveProbeConfig(probe);
        if (response.code !== 0) {
          success = false;
          setError(response?.message || '保存探针配置失败');
          message.error(response?.message || '保存探针配置失败');
          break;
        }
      }
      
      if (success) {
        // 重新加载数据
        await loadProbes(deploy.appData.application.id, deploy.envId);
        message.success('探针配置已保存');
        return true;
      }
      
      return false;
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : '保存探针配置失败';
      setError(errorMsg);
      message.error(errorMsg);
      return false;
    } finally {
      setLoading(false);
    }
  }, [loadProbes]);

  useEffect(() => {
    if (deploy.appData.application.id && deploy.envId) {
      loadProbes(deploy.appData.application.id, deploy.envId);
    }
  }, [deploy.appData.application.id, deploy.envId]); // 移除loadProbes依赖

  return {
    loading,
    error,
    loadProbes,
    saveProbes,
    getProbeConfig
  };
} 