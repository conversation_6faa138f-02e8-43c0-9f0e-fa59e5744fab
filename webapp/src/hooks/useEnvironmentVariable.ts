import { useEffect, useCallback, useState } from 'react';
import { useModel } from '@umijs/max';
import { message } from 'antd';
import { getByAppAndEnvID, create, update } from '@/services/api/EnvironmentVariableController';
import { EnvVariableTooltipView } from '@/types/environment_variable';

/**
 * 环境变量Hook
 */
export default function useEnvironmentVariable(appId?: number, envId?: number) {
  const { deploy, updateDeploy } = useModel('deploy');
  
  
  const [envVariableTooltipView, setEnvVariableTooltipView] = useState<EnvVariableTooltipView>({
    id: 0,
    app_id: 0,
    env_id: 0,
    key_name: '',
    value: '',
    description: '',
    c_t: 0,
    create_by: 0,
    u_t: 0,
    update_by: 0,
    status: 0,
    variable_type: 0,
    scriptExecutionResult: null,
    scriptExecutionError: null,
    scriptExecutionLoading: false,
  });

  const updateEnvVariableTooltipView = useCallback((view: Partial<EnvVariableTooltipView>) => {
    setEnvVariableTooltipView((prev) => ({ ...prev, ...view }));
  }, []);

  // 加载应用数据的方法
  const loadEnvironmentVariables = useCallback(async (appNodeId: number, envId: number) : Promise<API.EnvironmentVariable[] | null> => {
    if (!appNodeId || !envId) return null;
    
    try {
      // 调用API获取应用数据
      const response = await getByAppAndEnvID(appNodeId, envId);
      
      if (response.code === 0 && response.data) {
        // 更新全局状态
        updateDeploy({
          environmentVariables: response.data,
        });
        
        return response.data;
      } else {
        const errorMsg = response?.message || '获取应用数据失败';
        message.error(errorMsg);
        return null;
      }
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : '获取应用数据失败';
      message.error(errorMsg);
      return null;
    }
  }, [updateDeploy]);

  const createEnvironmentVariable = useCallback(async (params: API.CreateEnvironmentVariableRequest) : Promise<API.EnvironmentVariable | null> => {
    const response = await create(params);
    if (response.code === 0 && response.data) {
      if (appId && envId) {
        await loadEnvironmentVariables(appId, envId);
      }
      
      return response.data;
    }
    return null;
  }, [deploy.environmentVariables, updateDeploy]);

  const updateEnvironmentVariable = useCallback(async (params: API.UpdateEnvironmentVariableRequest): Promise<API.EnvironmentVariable | null> => {
    const response = await update(params);
    if (response.code === 0 && response.data) {
      // 更新成功后，重新加载完整列表数据
      
      if (appId && envId) {
        await loadEnvironmentVariables(appId, envId);
      }
      return response.data;
    }
    return null;
  }, [appId, envId, loadEnvironmentVariables]);

  // 当appId或envId变化时，自动加载数据
  useEffect(() => {
    if (appId && envId) {
      loadEnvironmentVariables(appId, envId);
    }
  }, [appId, envId]);

  return {
    envVariableTooltipView,
    updateEnvVariableTooltipView,
    loadEnvironmentVariables,
    createEnvironmentVariable,
    updateEnvironmentVariable,
  };
} 