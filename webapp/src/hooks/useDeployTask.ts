import { createDeployTask } from '@/services/api/DeployTaskController';
import { DeployTaskOperationView } from '@/types/deploytask';
import { useModel } from '@umijs/max';
import { useState } from 'react';
import { useFormContext } from '@/contexts/FormContext';

/**
 * 部署任务加载Hook
 * 使用FormContext获取表单实例
 */
export default function useDeployTask() {
  const { form } = useFormContext();
  const { deploy } = useModel('deploy');
  const [deployTaskOperationView, setDeployTaskOperationView] = useState<DeployTaskOperationView>({
    deployType: 'deploy',
    isBranch: true,
    semver: '',
    commitId: '',
    selectedGroupIds: [],
    description: '',
    rdmId: '',
  });


  const updateDeployTaskOperationView = (view: Partial<DeployTaskOperationView>) => {
    setDeployTaskOperationView({
      ...deployTaskOperationView,
      ...view,
    });
  };

  const selectAllGroup = () => {
    const allGroupIds = deploy.groupList.map((group: API.Group) => group.id);
    updateDeployTaskOperationView({
      selectedGroupIds: allGroupIds,
    });
    
    // 同步更新表单值
    if (form) {
      form.setFieldsValue({ deployGroups: allGroupIds });
    }
  };

  const clearAllGroup = () => {
    // 保留当前分组
    const currentGroup = deploy.groupId ? [deploy.groupId] : [];
    updateDeployTaskOperationView({
      selectedGroupIds: currentGroup,
    });
    
    // 同步更新表单值
    if (form) {
      form.setFieldsValue({ deployGroups: currentGroup });
    }
  };

  const invertAllGroup = () => {
    const allGroupIds = deploy.groupList.map((group: API.Group) => group.id);
    const invertedIds = allGroupIds.filter((id: number) => !deployTaskOperationView.selectedGroupIds.includes(id));
    
    // 确保当前分组被选中
    if (!invertedIds.includes(deploy.groupId)) {
      invertedIds.push(deploy.groupId);
    }
    
    updateDeployTaskOperationView({
      selectedGroupIds: invertedIds,
    });
    
    // 同步更新表单值
    if (form) {
      form.setFieldsValue({ deployGroups: invertedIds });
    }
  };


  // 创建部署任务
  const addDeployTask = async () : Promise<API.DeployTask | null> => {
    const params: API.CreateDeployTaskRequest = {
      app_id: deploy.appData.application.id,
      env_id: deploy.envId,
      is_branch: deployTaskOperationView.isBranch ? 1: 0,
      group_ids: deployTaskOperationView.selectedGroupIds,
      semver: deployTaskOperationView.semver,
      commit_id: deployTaskOperationView.commitId,
      deploy_type: deployTaskOperationView.deployType,
      description: deployTaskOperationView.description,
    };
    
    try {
      const response = await createDeployTask(params);
      return response.data;
    } catch (error) {
      console.error('创建部署任务错误:', error);
      return null;
    }
  };

  return {
    selectAllGroup,
    clearAllGroup,
    invertAllGroup,
    deployTaskOperationView,
    updateDeployTaskOperationView,
    addDeployTask,
  };
} 