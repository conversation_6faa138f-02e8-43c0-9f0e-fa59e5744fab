import { useEffect, useCallback, useState } from 'react';
import { useModel } from '@umijs/max';
import { message } from 'antd';
import { 
  getContainerConfig, 
  createContainerConfig, 
  updateContainerConfig 
} from '@/services/api/ContainerController';

/**
 * 容器配置Hook
 * 用于获取和管理应用的容器配置
 */
export default function useContainerConfig() {
  const { deploy } = useModel('deploy');
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [containerConfig, setContainerConfig] = useState<API.Container | null>(null);

  // 默认容器配置
  const getDefaultConfig = useCallback((): Omit<API.Container, 'id' | 'c_t' | 'create_by' | 'u_t' | 'update_by' | 'is_deleted'> => ({
    app_id: deploy.appData?.application?.id || 0,
    env_id: deploy.envId || 0,
    group_id: 0, // 应用级别配置
    base_image: 'openjdk:17-jre-slim',
    replicas: 1,
    namespace: 'default',
    ports: [
      {
        name: 'http',
        container_port: 8080,
        service_port: 8080,
        protocol: 'TCP'
      }
    ],
    resources: {
      requests: {
        cpu: '500m',
        memory: '512Mi'
      },
      limits: {
        cpu: '1000m',
        memory: '1Gi'
      }
    },
    environment: {},
    strategy: 'RollingUpdate',
    rollout_config: {
      max_surge: '25%',
      max_unavailable: '25%',
      revision_history_limit: 10,
      timeout_seconds: 600
    },
    status: 1
  }), [deploy.appData?.application?.id, deploy.envId]);

  // 加载容器配置
  const loadContainerConfig = useCallback(async (appId: number, envId: number): Promise<API.Container | null> => {
    if (!appId || !envId) return null;
    
    setLoading(true);
    setError(null);
    
    try {
      const response = await getContainerConfig({
        app_id: appId,
        env_id: envId
      });
      
      if (response.code === 0 && response.data) {
        setContainerConfig(response.data);
        return response.data;
      } else {
        // 如果没有配置，使用默认配置
        const defaultConfig = getDefaultConfig();
        setContainerConfig({
          ...defaultConfig,
          id: 0,
          c_t: Date.now(),
          create_by: 0,
          u_t: Date.now(),
          update_by: 0,
          is_deleted: 0
        } as API.Container);
        return null;
      }
    } catch (err) {
      console.warn('获取容器配置失败，使用默认配置:', err);
      // 出错时也使用默认配置
      const defaultConfig = getDefaultConfig();
      setContainerConfig({
        ...defaultConfig,
        id: 0,
        c_t: Date.now(),
        create_by: 0,
        u_t: Date.now(),
        update_by: 0,
        is_deleted: 0
      } as API.Container);
      return null;
    } finally {
      setLoading(false);
    }
  }, [getDefaultConfig]);

  // 保存容器配置
  const saveContainerConfig = useCallback(async (config: Partial<API.Container>): Promise<boolean> => {
    if (!deploy.appData?.application?.id || !deploy.envId) {
      message.error('应用ID或环境ID未设置');
      return false;
    }
    
    setLoading(true);
    setError(null);
    
    try {
      let response;
      
      if (containerConfig?.id && containerConfig.id > 0) {
        // 更新现有配置
        response = await updateContainerConfig({
          ...config,
          app_id: deploy.appData.application.id,
          env_id: deploy.envId,
        });
      } else {
        // 创建新配置
        const newConfig = {
          ...getDefaultConfig(),
          ...config,
          app_id: deploy.appData.application.id,
          env_id: deploy.envId,
        };
        response = await createContainerConfig(newConfig);
      }
      
      if (response.code === 0 && response.data) {
        setContainerConfig(response.data);
        message.success('容器配置已保存');
        return true;
      } else {
        const errorMsg = response?.message || '保存容器配置失败';
        setError(errorMsg);
        message.error(errorMsg);
        return false;
      }
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : '保存容器配置失败';
      setError(errorMsg);
      message.error(errorMsg);
      return false;
    } finally {
      setLoading(false);
    }
  }, [deploy.appData?.application?.id, deploy.envId, containerConfig, getDefaultConfig]);

  // 重置为默认配置
  const resetToDefault = useCallback(() => {
    const defaultConfig = getDefaultConfig();
    setContainerConfig({
      ...defaultConfig,
      id: 0,
      c_t: Date.now(),
      create_by: 0,
      u_t: Date.now(),
      update_by: 0,
      is_deleted: 0
    } as API.Container);
  }, [getDefaultConfig]);

  // 监听应用数据变化，自动加载配置
  useEffect(() => {
    if (deploy.appData?.application?.id && deploy.envId) {
      loadContainerConfig(deploy.appData.application.id, deploy.envId);
    }
  }, [deploy.appData?.application?.id, deploy.envId, loadContainerConfig]);

  return {
    // 数据
    containerConfig,
    loading,
    error,
    
    // 操作方法
    loadContainerConfig,
    saveContainerConfig,
    resetToDefault,
    setContainerConfig,
  };
} 