import { useState, useCallback, useEffect, useRef } from 'react';
import { useModel } from '@umijs/max';
import { message } from 'antd';
import { 
  getGroupList, 
  getGroupDetail, 
  createGroup, 
  updateGroup, 
  deleteGroup, 
  setDefaultGroup 
} from '@/services/api/GroupController';
import eventBus, { EVENTS } from '@/utils/eventBus';

/**
 * 分组管理Hook
 */
export default function useGroup() {
  const { deploy, updateDeploy } = useModel('deploy');
  
  // 状态管理
  const [loading, setLoading] = useState<boolean>(false);
  const [createLoading, setCreateLoading] = useState<boolean>(false);
  const [updateLoading, setUpdateLoading] = useState<boolean>(false);
  const [deleteLoading, setDeleteLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // 确保 groupList 始终是数组
  const groupList = deploy.groupList || [];
  
  // 当前选中的分组
  const currentGroup = groupList.find(group => group.id === deploy.groupId) || null;
  
  // 启用的分组列表
  const enabledGroups = groupList.filter(group => group.status === 1);
  
  // 禁用的分组列表
  const disabledGroups = groupList.filter(group => group.status === 0);

  /**
   * 加载分组列表
   */
  const loadGroupList = useCallback(async (appId?: number, envId?: number) => {
    const targetAppId = appId || deploy.appData?.application?.id;
    const targetEnvId = envId || deploy.envId;
    
    if (!targetAppId || !targetEnvId) {
      return [];
    }

    setLoading(true);
    setError(null);
    
    try {
      const response = await getGroupList({
        app_id: targetAppId,
        env_id: targetEnvId,
      });
      
      if (response.code === 0 && response.data) {
        // 按ID排序
        const sortedGroups = response.data.sort((a, b) => a.id - b.id);
        
        updateDeploy({ 
          groupList: sortedGroups 
        });
        
        // 如果当前没有选中分组，自动选择第一个启用的分组
        if (!deploy.groupId && sortedGroups.length > 0) {
          const enabledGroup = sortedGroups.find(group => group.status === 1);
          if (enabledGroup) {
            updateDeploy({ groupId: enabledGroup.id });
          } else if (sortedGroups.length > 0) {
            // 如果没有启用的分组，选择第一个分组
            updateDeploy({ groupId: sortedGroups[0].id });
          }
        }
        
        return sortedGroups;
      } else {
        const errorMsg = response.message || '获取分组列表失败';
        setError(errorMsg);
        message.error(errorMsg);
        return [];
      }
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : '获取分组列表失败';
      setError(errorMsg);
      message.error(errorMsg);
      return [];
    } finally {
      setLoading(false);
    }
  }, [deploy.appData?.application?.id, deploy.envId, deploy.groupId, updateDeploy]);

  /**
   * 选择分组
   */
  const selectGroup = useCallback((groupId: number) => {
    updateDeploy({ groupId });
    
    // 触发分组选择事件
    eventBus.publish(EVENTS.GROUP_SELECTED, { 
      groupId, 
      group: groupList.find(g => g.id === groupId) 
    });
  }, [updateDeploy, groupList]);

  /**
   * 获取分组详情
   */
  const getGroupInfo = useCallback(async (groupId: number) => {
    try {
      const response = await getGroupDetail(groupId);
      
      if (response.code === 0 && response.data) {
        return response.data;
      } else {
        message.error(response.message || '获取分组详情失败');
        return null;
      }
    } catch (err) {
      console.error('获取分组详情失败:', err);
      message.error('获取分组详情失败');
      return null;
    }
  }, []);

  /**
   * 创建分组
   */
  const createNewGroup = useCallback(async (groupData: API.CreateGroupRequest) => {
    setCreateLoading(true);
    setError(null);
    
    try {
      // 设置默认的app_id和env_id
      const createData = {
        ...groupData,
        app_id: groupData.app_id || deploy.appData?.application?.id,
        env_id: groupData.env_id || deploy.envId,
      };
      
      const response = await createGroup(createData);
      
      if (response.code === 0 && response.data) {
        message.success('创建分组成功');
        
        // 重新加载分组列表
        await loadGroupList();
        
        // 自动选择新创建的分组
        updateDeploy({ groupId: response.data.id });
        
        // 触发分组创建事件
        eventBus.publish(EVENTS.GROUP_CREATED, { group: response.data });
        
        return response.data;
      } else {
        const errorMsg = response.message || '创建分组失败';
        setError(errorMsg);
        message.error(errorMsg);
        return null;
      }
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : '创建分组失败';
      setError(errorMsg);
      message.error(errorMsg);
      return null;
    } finally {
      setCreateLoading(false);
    }
  }, [deploy.appData?.application?.id, deploy.envId, loadGroupList, updateDeploy]);

  /**
   * 更新分组
   */
  const updateGroupInfo = useCallback(async (groupId: number, groupData: Partial<API.CreateGroupRequest>) => {
    setUpdateLoading(true);
    setError(null);
    
    try {
      const response = await updateGroup(groupId, groupData);
      
      if (response.code === 0 && response.data) {
        message.success('更新分组成功');
        
        // 重新加载分组列表
        await loadGroupList();
        
        // 触发分组更新事件
        eventBus.publish(EVENTS.GROUP_UPDATED, { group: response.data });
        
        return response.data;
      } else {
        const errorMsg = response.message || '更新分组失败';
        setError(errorMsg);
        message.error(errorMsg);
        return null;
      }
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : '更新分组失败';
      setError(errorMsg);
      message.error(errorMsg);
      return null;
    } finally {
      setUpdateLoading(false);
    }
  }, [loadGroupList]);

  /**
   * 删除分组
   */
  const deleteGroupById = useCallback(async (groupId: number) => {
    setDeleteLoading(true);
    setError(null);
    
    try {
      const response = await deleteGroup(groupId);
      
      if (response.code === 0) {
        message.success('删除分组成功');
        
        // 如果删除的是当前选中的分组，选择其他分组
        if (deploy.groupId === groupId) {
          const remainingGroups = groupList.filter(g => g.id !== groupId);
          if (remainingGroups.length > 0) {
            const enabledGroup = remainingGroups.find(g => g.status === 1);
            const nextGroupId = enabledGroup ? enabledGroup.id : remainingGroups[0].id;
            updateDeploy({ groupId: nextGroupId });
          } else {
            updateDeploy({ groupId: 0 });
          }
        }
        
        // 重新加载分组列表
        await loadGroupList();
        
        // 触发分组删除事件
        eventBus.publish(EVENTS.GROUP_DELETED, { groupId });
        
        return true;
      } else {
        const errorMsg = response.message || '删除分组失败';
        setError(errorMsg);
        message.error(errorMsg);
        return false;
      }
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : '删除分组失败';
      setError(errorMsg);
      message.error(errorMsg);
      return false;
    } finally {
      setDeleteLoading(false);
    }
  }, [deploy.groupId, groupList, updateDeploy, loadGroupList]);

  /**
   * 设置默认分组
   */
  const setAsDefaultGroup = useCallback(async (groupId: number) => {
    try {
      const response = await setDefaultGroup(groupId);
      
      if (response.code === 0) {
        message.success('设置默认分组成功');
        
        // 重新加载分组列表以更新默认分组状态
        await loadGroupList();
        
        // 触发默认分组设置事件
        eventBus.publish(EVENTS.DEFAULT_GROUP_SET, { groupId });
        
        return true;
      } else {
        message.error(response.message || '设置默认分组失败');
        return false;
      }
    } catch (err) {
      console.error('设置默认分组失败:', err);
      message.error('设置默认分组失败');
      return false;
    }
  }, [loadGroupList]);

  /**
   * 复制分组配置
   */
  const cloneGroup = useCallback(async (sourceGroupId: number, newGroupName: string) => {
    const sourceGroup = groupList.find(g => g.id === sourceGroupId);
    if (!sourceGroup) {
      message.error('源分组不存在');
      return null;
    }

    // 复制分组配置，只复制实际存在的字段
    const cloneData: API.CreateGroupRequest = {
      name: newGroupName,
      code: `${sourceGroup.code}_copy`,
      description: `${sourceGroup.description} (复制)`,
      app_id: sourceGroup.app_id,
      env_id: sourceGroup.env_id,
      status: sourceGroup.status,
    };

    return await createNewGroup(cloneData);
  }, [groupList, createNewGroup]);

  /**
   * 获取分组的基础信息
   */
  const getGroupConfig = useCallback((groupId: number) => {
    const group = groupList.find(g => g.id === groupId);
    if (!group) return null;

    return {
      // 基础信息
      basicInfo: {
        id: group.id,
        code: group.code,
        name: group.name,
        description: group.description,
        app_id: group.app_id,
        env_id: group.env_id,
        status: group.status,
      },
      // 审计信息
      auditInfo: {
        c_t: group.c_t,
        create_by: group.create_by,
        u_t: group.u_t,
        update_by: group.update_by,
        is_deleted: group.is_deleted,
      }
    };
  }, [groupList]);

  /**
   * 验证分组配置
   */
  const validateGroupConfig = useCallback((groupData: Partial<API.CreateGroupRequest>) => {
    const errors: string[] = [];

    // 必填字段验证
    if (!groupData.name?.trim()) {
      errors.push('分组名称不能为空');
    }

    if (!groupData.app_id || groupData.app_id <= 0) {
      errors.push('应用ID不能为空');
    }

    if (!groupData.env_id || groupData.env_id <= 0) {
      errors.push('环境ID不能为空');
    }

    // 状态验证
    if (groupData.status !== undefined && groupData.status !== 0 && groupData.status !== 1) {
      errors.push('状态值必须为0（禁用）或1（启用）');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }, []);

  /**
   * 清空当前选择
   */
  const clearSelection = useCallback(() => {
    updateDeploy({ groupId: 0 });
  }, [updateDeploy]);

  /**
   * 重新加载当前应用的分组数据
   */
  const reloadGroups = useCallback(() => {
    return loadGroupList();
  }, [loadGroupList]);

  useEffect(() => {
    if (deploy.appData?.application?.id && deploy.envId) {
      loadGroupList(deploy.appData?.application?.id, deploy.envId);
    }
  }, [deploy.appData?.application?.id, deploy.envId]);

  // 监听事件
  useEffect(() => {
    // 监听应用数据更新事件
    const handleAppDataUpdated = () => {
      loadGroupList();
    };
    
    eventBus.subscribe(EVENTS.APP_DATA_UPDATED, handleAppDataUpdated);
    
    return () => {
      eventBus.unsubscribe(EVENTS.APP_DATA_UPDATED, handleAppDataUpdated);
    };
  }, [loadGroupList]);

  return {
    // 状态
    loading,
    createLoading,
    updateLoading, 
    deleteLoading,
    error,
    
    // 数据
    groupList,
    currentGroup,
    enabledGroups,
    disabledGroups,
    selectedGroupId: deploy.groupId,
    
    // 基础操作方法
    loadGroupList,
    selectGroup,
    getGroupInfo,
    createNewGroup,
    updateGroupInfo,
    deleteGroupById,
    setAsDefaultGroup,
    clearSelection,
    reloadGroups,
    
    // 高级功能
    cloneGroup,
    getGroupConfig,
    validateGroupConfig,
  };
} 