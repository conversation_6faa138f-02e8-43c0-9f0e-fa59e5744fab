import { useEffect, useState, useCallback, useRef } from 'react';
import { useModel } from '@umijs/max';
import { message } from 'antd';
import { getRuntimeSettingsByAppAndEnvID } from '@/services/api/RuntimeController';

/**
 * 应用数据加载Hook
 * 用于在页面加载时自动获取应用数据
 */
export default function useRuntimeLoader(appId?: number, envId?: number) {
  const { updateRuntime } = useModel('runtime');
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const currentAppIdRef = useRef(appId);
  const currentEnvIdRef = useRef(envId);
  const updateRuntimeRef = useRef(updateRuntime);

  // 保持ref最新
  useEffect(() => {
    currentAppIdRef.current = appId;
    currentEnvIdRef.current = envId;
    updateRuntimeRef.current = updateRuntime;
  }, [appId, envId, updateRuntime]);

  // 加载应用数据的方法
  const loadRuntimeSettings = useCallback(async (appNodeId: number, environment: number) : Promise<API.RuntimeSetting[] | null> => {
    if (!appNodeId || !environment) return null;
    
    setLoading(true);
    setError(null);
    
    try {
      // 调用API获取应用数据
      const response = await getRuntimeSettingsByAppAndEnvID(appNodeId, environment);
      
      if (response.code === 0 && response.data) {
        // 使用ref获取最新的updateRuntime函数
        updateRuntimeRef.current({ 
          runtimeSettings: response.data,
          applicationRuntimeSetting: response.data.find((setting: API.RuntimeSetting) => setting.group_id === 0),
        });
        return response.data;
      } else {
        const errorMsg = response?.message || '获取应用数据失败';
        setError(errorMsg);
        message.error(errorMsg);
        return null;
      }
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : '获取应用数据失败';
      setError(errorMsg);
      message.error(errorMsg);
      return null;
    } finally {
      setLoading(false);
    }
  }, []); // 移除updateRuntime依赖

  // 当nodeId或envId变化时，自动加载数据
  useEffect(() => {
    if (appId && envId) {
      loadRuntimeSettings(appId, envId);
    }
  }, [appId, envId]); // 移除loadRuntimeSettings依赖

  return {
    loading,
    error,
    loadRuntimeSettings,
  };
} 