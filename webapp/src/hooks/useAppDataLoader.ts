import { useEffect, useState, useCallback, useRef } from 'react';
import { useModel } from '@umijs/max';
import { message } from 'antd';
import { getEnvNameById } from '@/constants/env';
import { getAppInfoByNodeIdAndEnvId } from '@/services/api/SrvTreeController';
import eventBus, { EVENTS } from '@/utils/eventBus';

/**
 * 应用数据加载Hook
 * 用于在页面加载时自动获取应用数据
 */
export default function useAppDataLoader(nodeId?: number, envId?: number) {
  const { deploy, updateDeploy } = useModel('deploy');
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const currentNodeIdRef = useRef(nodeId);
  const currentEnvIdRef = useRef(envId);

  // 保持nodeId和envId最新
  useEffect(() => {
    currentNodeIdRef.current = nodeId;
    currentEnvIdRef.current = envId;
  }, [nodeId, envId]);

  // 加载应用数据的方法
  const loadAppData = useCallback(async (appNodeId: number, environment: number) : Promise<API.AppData | null> => {
    if (!appNodeId || !environment) return null;
    
    setLoading(true);
    setError(null);
    
    try {
      // 调用API获取应用数据
      const response = await getAppInfoByNodeIdAndEnvId(appNodeId, environment);
      
      if (response.code === 0 && response.data) {
        updateDeploy({ 
          appData: response.data,
          envId: environment,
          envName: getEnvNameById(environment),
        });
        return response.data;
      } else {
        const errorMsg = response?.message || '获取应用数据失败';
        setError(errorMsg);
        message.error(errorMsg);
        return null;
      }
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : '获取应用数据失败';
      setError(errorMsg);
      message.error(errorMsg);
      return null;
    } finally {
      setLoading(false);
    }
  }, [updateDeploy]); 

  // 重新加载当前应用数据
  const reloadCurrentAppData = useCallback(() => {
    const currentNodeId = currentNodeIdRef.current;
    const currentEnvId = currentEnvIdRef.current;
    
    if (currentNodeId && currentEnvId) {
      return loadAppData(currentNodeId, currentEnvId);
    }
    return Promise.resolve(null);
  }, []); 

  // 监听配置文件创建事件
  useEffect(() => {
    const handleConfigFileCreated = (data: any) => {
      if (data.appId === deploy.appData.application.id && 
          data.envId === deploy.envId) {
        reloadCurrentAppData();
      }
    };
    
    // 监听配置文件创建事件
    eventBus.subscribe(EVENTS.CONFIG_FILE_CREATED, handleConfigFileCreated);
    
    // 监听应用数据需要更新事件
    eventBus.subscribe(EVENTS.APP_DATA_NEEDED, reloadCurrentAppData);
    
    // 组件卸载时取消订阅
    return () => {
      eventBus.unsubscribe(EVENTS.CONFIG_FILE_CREATED, handleConfigFileCreated);
      eventBus.unsubscribe(EVENTS.APP_DATA_NEEDED, reloadCurrentAppData);
    };
  }, [reloadCurrentAppData]);

  // 当nodeId或envId变化时，自动加载数据
  useEffect(() => {
    if (nodeId && envId) {
      loadAppData(nodeId, envId);
    }
  }, [nodeId, envId]); 

  return {
    loading,
    error,
    appData: deploy.appData,
    loadAppData,
    reloadAppData: reloadCurrentAppData,
  };
} 