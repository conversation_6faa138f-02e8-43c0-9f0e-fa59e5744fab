import { useState, useEffect, useMemo } from 'react';
import { useModel } from '@umijs/max';
import { message } from 'antd';
import dayjs from 'dayjs';
import { getDeployTasks, getDeployTaskDetail, queryDeployTasks } from '@/services/api/DeployTaskController';
import type { DeployHistoryView, DeployLogView } from '@/types/deploytask';
import { convertToDeployHistoryView, convertToDeployLogView } from '@/utils/deployTaskConverter';
import eventBus, { EVENTS } from '@/utils/eventBus';

/**
 * 部署历史Hook
 * 用于获取和管理部署历史数据
 */
export default function useDeployHistory() {
  const { deploy } = useModel('deploy');
  
  // 部署历史数据状态
  const [historyList, setHistoryList] = useState<DeployHistoryView[]>([]);
  const [currentHistory, setCurrentHistory] = useState<DeployHistoryView | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [detailLoading, setDetailLoading] = useState<boolean>(false);
  
  // 分页相关状态
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
    showSizeChanger: true,
    pageSizeOptions: ['10', '20', '50', '100'],
  });
  
  // 筛选状态
  const [searchFilter, setSearchFilter] = useState<string>('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [typeFilter, setTypeFilter] = useState<string>('all');
  const [strategyFilter, setStrategyFilter] = useState<string>('all');
  const [groupFilter, setGroupFilter] = useState<string>('all');

  // 获取部署历史数据（使用分页查询API）
  const fetchDeployHistory = async () => {
    if (!deploy.appData?.application?.id || !deploy.envId) {
      return;
    }

    setLoading(true);
    try {
      // 构建查询参数
      const params: API.QueryDeployTasksParams = {
        app_id: deploy.appData.application.id,
        env_id: deploy.envId,
        page: pagination.current,
        page_size: pagination.pageSize
      };
      
      // 添加状态过滤
      if (statusFilter !== 'all') {
        params.status = Number(statusFilter);
      }
      
      // 添加部署类型过滤
      if (typeFilter !== 'all') {
        let deployType = '';
        switch(typeFilter) {
          case '部署': 
            deployType = 'deploy';
            break;
          case '编译部署': 
            deployType = 'build_deploy';
            break;
          case '回滚部署': 
            deployType = 'rollback';
            break;
        }
        if (deployType) {
          params.deploy_type = deployType;
        }
      }
      
      // 添加部署策略过滤
      if (strategyFilter !== 'all') {
        let deployStrategy = '';
        switch(strategyFilter) {
          case '普通': 
            deployStrategy = 'normal';
            break;
          case '蓝绿': 
            deployStrategy = 'blue-green';
            break;
          case '金丝雀': 
            deployStrategy = 'canary';
            break;
        }
        if (deployStrategy) {
          params.deploy_strategy = deployStrategy;
        }
      }
      
      // 添加分组过滤
      if (groupFilter !== 'all') {
        const group = deploy.appData.groups.find(g => g.name === groupFilter);
        if (group) {
          params.group_id = group.id;
        }
      }
      
      // 添加关键字搜索
      if (searchFilter) {
        params.keyword = searchFilter;
      }
      
      const response = await queryDeployTasks(params);
      if (response.code === 0 && response.data) {
        // 更新分页信息
        setPagination(prev => ({
          ...prev,
          current: response.data.page_info.page,
          pageSize: response.data.page_info.page_size,
          total: response.data.page_info.total
        }));
        
        // 创建分组ID到分组的映射
        const groupsMap: Record<string, API.Group> = {};
        deploy.appData.groups.forEach(group => {
          groupsMap[group.id] = group;
        });
        
        // 使用转换工具函数将API数据转换为视图模型
        const histories = response.data.list.map(task => 
          convertToDeployHistoryView(task, groupsMap)
        );
        
        setHistoryList(histories);
      }
    } catch (error) {
      console.error('获取部署历史失败:', error);
      message.error('获取部署历史失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取部署历史详情
  const fetchDeployHistoryDetail = async (historyId: string) => {
    setDetailLoading(true);
    try {
      const response = await getDeployTaskDetail(Number(historyId));
      if (response.code === 0 && response.data) {
        const task = response.data;
        
        // 查找当前历史记录
        const history = historyList.find(h => h.id === historyId);
        if (!history) return null;
        
        // 创建分组ID到分组的映射
        const groupsMap: Record<string, API.Group> = {};
        deploy.appData.groups.forEach(group => {
          groupsMap[group.id] = group;
        });
        
        // 使用转换函数生成完整的历史视图，包含日志
        const detailHistory = convertToDeployHistoryView(task, groupsMap);
        
        setCurrentHistory(detailHistory);
        return detailHistory;
      }
      return null;
    } catch (error) {
      console.error('获取部署任务详情失败:', error);
      message.error('获取部署任务详情失败');
      return null;
    } finally {
      setDetailLoading(false);
    }
  };

  // 处理分页变化
  const handlePaginationChange = (page: number, pageSize?: number) => {
    setPagination(prev => ({
      ...prev,
      current: page,
      pageSize: pageSize || prev.pageSize
    }));
    
    // 页码变化时重新加载数据
    setTimeout(() => {
      fetchDeployHistory();
    }, 0);
  };
  
  // 筛选条件变化处理函数
  const handleFilterChange = () => {
    // 重置到第一页并重新加载数据
    setPagination(prev => ({
      ...prev,
      current: 1
    }));
    
    setTimeout(() => {
      fetchDeployHistory();
    }, 0);
  };

  // 获取分组列表
  const historyGroups = useMemo(() => {
    return Array.from(new Set(historyList.map((history) => history.groupName)));
  }, [historyList]);

  // 当应用或环境变化时，重新获取部署历史
  useEffect(() => {
    fetchDeployHistory();
  }, [deploy.appData?.application?.id, deploy.envId]);

  // 当筛选条件变化时触发过滤
  useEffect(() => {
    handleFilterChange();
  }, [searchFilter, statusFilter, typeFilter, strategyFilter, groupFilter]);

  // 订阅部署任务创建成功事件
  useEffect(() => {
    // 定义事件处理函数
    const handleDeployTaskCreated = (taskResponse: any) => {
      console.log('部署任务创建成功，刷新部署历史:', taskResponse);
      // 刷新部署历史数据
      fetchDeployHistory();
    };
    
    // 订阅事件
    eventBus.subscribe(EVENTS.DEPLOY_TASK_CREATED, handleDeployTaskCreated);
    
    // 组件卸载时取消订阅
    return () => {
      eventBus.unsubscribe(EVENTS.DEPLOY_TASK_CREATED, handleDeployTaskCreated);
    };
  }, []);

  return {
    // 数据
    historyList,
    filteredHistoryList: historyList, // 因为已经在后端过滤，前端无需再次过滤
    paginatedHistoryList: historyList, // 已在后端分页，此处直接返回列表
    currentHistory,
    historyGroups,
    loading,
    detailLoading,
    
    // 分页
    pagination,
    handlePaginationChange,
    
    // 筛选状态
    searchFilter,
    setSearchFilter,
    statusFilter,
    setStatusFilter,
    typeFilter,
    setTypeFilter,
    strategyFilter,
    setStrategyFilter,
    groupFilter,
    setGroupFilter,
    
    // 操作方法
    fetchDeployHistory,
    fetchDeployHistoryDetail,
    setCurrentHistory,
  };
}