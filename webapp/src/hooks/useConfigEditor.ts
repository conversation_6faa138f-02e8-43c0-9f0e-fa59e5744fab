import { useState, useRef, useCallback } from 'react';
import { message } from 'antd';
import { ConfigFileFormat, ValidationResult } from '../types/config';
import { validateConfigSyntax } from '../utils/configValidator';
import { GroupConfigFileView } from '../types/config';

interface UseConfigEditorOptions {
  onSave?: (configFile: API.ConfigFile) => void;
}

export const useConfigEditor = (options: UseConfigEditorOptions = {}) => {
  const [configFile, setConfigFile] = useState<GroupConfigFileView | null>(null);
  const [configFileFormat, setConfigFileFormat] = useState<ConfigFileFormat>('properties');
  const [syntaxError, setSyntaxError] = useState<string | null>(null);
  const [isValidating, setIsValidating] = useState<boolean>(false);
  const [validationPassed, setValidationPassed] = useState<boolean>(true);
  
  const editorRef = useRef<any>(null);

  const updateEditorMarkers = useCallback((
    content: string,
    result: ValidationResult,
  ) => {
    if (!editorRef.current) return;

    // 获取当前编辑器实例
    const editor = editorRef.current;

    // 获取Monaco全局实例
    const monacoEditor = (window as any).monaco;
    if (!monacoEditor || !editor.getModel()) return;

    // 清除现有的所有标记
    monacoEditor.editor.setModelMarkers(editor.getModel(), 'syntax', []);

    // 如果验证通过，不需要添加错误标记
    if (result.valid) return;

    // 获取行号和列号，优先使用验证结果中的位置信息
    let lineNumber = result.line || 1;
    let column = result.column || 1;
    let message = result.error || '未知语法错误';

    // 如果验证结果中没有提供位置信息，尝试从错误消息中提取
    if (!result.line) {
      // 从错误消息中提取行号和列号信息
      if (configFileFormat === 'yaml') {
        // YAML错误格式通常类似于: "YAMLException: duplicated mapping key at line 5, column 3:"
        const lineMatch = message.match(/line (\d+)/i);
        const colMatch = message.match(/column (\d+)/i);
        if (lineMatch) lineNumber = parseInt(lineMatch[1], 10);
        if (colMatch) column = parseInt(colMatch[1], 10);
      } else if (configFileFormat === 'json') {
        // JSON错误格式通常类似于: "Parse error on line 2: Unexpected token"
        const lineMatch = message.match(/line (\d+)/i);
        if (lineMatch) lineNumber = parseInt(lineMatch[1], 10);
      } else if (configFileFormat === 'properties') {
        // Properties错误格式我们自定义为: "第 X 行不符合 key=value 或 key:value 格式"
        const lineMatch = message.match(/第 (\d+) 行/);
        if (lineMatch) lineNumber = parseInt(lineMatch[1], 10);
      }
    }

    // 确保行号在有效范围内
    const lines = content.split('\n');
    if (lineNumber > lines.length) lineNumber = lines.length;
    if (lineNumber < 1) lineNumber = 1;

    // 获取错误行的内容
    const errorLine = lines[lineNumber - 1] || '';

    // 使用Monaco编辑器的原生错误标记系统
    // 这会自动创建波浪线和错误提示
    monacoEditor.editor.setModelMarkers(editor.getModel(), 'syntax', [
      {
        startLineNumber: lineNumber,
        endLineNumber: lineNumber,
        startColumn: column,
        endColumn: column + (errorLine.length > 0 ? errorLine.length : 1),
        message: message,
        severity: monacoEditor.MarkerSeverity.Error,
      },
    ]);

    // 将编辑器滚动到错误行
    editor.revealLineInCenter(lineNumber);
  }, [configFileFormat]);

  const handleContentChange = useCallback((value: string | undefined) => {
    if (configFile) {
      // 更新内容
      const newContent = value || '';
      setConfigFile({
        ...configFile,
        content_payload: {
          content: newContent,
        } as API.ConfigFileContent,
      });

      // 内容变化500ms后进行校验，避免频繁校验
      clearTimeout((window as any).syntaxValidationTimer);
      (window as any).syntaxValidationTimer = setTimeout(() => {
        setIsValidating(true);
        const result = validateConfigSyntax(newContent, configFileFormat);
        setSyntaxError(result.error);
        setValidationPassed(result.valid);
        setIsValidating(false);

        // 更新编辑器中的错误标记
        if (editorRef.current) {
          updateEditorMarkers(newContent, result);
        }
      }, 500);
    }
  }, [configFile, configFileFormat, updateEditorMarkers]);

  const handleEditorDidMount = useCallback((editor: any, monaco: any) => {
    editorRef.current = editor;

    // Monaco编辑器实例保存到window对象中以便后续使用
    if (monaco) {
      (window as any).monaco = monaco;
    }

    // 初始化时进行一次语法检查
    if (configFile?.content_payload) {
      setIsValidating(true);
      const result = validateConfigSyntax(configFile.content_payload.content, configFileFormat);
      setSyntaxError(result.error);
      setValidationPassed(result.valid);
      setIsValidating(false);
      updateEditorMarkers(configFile.content_payload.content, result);
    }
  }, [configFile, configFileFormat, updateEditorMarkers]);

  const saveConfigFile = useCallback(async () => {
    if (!configFile) return;

    // 保存配置文件前的最终校验
    setIsValidating(true);
    const result = validateConfigSyntax(configFile.content_payload?.content || '', configFileFormat);
    setIsValidating(false);

    if (result.valid) {
      try {
        // 等待保存操作完成，ConfigService已经会显示成功消息
        const saveResult = await options.onSave?.(configFile);
        // 不需要再次显示成功消息，因为ConfigService已经处理了
        return saveResult;
      } catch (error) {
        // 错误已在ConfigService中处理
        return false;
      }
    } else {
      setSyntaxError(result.error);
      setValidationPassed(false);
      message.error('配置文件包含语法错误，请修正后重试！');
      return false;
    }
  }, [configFile, configFileFormat, options]);

  const setFormat = useCallback((format: ConfigFileFormat) => {
    setConfigFileFormat(format);
    setSyntaxError(null);
    setValidationPassed(true);
  }, []);

  return {
    configFile,
    setConfigFile,
    configFileFormat,
    setConfigFileFormat: setFormat,
    syntaxError,
    isValidating,
    validationPassed,
    editorRef,
    handleContentChange,
    handleEditorDidMount,
    saveConfigFile,
  };
}; 