import { useEffect, useState } from 'react';
import { ConfigService } from '@/services/config';
import { ConfigViewMode, GroupConfigFileViewType, GroupConfigFileView } from '@/types/config';
import { message } from 'antd';
import eventBus, { EVENTS } from '@/utils/eventBus';
import { createConfigFile as apiCreateConfigFile } from '@/services/api/ConfigController';

/**
 * 配置文件Hook，用于管理配置文件的获取、处理和更新
 */
export const useConfigFiles = (appData: API.AppData) => {
  // 配置文件相关状态
  const [configFiles, setConfigFiles] = useState<API.ConfigFile[]>([]);
  const [applicationConfigFiles, setApplicationConfigFiles] = useState<API.ConfigFile[]>([]);
  const [groupConfigFiles, setGroupConfigFiles] = useState<API.ConfigFile[]>([]);
  const [configFilesLoading, setConfigFilesLoading] = useState<boolean>(false);
  const [currentConfigFile, setCurrentConfigFile] = useState<API.ConfigFile | null>(null);
  const [configViewMode, setConfigViewMode] = useState<ConfigViewMode>('view');
  const [configModalVisible, setConfigModalVisible] = useState<boolean>(false);

  useEffect(() => {
    setConfigFiles(appData.config_files || []);
    getApplicationConfigFiles();
  }, [appData]);


  // 根据分组ID获取筛选的配置文件
  const getGroupConfigFiles = (groupId: number) => {
    setGroupConfigFiles(appData.config_files?.filter(file => file.group_id === groupId) || []);
  };

  // 根据应用ID获取筛选的配置文件
  const getApplicationConfigFiles = () => {
    setApplicationConfigFiles(appData.config_files?.filter(file => file.group_id === 0) || []);
  };

  const getGroupConfigFileView = (groupId: number): GroupConfigFileView[] => {
    if (!appData || !appData.config_files || appData.config_files.length === 0) {
      return [];
    }
    // 获取应用级(全局)配置文件
    const appConfigFiles = appData.config_files?.filter(file => file.group_id === 0) || [];
    
    // 获取分组级配置文件
    const groupConfigFiles = appData.config_files?.filter(file => file.group_id === groupId) || [];
    
    // 整合配置文件，添加viewType属性
    const configFilesWithViewType: GroupConfigFileView[] = [];
    
    // 处理分组配置文件
    groupConfigFiles.forEach(groupFile => {
      // 检查此配置文件是否覆盖了全局配置
      const appFile = appConfigFiles.find(appFile => 
         appFile.path === groupFile.path
      );
      
      const viewType: GroupConfigFileViewType = appFile ? 'overridden' : 'groupOnly';
      configFilesWithViewType.push({
        ...groupFile,
        viewType,
      } as GroupConfigFileView);
    });
    
    // 处理全局配置文件 - 添加被分组继承的全局配置
    appConfigFiles.forEach(appFile => {
      // 检查此全局配置是否已被分组覆盖
      const isOverridden = groupConfigFiles.some(groupFile => 
         groupFile.path === appFile.path
      );
      
      if (!isOverridden) {
        const viewType: GroupConfigFileViewType = 'inherited';
        configFilesWithViewType.push({
          ...appFile,
          viewType,
        } as GroupConfigFileView);
      }
    });
    
    return configFilesWithViewType;
  };


  // 查看配置文件
  const viewConfigFile = async (file: API.ConfigFile) => {
    setConfigFilesLoading(true);

    try {
      const fileWithContent = await ConfigService.getConfigFileContent(file.id);
      if (fileWithContent) {
        file.content_payload = fileWithContent;
        setCurrentConfigFile(file);
        setConfigViewMode('view');
        setConfigModalVisible(true);
      }
    } catch (error) {
      console.error('获取配置文件内容错误:', error);
    } finally {
      setConfigFilesLoading(false);
    }
  };

  // 编辑配置文件
  const editConfigFile = async (file: API.ConfigFile) => {
    setConfigFilesLoading(true);
    try {
      const fileWithContent = await ConfigService.getConfigFileContent(file.id);
      if (fileWithContent) {
        file.content_payload = fileWithContent;
        setCurrentConfigFile(file);
        setConfigViewMode('edit');
        setConfigModalVisible(true);
      }
    } catch (error) {
      console.error('获取配置文件内容错误:', error);
    } finally {
      setConfigFilesLoading(false);
    }
  };

  // 创建配置文件
  const createConfigFile = async (configFileRequest: Config.CreateConfigFileRequest) => {
    try {
      setConfigFilesLoading(true);
      
      // 调用API创建配置文件
      const response = await apiCreateConfigFile(configFileRequest);
      
      if (response.code === 0 && response.data) {
        // 成功创建，更新本地状态
        const newFile = response.data;
        
        // 添加新文件到本地缓存
        setConfigFiles([...configFiles, newFile]);
        
        if (newFile.group_id === 0) {
          setApplicationConfigFiles([...applicationConfigFiles, newFile]);
        } else {
          setGroupConfigFiles([...groupConfigFiles, newFile]);
        }
        
        // 关闭编辑窗口
        closeConfigModal();
        
        // 触发事件，通知应用数据需要更新
        eventBus.publish(EVENTS.CONFIG_FILE_CREATED, {
          fileId: newFile.id,
          appId: newFile.app_id,
          envId: newFile.env_id,
          groupId: newFile.group_id
        });
        
        message.success('配置文件创建成功');
        return true;
      } else {
        message.error(response?.message || '创建配置文件失败');
        return false;
      }
    } catch (error) {
      console.error('创建配置文件错误:', error);
      const errorMsg = error instanceof Error ? error.message : '创建配置文件失败';
      message.error(errorMsg);
      return false;
    } finally {
      setConfigFilesLoading(false);
    }
  };

  // 保存配置文件
  const saveConfigFile = async (file: API.ConfigFile) => {
    try {
      setConfigFilesLoading(true);
      
      // 检查必填字段
      if (!file.name || !file.path) {
        message.error('文件名和路径不能为空');
        return false;
      }

      // 根据ID判断是创建新文件还是更新现有文件
      const isNewFile = file.id === 0;
      
      let result;
      if (isNewFile) {
        // 创建新文件
        result = await ConfigService.createConfigFile(file);
      } else {
        // 更新现有文件
        result = await ConfigService.updateConfigFile(file);
      }
      
      if (result) {
        try {
          // 更新本地缓存的配置文件列表
          if (isNewFile) {
            // 添加新文件到列表
            setConfigFiles([...configFiles, file]);
            if (file.group_id === 0) {
              setApplicationConfigFiles([...applicationConfigFiles, file]);
            } else {
              setGroupConfigFiles([...groupConfigFiles, file]);
            }
          } else {
            // 更新现有文件
            const updatedFiles = configFiles.map(f => 
              f.id === file.id ? file : f
            );
            setConfigFiles(updatedFiles);
            
            if (file.group_id === 0) {
              setApplicationConfigFiles(applicationConfigFiles.map(f => 
                f.id === file.id ? file : f
              ));
            } else {
              setGroupConfigFiles(groupConfigFiles.map(f => 
                f.id === file.id ? file : f
              ));
            }
          }
          
          // 关闭编辑窗口
          closeConfigModal();
          return true;
        } catch (updateError) {
          console.error('更新本地配置文件列表错误:', updateError);
          // 即使本地更新失败，服务端保存已成功，仍然返回true
          closeConfigModal();
          return true;
        }
      }
      
      // 保存失败时不关闭模态框，让用户可以继续编辑
      return false;
    } catch (error) {
      console.error('保存配置文件错误:', error);
      // 显示更具体的错误信息
      const errorMessage = error instanceof Error ? error.message : '保存配置文件失败';
      message.error(errorMessage);
      return false;
    } finally {
      setConfigFilesLoading(false);
    }
  };

  const getNewConfigFile = (groupId: number) => {
    const newConfigFile: API.ConfigFile = {
      name: '',
      path: '',
      format: 'properties',
      id: 0,
      app_id: 0,
      env_id: 0,
      group_id: groupId,
      description: '',
    };
    return newConfigFile;
  };

  // 创建新配置文件
  const createNewConfigFile = (groupId: number) => {
    setCurrentConfigFile(getNewConfigFile(groupId));
    setConfigViewMode('create');
    setConfigModalVisible(true);
  };
  
  // 关闭配置文件模态框
  const closeConfigModal = () => {
    setConfigModalVisible(false);
    setCurrentConfigFile(null);
  };

  // 处理配置文件内容变更
  const handleConfigContentChange = (content: string) => {
    if (currentConfigFile && currentConfigFile.content_payload) {
      // 更新当前配置文件的内容
      const updatedContent = {
        ...currentConfigFile.content_payload,
        content: content
      };
      
      setCurrentConfigFile({
        ...currentConfigFile,
        content_payload: updatedContent
      });
    }
  };

  return {
    // 状态
    configFiles,
    configFilesLoading,
    currentConfigFile,
    configViewMode,
    configModalVisible,
    applicationConfigFiles,
    groupConfigFiles,
    createConfigFile,
    // 方法
    createNewConfigFile,
    getGroupConfigFiles,
    getApplicationConfigFiles,
    viewConfigFile,
    editConfigFile,
    closeConfigModal,
    handleConfigContentChange,
    setCurrentConfigFile,
    getGroupConfigFileView,
    setConfigModalVisible,
    saveConfigFile,
  };
};

export default useConfigFiles; 