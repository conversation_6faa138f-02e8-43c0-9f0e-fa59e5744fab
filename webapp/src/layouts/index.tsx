import {
  AppstoreOutlined,
  DeploymentUnitOutlined,
  SettingOutlined,
} from '@ant-design/icons';
import { Layout, Menu } from 'antd';
import React from 'react';
import { history, Outlet, useLocation } from 'umi';

const { Header, Content } = Layout;

const BasicLayout: React.FC = () => {
  const location = useLocation();

  const menuItems = [
    {
      key: '/deploy',
      icon: <DeploymentUnitOutlined />,
      label: '应用部署',
      onClick: () => history.push('/deploy'),
    },
    {
      key: '/image-component',
      icon: <AppstoreOutlined />,
      label: '镜像与组件',
      onClick: () => history.push('/image-component'),
    },
    {
      key: '/app-management',
      icon: <SettingOutlined />,
      label: '应用管理',
      onClick: () => history.push('/app-management'),
    },
  ];

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Header
        style={{
          background: '#fff',
          borderBottom: '1px solid #f0f0f0',
          padding: '0 24px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}
      >
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <div
            style={{
              fontSize: '20px',
              fontWeight: 'bold',
              marginRight: '40px',
              color: '#1890ff',
            }}
          >
            <AppstoreOutlined style={{ marginRight: '8px' }} />
            部署网关
          </div>
          <Menu
            mode="horizontal"
            selectedKeys={[location.pathname]}
            items={menuItems}
            style={{
              border: 'none',
              background: 'transparent',
              minWidth: '400px',
            }}
          />
        </div>
      </Header>
      <Content
        style={{
          padding: '24px',
          background: '#f5f5f5',
          flex: 1,
        }}
      >
        <Outlet />
      </Content>
    </Layout>
  );
};

export default BasicLayout;
