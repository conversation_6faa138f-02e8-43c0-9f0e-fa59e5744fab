import React from 'react';
import { Card, Typography } from 'antd';

const { Title, Paragraph } = Typography;

interface GuideProps {
  name: string;
}

const Guide: React.FC<GuideProps> = ({ name }) => {
  return (
    <Card>
      <Title level={2}>欢迎使用部署平台，{name}！</Title>
      <Paragraph>
        这是一个现代化的应用部署管理平台，帮助您轻松管理应用的配置、部署和监控。
      </Paragraph>
      <Paragraph>
        主要功能包括：
        <ul>
          <li>配置文件管理与编辑</li>
          <li>多环境部署</li>
          <li>实时监控</li>
          <li>部署历史管理</li>
        </ul>
      </Paragraph>
    </Card>
  );
};

export default Guide; 