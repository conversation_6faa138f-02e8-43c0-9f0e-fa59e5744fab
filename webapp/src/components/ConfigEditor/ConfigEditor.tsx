import React from 'react';
import {
  Modal,
  Space,
  Tag,
  Button,
  Row,
  Col,
  Form,
  Input,
  Radio,
  Tooltip,
  Checkbox,
} from 'antd';
import {
  FileTextOutlined,
  LinkOutlined,
  CheckOutlined,
  WarningOutlined,
  SyncOutlined,
  QuestionCircleOutlined,
  SwapOutlined,
} from '@ant-design/icons';
import { Editor } from '@monaco-editor/react';
import { Typography } from 'antd';
import { ConfigFileFormat, ConfigViewMode, GroupConfigFileView } from '../../types/config';
import { useConfigEditor } from '../../hooks/useConfigEditor';
import { getDefaultContentByFormat, getMonacoLanguage } from '../../utils/configValidator';
import { getMonacoEditorOptions } from './editorOptions';

const { Text } = Typography;

export interface ConfigEditorProps {
  visible: boolean;
  configFile: API.ConfigFile;
  viewMode: ConfigViewMode;
  onCancel: () => void;
  onSave: (configFile: API.ConfigFile) => void;
} 

const ConfigEditor: React.FC<ConfigEditorProps> = ({
  visible,
  configFile,
  viewMode,
  onCancel,
  onSave,
}) => {
  const {
    configFile: currentConfigFile,
    setConfigFile,
    configFileFormat,
    setConfigFileFormat,
    syntaxError,
    isValidating,
    validationPassed,
    handleContentChange,
    handleEditorDidMount,
    saveConfigFile,
  } = useConfigEditor({
    onSave: (file) => {
      onSave(file);
      onCancel();
    },
  });

  React.useEffect(() => {
    if (configFile) {
      setConfigFile(configFile as GroupConfigFileView);
      setConfigFileFormat((configFile.format as ConfigFileFormat) || 'properties');
    }
  }, [configFile, setConfigFile, setConfigFileFormat]);

  const handleFormatChange = (format: ConfigFileFormat) => {
    setConfigFileFormat(format);
    
    // 当格式改变时，如果内容为空，则更新为新格式的默认内容
    if (currentConfigFile && (!currentConfigFile.content_payload?.content)) {
      const newContent = getDefaultContentByFormat(format);
      setConfigFile({
        ...currentConfigFile,
        content_payload: {
          content: newContent,
        } as API.ConfigFileContent,
        format: format,
      });
    }
  };

  return (
    <Modal
      title={
        <Space>
          <FileTextOutlined />
          <span>
            {viewMode === 'view'
              ? '查看配置文件'
              : currentConfigFile?.name
              ? '编辑配置文件'
              : '新建配置文件'}
            {currentConfigFile?.name && `: ${currentConfigFile.name}`}
          </span>
          {currentConfigFile?.format && (
            <Tag color={currentConfigFile.format === 'properties' ? 'green' : currentConfigFile.format === 'yaml' ? 'geekblue' : currentConfigFile.format === 'json' ? 'purple' : currentConfigFile.format === 'xml' ? 'magenta' : 'default'} style={{ marginRight: '4px', fontSize: '11px', lineHeight: '16px', padding: '0 4px' }}>
              {currentConfigFile.format.toUpperCase()}
            </Tag>
          )}
          {currentConfigFile?.viewType === 'inherited' && (
            <Tag color="blue" icon={<LinkOutlined />}>
              继承自全局配置
            </Tag>
          )}
          {currentConfigFile?.viewType === 'overridden' && (
            <Tag color="volcano" icon={<SwapOutlined />}>
              覆盖全局配置
            </Tag>
          )}
          {currentConfigFile?.viewType === 'groupOnly' && (
            <Tag color="orange" icon={<FileTextOutlined />}>
              分组专属配置
            </Tag>
          )}
        </Space>
      }
      open={visible}
      onCancel={onCancel}
      width={900}
      footer={
        viewMode === 'view' ? (
          <Space>
            <Button onClick={onCancel}>关闭</Button>
            {currentConfigFile?.viewType !== 'inherited' && (
              <Button type="primary" onClick={() => {}}>
                编辑
              </Button>
            )}
          </Space>
        ) : (
          <Space>
            <Button onClick={onCancel}>取消</Button>
            <Button
              type="primary"
              onClick={saveConfigFile}
              disabled={isValidating}
            >
              保存
            </Button>
            {isValidating && <SyncOutlined spin />}
          </Space>
        )
      }
    >
      {currentConfigFile && (
        <div>
          {viewMode === 'view' ? (
            <div style={{ marginBottom: '16px' }}>
              <Row gutter={16}>
                <Col span={12}>
                  <Text strong>文件路径:</Text>
                  <br />
                  <Text copyable>{currentConfigFile.path}</Text>
                </Col>
                <Col span={12}>
                  <Text strong>最后修改:</Text>
                  <br />
                  <Text type="secondary">{currentConfigFile.u_t}</Text>
                </Col>
              </Row>
            </div>
          ) : (
            <div style={{ marginBottom: '16px' }}>
              <Form layout="vertical">
                <Form.Item
                  label="文件路径（包含文件名）"
                  style={{ marginBottom: '16px' }}
                >
                  <Input
                    defaultValue={
                      currentConfigFile.path
                    }
                    placeholder="例如: /config/application.properties 或 config/app.yml"
                    style={{ width: '100%' }}
                    onChange={(e) => {
                      setConfigFile({
                        ...currentConfigFile,
                        path: e.target.value,
                        name: e.target.value.split('/').pop() || '',
                      });
                    }}
                  />
                </Form.Item>

                <Form.Item label="文件格式" style={{ marginBottom: '16px' }}>
                  <Radio.Group
                    value={configFileFormat}
                    onChange={(e) => handleFormatChange(e.target.value)}
                    style={{ width: '100%' }}
                  >
                    <Radio.Button value="properties">Properties</Radio.Button>
                    <Radio.Button value="yaml">YAML</Radio.Button>
                    <Radio.Button value="json">JSON</Radio.Button>
                    <Radio.Button value="xml">XML</Radio.Button>
                    <Radio.Button value="shell">Shell Script</Radio.Button>
                    <Radio.Button value="text">Plain Text</Radio.Button>
                  </Radio.Group>
                </Form.Item>
              </Form>
            </div>
          )}

          <div
            style={{
              marginBottom: '8px',
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}
          >
            <Text strong>文件内容:</Text>
            {viewMode === 'edit' && (
              <Space>
                {validationPassed ? (
                  <Tag color="success" icon={<CheckOutlined />}>
                    语法正确
                  </Tag>
                ) : syntaxError ? (
                  <Tooltip title={syntaxError}>
                    <Tag color="error" icon={<WarningOutlined />}>
                      语法错误
                    </Tag>
                  </Tooltip>
                ) : null}
                {isValidating && <SyncOutlined spin />}
              </Space>
            )}
          </div>

          {viewMode === 'view' ? (
            <div
              style={{
                backgroundColor: '#1e1e1e',
                padding: '16px',
                borderRadius: '6px',
                border: '1px solid #d9d9d9',
                fontFamily: 'Monaco, Consolas, "Courier New", monospace',
                fontSize: '13px',
                lineHeight: '1.5',
                whiteSpace: 'pre-wrap',
                maxHeight: '400px',
                overflow: 'auto',
                color: '#d4d4d4',
              }}
            >
              {currentConfigFile.content_payload?.content || '# 暂无内容'}
            </div>
          ) : (
            <div>
              <div style={{ border: '1px solid #d9d9d9', borderRadius: '6px' }}>
                <Editor
                  height="400px"
                  language={getMonacoLanguage(configFileFormat)}
                  theme="vs"
                  value={
                    currentConfigFile.content_payload?.content ||
                    getDefaultContentByFormat(configFileFormat)
                  }
                  onChange={handleContentChange}
                  options={getMonacoEditorOptions(configFileFormat)}
                  onMount={handleEditorDidMount}
                />
              </div>
            </div>
          )}

          {viewMode === 'edit' && (
            <div
              style={{
                marginTop: '16px',
                padding: '12px',
                backgroundColor: '#f8f9fa',
                borderRadius: '6px',
              }}
            >
              <Space direction="vertical" style={{ width: '100%' }}>
                <Checkbox defaultChecked>
                  <Space>
                    <span>替换环境变量</span>
                    <Tooltip title="勾选后，配置文件中的${ENV_NAME}格式的环境变量将在部署时被替换为实际值">
                      <QuestionCircleOutlined style={{ color: '#8c8c8c' }} />
                    </Tooltip>
                  </Space>
                </Checkbox>
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  提示: 使用等宽字体编辑，支持语法高亮。按 Ctrl+A 全选，Ctrl+Z
                  撤销。
                </Text>
              </Space>
            </div>
          )}
        </div>
      )}
    </Modal>
  );
};

export default ConfigEditor; 