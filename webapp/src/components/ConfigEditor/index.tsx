import React from 'react';
import { <PERSON><PERSON>, Spin } from 'antd';
import MonacoEditor from 'react-monaco-editor';
import styles from './style.less';

export interface ConfigEditorProps {
  value: string;
  language: string;
  onChange?: (value: string) => void;
  readOnly?: boolean;
  onSave?: (value: string) => void;
  height?: string | number;
  loading?: boolean;
}

export const ConfigEditor: React.FC<ConfigEditorProps> = ({
  value,
  language,
  onChange,
  readOnly = false,
  onSave,
  height = '400px',
  loading = false
}) => {
  const editorOptions = {
    selectOnLineNumbers: true,
    readOnly,
    scrollBeyondLastLine: false,
    automaticLayout: true,
    minimap: {
      enabled: true
    }
  };

  const handleSave = () => {
    onSave && onSave(value);
  };

  const getEditorLanguage = () => {
    // 将文件类型映射到Monaco支持的语言
    const langMap: Record<string, string> = {
      'properties': 'ini',
      'yaml': 'yaml',
      'yml': 'yaml',
      'json': 'json',
      'xml': 'xml',
      'shell': 'shell',
      'sh': 'shell',
      'bash': 'shell',
      'text': 'plaintext',
      'default': 'plaintext'
    };

    return langMap[language.toLowerCase()] || 'plaintext';
  };

  return (
    <div className={styles.editorContainer}>
      <Spin spinning={loading} tip="加载配置文件内容...">
        <MonacoEditor
          height={height}
          language={getEditorLanguage()}
          value={value}
          options={editorOptions}
          onChange={onChange}
          theme="vs-dark"
        />

        {!readOnly && (
          <div className={styles.editorFooter}>
            <Button type="primary" onClick={handleSave}>
              保存
            </Button>
          </div>
        )}
      </Spin>
    </div>
  );
};

export default ConfigEditor; 