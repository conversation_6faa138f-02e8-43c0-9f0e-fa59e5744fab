import { ConfigFileFormat } from '../../types/config';

export const getMonacoEditorOptions = (format: ConfigFileFormat) => {
  const baseOptions = {
    selectOnLineNumbers: true,
    automaticLayout: true,
    fontSize: 13,
    fontFamily: 'Monaco, Consolas, "Courier New", monospace',
    lineNumbers: 'on' as const,
    roundedSelection: false,
    scrollBeyondLastLine: false,
    readOnly: false,
    theme: 'vs',
    minimap: {
      enabled: false,
    },
    folding: true,
    foldingStrategy: 'auto' as const,
    showFoldingControls: 'mouseover' as const,
    foldingHighlight: true,
    unfoldOnClickAfterEndOfLine: false,
    disableLayerHinting: true,
    fixedOverflowWidgets: true,
    suggest: {
      insertMode: 'replace' as const,
    },
    acceptSuggestionOnCommitCharacter: true,
    acceptSuggestionOnEnter: 'on' as const,
    accessibilitySupport: 'auto' as const,
    autoIndent: 'full' as const,
    colorDecorators: true,
    contextmenu: true,
    cursorBlinking: 'blink' as const,
    cursorSmoothCaretAnimation: false,
    cursorStyle: 'line' as const,
    disableMonospaceOptimizations: false,
    hideCursorInOverviewRuler: false,
    highlightActiveIndentGuide: true,
    links: true,
    mouseWheelZoom: false,
    multiCursorMergeOverlapping: true,
    multiCursorModifier: 'alt' as const,
    overviewRulerBorder: true,
    overviewRulerLanes: 2,
    quickSuggestions: true,
    quickSuggestionsDelay: 100,
    renderControlCharacters: false,
    renderFinalNewline: true,
    renderLineHighlight: 'line' as const,
    renderWhitespace: 'selection' as const,
    revealHorizontalRightPadding: 30,
    smoothScrolling: false,
    suggestOnTriggerCharacters: true,
    wordBasedSuggestions: true,
    wordSeparators: '`~!@#$%^&*()-=+[{]}\\|;:\'",.<>/?',
    wordWrap: 'off' as const,
    wordWrapBreakAfterCharacters: '\t})]?|&,;',
    wordWrapBreakBeforeCharacters: '{([+',
    wordWrapColumn: 80,
    wrappingIndent: 'none' as const,
  };

  // 根据文件格式提供特定的编辑器配置
  switch (format) {
    case 'json':
      return {
        ...baseOptions,
        tabSize: 2,
        insertSpaces: true,
        detectIndentation: false,
        formatOnPaste: true,
        formatOnType: true,
        autoIndent: 'full' as const,
        bracketPairColorization: {
          enabled: true,
        },
      };

    case 'yaml':
      return {
        ...baseOptions,
        tabSize: 2,
        insertSpaces: true,
        detectIndentation: false,
        wordWrap: 'on' as const,
        rulers: [80],
      };

    case 'xml':
      return {
        ...baseOptions,
        tabSize: 2,
        insertSpaces: true,
        detectIndentation: false,
        formatOnPaste: true,
        formatOnType: true,
        autoClosingBrackets: 'always' as const,
        autoClosingQuotes: 'always' as const,
        autoSurround: 'languageDefined' as const,
      };

    case 'shell':
      return {
        ...baseOptions,
        tabSize: 4,
        insertSpaces: true,
        detectIndentation: false,
        wordWrap: 'on' as const,
        rulers: [120],
        autoClosingBrackets: 'always' as const,
        autoClosingQuotes: 'always' as const,
      };

    case 'properties':
      return {
        ...baseOptions,
        tabSize: 4,
        insertSpaces: true,
        detectIndentation: false,
        wordWrap: 'on' as const,
        rulers: [100],
      };

    default:
      return {
        ...baseOptions,
        tabSize: 4,
        insertSpaces: true,
        detectIndentation: true,
        wordWrap: 'on' as const,
      };
  }
}; 