/**
 * 环境类型枚举常量
 */

// 环境ID常量
export const ENV_ID = {
  PRODUCTION: 1,
  STAGING: 2, 
  TESTING: 3,
} as const;

// 环境名称常量
export const ENV_NAME = {
  PRODUCTION: 'production',
  STAGING: 'staging',
  TESTING: 'testing',
} as const;

// 环境中文名称
export const ENV_LABEL = {
  [ENV_NAME.PRODUCTION]: '生产',
  [ENV_NAME.STAGING]: '预发',
  [ENV_NAME.TESTING]: '测试',
} as const;

/**
 * 根据环境名称获取环境ID
 * @param envName 环境名称
 * @returns 环境ID
 */
export function getEnvIdByName(envName: string): number | undefined {
  switch (envName) {
    case ENV_NAME.PRODUCTION:
      return ENV_ID.PRODUCTION;
    case ENV_NAME.STAGING:
      return ENV_ID.STAGING;
    case ENV_NAME.TESTING:
      return ENV_ID.TESTING;
    default:
      return undefined;
  }
}

/**
 * 根据环境ID获取环境名称
 * @param envId 环境ID
 * @returns 环境名称
 */
export function getEnvNameById(envId: number): string | undefined {
  switch (envId) {
    case ENV_ID.PRODUCTION:
      return ENV_NAME.PRODUCTION;
    case ENV_ID.STAGING:
      return ENV_NAME.STAGING;
    case ENV_ID.TESTING:
      return ENV_NAME.TESTING;
    default:
      return undefined;
  }
} 