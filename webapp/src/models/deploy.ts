import { useState, useEffect, useCallback } from 'react';
import { ENV_NAME, getEnvIdByName } from '@/constants';
import { localStore, STORAGE_KEYS } from '@/utils/storage';

interface DeployState {
  appData: API.AppData;
  envId: number;
  envName: string;
  groupId: number;
  nodeId: number;
  runtimeSettings: API.RuntimeSetting[];
  environmentVariables: API.EnvironmentVariable[];
  runtimeProbes: API.RuntimeProbe[]; 
  groupList: API.Group[];
}

export default function useDeploy() {
  // 从本地存储中获取初始状态或使用默认值
  const initialState = localStore.get<DeployState>(STORAGE_KEYS.DEPLOY_STATE) || {
    appData: {
      application: {} as API.Application, 
      app_settings: {} as API.AppSettings, 
      config_files: [] as API.ConfigFile[],
    } as API.AppData,
    envId: getEnvIdByName(ENV_NAME.TESTING)!,
    envName: ENV_NAME.TESTING,
    groupId: 0,
    nodeId: 0,
    environmentVariables: [] as API.EnvironmentVariable[],
    runtimeSettings: [] as API.RuntimeSetting[],
    runtimeProbes: [] as API.RuntimeProbe[],
    groupList: [] as API.Group[]
  };

  const [deploy, setDeploy] = useState<DeployState>(initialState);
  
  // 当 deploy 状态改变时，保存到本地存储
  useEffect(() => {
    localStore.set(STORAGE_KEYS.DEPLOY_STATE, deploy);
  }, [JSON.stringify(deploy)]);

  // 更新状态的方法
  const updateDeploy = useCallback((newData: Partial<DeployState>) => {
    setDeploy(prevData => {
      const updatedData = {
        ...prevData,
        ...newData
      };
      // 状态更新后立即保存到本地存储
      localStore.set(STORAGE_KEYS.DEPLOY_STATE, updatedData);
      return updatedData;
    });
  }, []); // 移除deploy依赖，使用函数式更新
  
  return {
    deploy,
    setDeploy,
    updateDeploy
  };
}
