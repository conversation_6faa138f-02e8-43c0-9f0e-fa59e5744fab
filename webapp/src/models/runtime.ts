import { useState } from 'react';

interface RuntimeState {
  runtimeSettings: API.RuntimeSetting[];
  applicationRuntimeSetting: API.RuntimeSetting;
}

export default function useRuntime() {
  const [runtime, setRuntime] = useState<RuntimeState>({
    runtimeSettings: [] as API.RuntimeSetting[],
    applicationRuntimeSetting: {} as API.RuntimeSetting,
  });

  // 更新状态的方法
  const updateRuntime = (newData: Partial<RuntimeState>) => {
    setRuntime(prevData => {
      const updatedData = {
        ...prevData,
        ...newData
      };

      return updatedData;
    });
  };

  const getGroupRuntimeSetting = (groupId: number) => {
    return runtime.runtimeSettings.find((setting: API.RuntimeSetting) => setting.group_id === groupId);
  };
  
  return {
    runtime,
    setRuntime,
    updateRuntime,
    getGroupRuntimeSetting
  };
}
