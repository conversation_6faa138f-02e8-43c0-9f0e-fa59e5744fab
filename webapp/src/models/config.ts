/**
 * 配置文件管理模型
 * 实现应用级配置和分组级配置的继承关系
 */

// 配置文件类型
export enum ConfigType {
  APPLICATION = 'APPLICATION', // 应用级配置
  GROUP = 'GROUP', // 分组级配置
}

// 配置文件基本信息
export interface ConfigFileInfo {
  id: string;
  name: string;
  type: ConfigType;
  format: 'JSON' | 'YAML' | 'PROPERTIES' | 'XML' | 'TOML' | 'OTHER';
  applicationId?: string;
  groupId?: string;
  content: string;
  description?: string;
  createdAt: string;
  updatedAt: string;
  version: string;
  isActive: boolean;
}

// 配置文件服务接口
export const ConfigService = {
  /**
   * 获取应用配置文件
   * @param applicationId 应用ID
   */
  getApplicationConfig: async (
    applicationId: string,
  ): Promise<ConfigFileInfo | null> => {
    // TODO: 实现实际API调用
    // 这里是模拟实现
    return {
      id: `app-config-${applicationId}`,
      name: '应用级配置',
      type: ConfigType.APPLICATION,
      format: 'YAML',
      applicationId,
      content:
        'app:\n  name: AppName\n  version: 1.0.0\nserver:\n  port: 8080\n',
      description: '应用级配置文件',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      version: '1.0.0',
      isActive: true,
    };
  },

  /**
   * 获取分组配置文件
   * @param applicationId 应用ID
   * @param groupId 分组ID
   */
  getGroupConfig: async (
    applicationId: string,
    groupId: string,
  ): Promise<ConfigFileInfo | null> => {
    // TODO: 实现实际API调用
    // 这里是模拟实现
    // 模拟一些分组没有配置文件的情况
    if (groupId === 'group-2') {
      return null;
    }

    return {
      id: `group-config-${groupId}`,
      name: '分组级配置',
      type: ConfigType.GROUP,
      format: 'YAML',
      applicationId,
      groupId,
      content:
        'app:\n  name: AppName\n  version: 1.0.0\nserver:\n  port: 8081\n  contextPath: /api\n',
      description: '分组级配置文件',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      version: '1.0.0',
      isActive: true,
    };
  },

  /**
   * 获取有效配置文件 - 实现继承关系
   * 如果分组配置不存在，则返回应用配置
   * @param applicationId 应用ID
   * @param groupId 分组ID
   */
  getEffectiveConfig: async (
    applicationId: string,
    groupId: string,
  ): Promise<ConfigFileInfo | null> => {
    // 首先尝试获取分组配置
    const groupConfig = await ConfigService.getGroupConfig(
      applicationId,
      groupId,
    );

    // 如果存在分组配置，返回分组配置
    if (groupConfig) {
      return groupConfig;
    }

    // 否则返回应用配置
    return await ConfigService.getApplicationConfig(applicationId);
  },

  /**
   * 保存配置文件
   * @param config 配置文件信息
   */
  saveConfig: async (
    config: Omit<ConfigFileInfo, 'id' | 'createdAt' | 'updatedAt'>,
  ): Promise<ConfigFileInfo> => {
    // TODO: 实现实际API调用
    // 这里是模拟实现
    return {
      ...config,
      id: `config-${Date.now()}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
  },

  /**
   * 更新配置文件
   * @param configId 配置文件ID
   * @param config 配置文件更新内容
   */
  updateConfig: async (
    configId: string,
    config: Partial<ConfigFileInfo>,
  ): Promise<ConfigFileInfo> => {
    // TODO: 实现实际API调用
    // 这里是模拟实现
    return {
      ...(config as ConfigFileInfo),
      id: configId,
      updatedAt: new Date().toISOString(),
    };
  },

  /**
   * 删除配置文件
   * @param configId 配置文件ID
   */
  deleteConfig: async (configId: string): Promise<boolean> => {
    // TODO: 实现实际API调用
    // 这里是模拟实现
    return true;
  },
};

// 模型状态类型定义
interface ConfigModelState {
  applicationConfigs: Record<string, ConfigFileInfo>;
  groupConfigs: Record<string, ConfigFileInfo>;
}

// 全局状态模型
export default {
  namespace: 'config',
  state: {
    applicationConfigs: {},
    groupConfigs: {},
  } as ConfigModelState,
  effects: {
    *fetchEffectiveConfig(
      { payload }: { payload: { applicationId: string; groupId: string } },
      { call, put }: { call: Function; put: Function },
    ): Generator<any, ConfigFileInfo | null, any> {
      const { applicationId, groupId } = payload;
      const config = yield call(
        ConfigService.getEffectiveConfig,
        applicationId,
        groupId,
      );
      yield put({
        type: 'saveEffectiveConfig',
        payload: {
          applicationId,
          groupId,
          config,
        },
      });
      return config;
    },
  },
  reducers: {
    saveEffectiveConfig(
      state: ConfigModelState,
      {
        payload,
      }: {
        payload: {
          applicationId: string;
          groupId: string;
          config: ConfigFileInfo;
        };
      },
    ) {
      const { applicationId, groupId, config } = payload;
      if (config.type === ConfigType.APPLICATION) {
        return {
          ...state,
          applicationConfigs: {
            ...state.applicationConfigs,
            [applicationId]: config,
          },
        };
      } else {
        return {
          ...state,
          groupConfigs: {
            ...state.groupConfigs,
            [`${applicationId}:${groupId}`]: config,
          },
        };
      }
    },
  },
};
