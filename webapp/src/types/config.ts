export interface ValidationResult {
  valid: boolean;
  error: string | null;
  line?: number;
  column?: number;
}

export type ConfigFileFormat = 'properties' | 'yaml' | 'json' | 'xml' | 'shell' | 'text';

export type ConfigViewMode = 'view' | 'edit' | 'create';

export type GroupConfigFileViewType = 'groupOnly' | 'overridden' | 'inherited';

export interface GroupConfigFileView extends API.ConfigFile {
  viewType: GroupConfigFileViewType;
}

