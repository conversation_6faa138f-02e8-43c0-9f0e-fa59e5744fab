import { DeployStatus as DeployStatusEnum } from '@/pages/Deploy/types/common';

// 部署任务类型
export type DeployTaskType = 'deploy' | 'rollback' | 'build_deploy';

// 部署策略类型
export type DeployStrategyType = 'normal' | 'blue-green' | 'canary';

// 部署状态类型
export type DeployStatus = DeployStatusEnum;

// 部署计划批次项
export interface DeploymentPlanItem {
  batchId: number;
  groups: number[];
}

// 部署模式类型
export type DeployMode = 'sequential' | 'parallel';

// 部署任务操作视图（用于创建部署任务时的表单数据）
export interface DeployTaskOperationView {
  deployType: DeployTaskType;
  isBranch: boolean; // 是否是分支
  semver: string; // 分支名称
  commitId: string; // 提交ID
  selectedGroupIds: number[]; // 选中的分组ID
  description: string; // 描述
  rdmId: string; // RDM ID
  deploymentPlan?: DeploymentPlanItem[]; // 部署计划（批次和分组）
  deployMode?: DeployMode; // 部署模式（顺序或并行）
}

// 部署历史视图模型（用于展示部署历史）
export interface DeployHistoryView {
  id: string;
  semver: string;
  deployTime: string;
  deployType: '部署' | '编译部署' | '回滚部署';
  deployStrategy: '普通' | '蓝绿' | '金丝雀';
  operator: string;
  status: DeployStatus;
  duration: string;
  description: string;
  commitId: string;
  targetGroups: string[];
  groupName: string;
  config: {
    runtime: any;
  };
  logs: Array<DeployLogView>;
}

// 部署日志视图模型
export interface DeployLogView {
  type: 'info' | 'warning' | 'error' | 'success';
  content: string;
  timestamp: string;
}

// 部署历史组件属性接口（保持兼容性）
export interface DeployHistoryProps {
  deployHistory: DeployHistoryView[];
  deployHistoryVisible: boolean;
  deployHistoryFilter: string;
  deployHistoryStatusFilter: string;
  deployHistoryTypeFilter: string;
  deployHistoryStrategyFilter: string;
  deployHistoryGroupFilter: string;
  deployHistoryGroups: string[];
  onToggleVisible: () => void;
  onFilterChange: (value: string) => void;
  onStatusFilterChange: (value: string) => void;
  onTypeFilterChange: (value: string) => void;
  onStrategyFilterChange: (value: string) => void;
  onGroupFilterChange: (value: string) => void;
  onViewDetail: (history: DeployHistoryView) => void;
  onRedeploy: (history: DeployHistoryView) => void;
  onRollback: (history: DeployHistoryView) => void;
}

// API响应到视图模型的转换工具类型
export interface DeployTaskViewModelConverter {
  toDeployHistoryView(task: API.DeployTask): DeployHistoryView;
  toDeployLogView(log: API.DeployLog): DeployLogView;
}