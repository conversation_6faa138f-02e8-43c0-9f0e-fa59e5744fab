import { request } from 'umi';
import { 
  CloudServerOutlined, 
  BankOutlined,
  UsergroupAddOutlined,
  AppstoreOutlined,
  DatabaseOutlined,
  RocketOutlined
} from '@ant-design/icons';
import React from 'react';

// 获取用户服务树数据
export async function getUserSrvTree() {
  return request<API.StandardResponse<API.OpsNode[]>>('/api/srvTree/tree', {
    method: 'POST',
  });
}


// 根据环境ID和服务树节点ID获取应用及其分组信息
export async function getAppInfoByNodeIdAndEnvId(nodeId: number, envId: number) {
  const params: API.GetAppInfoRequest = {
    nodeId: Number(nodeId),
    envId: envId
  };
  
  return request<API.StandardResponse<API.AppData>>('/api/srvTree/appInfo', {
    method: 'POST',
    data: params,
  });
}

// 将后端OpsNode转换为前端TreeNode
export function convertOpsNodeToTreeNode(opsNode: API.OpsNode): any {
  let icon;
  let iconColor = '#1890ff'; // 默认颜色
  
  switch (opsNode.type) {
    case 'corp': // 公司
      icon = React.createElement(BankOutlined);
      iconColor = '#722ed1'; // 紫色
      break;
    case 'owt': // 部门
      icon = React.createElement(UsergroupAddOutlined);
      iconColor = '#13c2c2'; // 青色
      break;
    case 'pdl': // 业务线
      icon = React.createElement(AppstoreOutlined);
      iconColor = '#1890ff'; // 蓝色
      break;
    case 'sg': // 服务组
      icon = React.createElement(DatabaseOutlined);
      iconColor = '#52c41a'; // 绿色
      break;
    case 'srv': // 服务
      icon = React.createElement(RocketOutlined);
      iconColor = '#fa8c16'; // 橙色
      break;
    default:
      icon = React.createElement(CloudServerOutlined);
      iconColor = '#8c8c8c'; // 灰色
  }

  // 为图标添加颜色样式
  const styledIcon = React.cloneElement(icon, {
    style: { color: iconColor, fontSize: '14px' }
  });

  let children = undefined;
  const hasChildren = !!(opsNode.children && opsNode.children.length);
  const shouldShowExpander = hasChildren ;
  
  if (hasChildren) {
    children = opsNode.children!.map(child => convertOpsNodeToTreeNode(child));
  } else if (shouldShowExpander) {
    children = [];
  }

  const treeNode = {
    title: opsNode.label,
    key:  String(opsNode.id),
    icon: styledIcon,
    children: children,
    nodeType: opsNode.type,
    isLeaf: !shouldShowExpander, 
    nodeData: opsNode, // 保存原始节点数据
  };

  return treeNode;
}

// 将服务树数据转换为前端需要的格式
export function transformSrvTreeData(opsNodes: API.OpsNode[]) {
  if (!opsNodes || opsNodes.length === 0) {
    console.log('服务树数据为空');
    return [];
  }

  console.log('原始服务树数据:', opsNodes); 
  const transformedData = opsNodes.map(node => convertOpsNodeToTreeNode(node));
  console.log('转换后的服务树数据:', transformedData); 
  
  return transformedData;
}


// 获取第一层和第二层节点的key，用于只展开前两层
export function getFirstTwoLevelKeys(treeData: any[]): string[] {
  if (!treeData || treeData.length === 0) {
    return [];
  }
  
  let keys: string[] = [];
  
  // 收集第一层节点的key
  for (const node of treeData) {
    if (node.key) {
      keys.push(node.key);
    }
    
    // 收集第二层节点的key
    if (node.children && node.children.length > 0) {
      for (const childNode of node.children) {
        if (childNode.key) {
          keys.push(childNode.key);
        }
      }
    }
  }
  
  return keys;
} 