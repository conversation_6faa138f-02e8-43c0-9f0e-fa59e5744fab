import { request } from 'umi';

// 分组相关的 API 接口

/**
 * 分页获取分组列表
 */
export async function getGroupList(params: {
  app_id: number;
  env_id: number;
  code?: string;
  name?: string;
}) {
  
  // 修复类型错误，使用 API.Group[] 替代不存在的 API.ListGroupsResponse
  return request<API.StandardResponse<API.Group[]>>('/api/group/list', {
    method: 'GET',
    params,
  });
}

/**
 * 获取分组详情
 */
export async function getGroupDetail(id: number) {
  return request<API.StandardResponse<API.Group>>(`/api/groups/${id}`, {
    method: 'GET',
  });
}

/**
 * 创建分组
 */
export async function createGroup(data: API.CreateGroupRequest) {
  return request<API.StandardResponse<API.Group>>('/api/group/create', {
    method: 'POST',
    data,
  });
}

/**
 * 更新分组
 */
export async function updateGroup(id: number, data: API.UpdateGroupRequest) {
  return request<API.StandardResponse<API.Group>>(`/api/groups/${id}`, {
    method: 'PUT',
    data,
  });
}

/**
 * 删除分组
 */
export async function deleteGroup(id: number) {
  return request<API.StandardResponse<Record<string, any>>>(`/api/groups/${id}`, {
    method: 'DELETE',
  });
}

/**
 * 设置默认分组
 */
export async function setDefaultGroup(id: number) {
  return request<API.StandardResponse<Record<string, any>>>(`/api/groups/${id}/default`, {
    method: 'PUT',
  });
} 