import { request } from "@umijs/max";

/**
 * 根据应用ID和环境ID获取探针配置
 */
export async function getProbesByAppAndEnvID(appId: number, envId: number) {
  return request<API.StandardResponse<API.RuntimeProbe[]>>(`/api/runtime/probes`, {
    method: 'GET',
    params: {
      app_id: appId,
      env_id: envId,
    },
  });
}

/**
 * 创建或更新探针配置
 */
export async function saveProbeConfig(probe: Partial<API.RuntimeProbe>) {
  return request<API.StandardResponse<API.RuntimeProbe>>(`/api/runtime/probes`, {
    method: 'POST',
    data: probe,
  });
}

/**
 * 删除探针配置
 */
export async function deleteProbeConfig(id: number) {
  return request<API.StandardResponse<any>>(`/api/runtime/probes/${id}`, {
    method: 'DELETE',
  });
} 