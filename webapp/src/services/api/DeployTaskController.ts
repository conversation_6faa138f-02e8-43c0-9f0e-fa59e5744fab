import { request } from '@umijs/max';

/**
 * 创建部署任务
 * @param params 部署任务参数
 * @returns
 */ 
export async function createDeployTask(params: API.CreateDeployTaskRequest) : Promise<API.StandardResponse<API.DeployTask>> {
  return request('/api/deploytask/create', {
    method: 'POST',
    data: params,
  });
}

/**
 * 获取部署任务列表
 * @param appId 应用ID
 * @param envId 环境ID
 * @returns 部署任务列表
 */
export async function getDeployTasks(appId: number, envId: number) : Promise<API.StandardResponse<API.DeployTask[]>> {
  return request(`/api/deploytask/list`, {
    method: 'GET',
    params: { app_id: appId, env_id: envId }
  });
}

/**
 * 分页查询部署任务列表（支持过滤）
 * @param params 查询参数
 * @returns 分页部署任务列表
 */
export async function queryDeployTasks(params: API.QueryDeployTasksParams) : Promise<API.StandardResponse<API.DeployTasksResponse>> {
  return request(`/api/deploytask/query`, {
    method: 'GET',
    params: params
  });
}

/**
 * 获取部署任务详情
 * @param taskId 部署任务ID
 * @returns 部署任务详情
 */
export async function getDeployTaskDetail(taskId: number) : Promise<API.StandardResponse<API.DeployTask>> {
  return request(`/api/deploytask/detail/${taskId}`, {
    method: 'GET'
  });
}

/**
 * 获取部署历史列表
 * @param params 查询参数
 * @returns 部署历史列表
 */
export async function getDeployHistoryList(params: {
  app_id?: number;
  env_id?: number;
  page?: number;
  page_size?: number;
  version?: string;
  operator?: string;
  status?: string;
  start_time_from?: number;
  start_time_to?: number;
}) : Promise<API.StandardResponse<API.DeployHistoryResponse>> {
  return request(`/api/deploytask/history`, {
    method: 'GET',
    params: params
  });
}
