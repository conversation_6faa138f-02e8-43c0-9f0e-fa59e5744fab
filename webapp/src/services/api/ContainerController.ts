import { request } from 'umi';

// 容器配置相关的 API 接口

/**
 * 获取应用的容器配置
 * @param params 请求参数
 */
export async function getContainerConfig(params: {
  app_id: number;
  env_id: number;
}) {
  return request<API.StandardResponse<API.Container>>('/api/container/config', {
    method: 'GET',
    params: params,
  });
}

/**
 * 创建容器配置
 * @param container 容器配置对象
 */
export async function createContainerConfig(container: Omit<API.Container, 'id' | 'c_t' | 'create_by' | 'u_t' | 'update_by' | 'is_deleted'>) {
  return request<API.StandardResponse<API.Container>>('/api/container/config', {
    method: 'POST',
    data: container,
  });
}

/**
 * 更新容器配置
 * @param container 容器配置对象
 */
export async function updateContainerConfig(container: Partial<API.Container> & { 
  app_id: number; 
  env_id: number; 
}) {
  return request<API.StandardResponse<API.Container>>('/api/container/config', {
    method: 'PUT',
    data: container,
  });
}

/**
 * 删除容器配置
 * @param params 删除参数
 */
export async function deleteContainerConfig(params: {
  app_id: number;
  env_id: number;
}) {
  return request<API.StandardResponse<boolean>>('/api/container/config', {
    method: 'DELETE',
    params: params,
  });
}

/**
 * 获取容器配置列表
 * @param params 查询参数
 */
export async function getContainerConfigList(params: {
  app_id: number;
  env_id: number;
}) {
  return request<API.StandardResponse<API.Container[]>>('/api/container/config/list', {
    method: 'GET',
    params: params,
  });
}

/**
 * 验证容器配置
 * @param container 容器配置对象
 */
export async function validateContainerConfig(container: Partial<API.Container>) {
  return request<API.StandardResponse<{
    valid: boolean;
    errors: string[];
    warnings: string[];
  }>>('/api/container/config/validate', {
    method: 'POST',
    data: container,
  });
}

/**
 * 预览容器配置YAML
 * @param container 容器配置对象
 */
export async function previewContainerYaml(container: Partial<API.Container>) {
  return request<API.StandardResponse<{
    deployment_yaml: string;
    service_yaml: string;
  }>>('/api/container/config/preview', {
    method: 'POST',
    data: container,
  });
} 