import { request } from 'umi';

// build配置相关的 API 接口

/**
 * 获取构建模板列表
 * @param language 编程语言（可选）
 */
export async function getBuildTemplates(language?: string) {
  return request('/api/build/templates', {
    method: 'GET',
    params: language ? { language } : {},
  });
}

/**
 * 根据ID获取构建模板
 * @param id 模板ID
 */
export async function getBuildTemplateById(id: number) {
  return request(`/api/build/templates/${id}`, {
    method: 'GET',
  });
}

/**
 * 获取默认构建模板
 * @param language 编程语言
 */
export async function getDefaultTemplate(language: string) {
  return request('/api/build/templates/default', {
    method: 'GET',
    params: { language },
  });
}

/**
 * 获取项目build配置
 * @param params 请求参数
 */
export async function getProjectBuildConfig(params: {
  app_id: number;
  env_id: number;
}) {
  return request('/api/build/config', {
    method: 'GET',
    params: params,
  });
}

/**
 * 创建项目build配置
 * @param config 构建配置
 */
export async function createProjectBuildConfig(config: {
  app_id: number;
  env_id: number;
  template_id: number;
  language: string;
  template_type: string;
  build_params?: any;
  runtime_params?: any;
  container_params?: any;
  custom_build_script?: string;
  pre_build_script?: string;
  post_build_script?: string;
}) {
  return request('/api/build/config/create', {
    method: 'POST',
    data: config,
  });
}

/**
 * 更新项目build配置
 * @param config 更新的配置（包含app_id和env_id）
 */
export async function updateProjectBuildConfig(config: {
  app_id: number;
  env_id: number;
  build_params?: any;
  runtime_params?: any;
  container_params?: any;
  custom_build_script?: string;
  pre_build_script?: string;
  post_build_script?: string;
}) {
  return request('/api/build/config/update', {
    method: 'POST',
    data: config,
  });
}

/**
 * 删除项目build配置
 * @param params 删除参数
 */
export async function deleteProjectBuildConfig(params: {
  app_id: number;
  env_id: number;
}) {
  return request('/api/build/config/delete', {
    method: 'POST',
    data: params,
  });
}

/**
 * 预览build配置
 * @param config 预览配置
 */
export async function previewBuildConfig(config: {
  language: string;
  template_type: string;
  build_params?: any;
  runtime_params?: any;
  container_params?: any;
}) {
  return request('/api/build/config/preview', {
    method: 'POST',
    data: config,
  });
} 