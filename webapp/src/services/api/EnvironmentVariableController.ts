import { request } from '@umijs/max';


export async function getByAppAndEnvID(appId: number, envId: number) : Promise<API.StandardResponse<API.EnvironmentVariable[]>> {
  return request('/api/environment_variable/list', {
    method: 'GET',
    params: { app_id: appId, env_id: envId },
  });
}

export async function create(params: API.CreateEnvironmentVariableRequest) : Promise<API.StandardResponse<API.EnvironmentVariable>> {
  return request('/api/environment_variable/create', {
    method: 'POST',
    data: params,
  });
}

export async function update(params: API.UpdateEnvironmentVariableRequest) : Promise<API.StandardResponse<API.EnvironmentVariable>> {
  return request('/api/environment_variable/update', {
    method: 'POST',
    data: params,
  });
}