import { request } from 'umi';

/**
 * 获取版本号列表
 * @param isBranch 是否是分支
 * @param projectId 项目ID
 * @param search 搜索关键词
 * @param page 页码
 * @param perPage 每页条数
 */
export async function getSemverList(isBranch: number, projectId: number, search: string, page: number, perPage: number) {
  return request<API.StandardResponse<API.StandardResponse<API.GitLabBranch[] | API.GitLabTag[]>>>('/api/gitlab/semvers', {
    method: 'GET',
    params: { is_branch: isBranch, project_id: projectId, search, page, per_page: perPage }
  });
}
