/* eslint-disable */
// 镜像和组件管理API
import { request } from '@umijs/max';

/** 查询镜像列表 GET /api/v1/images */
export async function queryImageList(
  params: {
    // query
    /** 关键词 */
    keyword?: string;
    /** 当前页 */
    page?: number;
    /** 页大小 */
    limit?: number;
  },
  options?: { [key: string]: any },
) {
  return request<API.Result_PageInfo_ImageInfo__>('/api/images/list', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 添加镜像 POST /api/v1/image */
export async function addImage(
  body?: API.ImageInfoVO,
  options?: { [key: string]: any },
) {
  return request<API.Result_ImageInfo_>('/api/v1/image', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取镜像详情 GET /api/v1/image/${param0} */
export async function getImageDetail(
  params: {
    // path
    /** imageId */
    imageId?: string;
  },
  options?: { [key: string]: any },
) {
  const { imageId: param0 } = params;
  return request<API.Result_ImageInfo_>(`/api/v1/image/${param0}`, {
    method: 'GET',
    params: { ...params },
    ...(options || {}),
  });
}

/** 修改镜像 PUT /api/v1/image/${param0} */
export async function modifyImage(
  params: {
    // path
    /** imageId */
    imageId?: string;
  },
  body?: API.ImageInfoVO,
  options?: { [key: string]: any },
) {
  const { imageId: param0 } = params;
  return request<API.Result_ImageInfo_>(`/api/images/update/${param0}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { },
    data: body,
    ...(options || {}),
  });
}

/** 删除镜像 DELETE /api/v1/image/${param0} */
export async function deleteImage(
  params: {
    // path
    /** imageId */
    imageId?: string;
  },
  options?: { [key: string]: any },
) {
  const { imageId: param0 } = params;
  return request<API.Result_string_>(`/api/images/del/${param0}`, {
    method: 'DELETE',
    params: {  },
    ...(options || {}),
  });
}

/** 查询组件列表 GET /api/v1/components */
export async function queryComponentList(
  params: {
    // query
    /** 关键词 */
    keyword?: string;
    /** 当前页 */
    current?: number;
    /** 页大小 */
    pageSize?: number;
  },
  options?: { [key: string]: any },
) {
  return request<API.Result_PageInfo_ComponentInfo__>('/api/v1/components', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 添加组件 POST /api/v1/component */
export async function addComponent(
  body?: API.ComponentInfoVO,
  options?: { [key: string]: any },
) {
  return request<API.Result_ComponentInfo_>('/api/v1/component', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取组件详情 GET /api/v1/component/${param0} */
export async function getComponentDetail(
  params: {
    // path
    /** componentId */
    componentId?: string;
  },
  options?: { [key: string]: any },
) {
  const { componentId: param0 } = params;
  return request<API.Result_ComponentInfo_>(`/api/v1/component/${param0}`, {
    method: 'GET',
    params: { ...params },
    ...(options || {}),
  });
}

/** 修改组件 PUT /api/v1/component/${param0} */
export async function modifyComponent(
  params: {
    // path
    /** componentId */
    componentId?: string;
  },
  body?: API.ComponentInfoVO,
  options?: { [key: string]: any },
) {
  const { componentId: param0 } = params;
  return request<API.Result_ComponentInfo_>(`/api/v1/component/${param0}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...params },
    data: body,
    ...(options || {}),
  });
}

/** 删除组件 DELETE /api/v1/component/${param0} */
export async function deleteComponent(
  params: {
    // path
    /** componentId */
    componentId?: string;
  },
  options?: { [key: string]: any },
) {
  const { componentId: param0 } = params;
  return request<API.Result_string_>(`/api/v1/component/${param0}`, {
    method: 'DELETE',
    params: { ...params },
    ...(options || {}),
  });
}
