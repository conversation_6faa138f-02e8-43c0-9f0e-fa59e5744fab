/* eslint-disable */
import { request } from '@umijs/max';

// ================================
// Pod 基础操作 API
// ================================

/** 获取Pod列表 GET /api/pods/list */
export async function getPodList(
  params: {
    namespace: string;
  },
  options?: { [key: string]: any },
) {
  return request<API.Result_PodListResponse_>('/api/pods/list', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取Pod详情 GET /api/pods/detail/${namespace}/${podName} */
export async function getPodDetail(
  params: {
    namespace: string;
    podName: string;
  },
  options?: { [key: string]: any },
) {
  const { namespace, podName } = params;
  return request<API.Result_Pod_>(`/api/pods/detail/${encodeURIComponent(namespace)}/${encodeURIComponent(podName)}`, {
    method: 'GET',
    ...(options || {}),
  });
}

// ================================
// Pod 重启操作 API
// ================================

/** 重启Pod POST /api/pods/restart */
export async function restartPods(
  body: API.RestartRequest,
  options?: { [key: string]: any },
) {
  return request<API.Result_RestartResult_>('/api/pods/restart', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

// ================================
// Arthas 操作 API
// ================================

/** 启动Arthas POST /api/pods/arthas/start */
export async function startArthas(
  body: API.ArthasStartRequest,
  options?: { [key: string]: any },
) {
  return request<API.Result_ArthasStatusResponse_>('/api/pods/arthas/start', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 停止Arthas POST /api/pods/arthas/stop/${namespace}/${podName} */
export async function stopArthas(
  params: {
    namespace: string;
    podName: string;
  },
  options?: { [key: string]: any },
) {
  const { namespace, podName } = params;
  return request<API.Result_string_>(`/api/pods/arthas/stop/${encodeURIComponent(namespace)}/${encodeURIComponent(podName)}`, {
    method: 'POST',
    ...(options || {}),
  });
}

/** 获取Arthas状态 GET /api/pods/arthas/status/${namespace}/${podName} */
export async function getArthasStatus(
  params: {
    namespace: string;
    podName: string;
  },
  options?: { [key: string]: any },
) {
  const { namespace, podName } = params;
  return request<API.Result_ArthasStatusResponse_>(`/api/pods/arthas/status/${encodeURIComponent(namespace)}/${encodeURIComponent(podName)}`, {
    method: 'GET',
    ...(options || {}),
  });
}

// ================================
// 终端操作 API  
// ================================

/** 启动终端会话 POST /api/pods/terminal/start */
export async function startTerminal(
  body: {
    namespace: string;
    pod_name: string;
    container_name?: string;
    shell?: string;
    cols?: number;
    rows?: number;
    timeout?: number;
  },
  options?: { [key: string]: any },
) {
  return request<API.Result_string_>('/api/pods/terminal/start', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 停止终端会话 POST /api/pods/terminal/stop/${namespace}/${podName}/${sessionId} */
export async function stopTerminal(
  params: {
    namespace: string;
    podName: string;
    sessionId: string;
  },
  options?: { [key: string]: any },
) {
  const { namespace, podName, sessionId } = params;
  return request<API.Result_string_>(`/api/pods/terminal/stop/${encodeURIComponent(namespace)}/${encodeURIComponent(podName)}/${encodeURIComponent(sessionId)}`, {
    method: 'POST',
    ...(options || {}),
  });
}

// ================================
// 便捷方法 - 组合操作
// ================================

/** 重启单个Pod */
export async function restartSinglePod(
  namespace: string,
  podName: string,
  strategy: 'graceful' | 'force' = 'graceful',
  options?: { [key: string]: any },
) {
  return restartPods({
    namespace,
    pod_names: [podName],
    strategy,
  }, options);
}

/** 重启所有Pod */
export async function restartAllPods(
  namespace: string,
  strategy: 'graceful' | 'force' = 'graceful',
  options?: { [key: string]: any },
) {
  return restartPods({
    namespace,
    pod_names: [], // 空数组表示所有Pod
    strategy,
  }, options);
} 