/* eslint-disable */
// 该文件由 OneAPI 自动生成，请勿手动修改！

import * as UserController from './UserController';
import * as ImageController from './ImageController';
import * as SrvTreeController from './SrvTreeController';
import * as ConfigController from './ConfigController';
import * as DeployTaskController from './DeployTaskController';
import * as SemverController from './SemverController';
import * as PodController from './PodController';
import * as ContainerController from './ContainerController';

export default {
  UserController,
  ImageController,
  SrvTreeController,
  ConfigController,
  DeployTaskController,
  SemverController,
  PodController,
  ContainerController,
};

export {
  UserController,
  ImageController,
  SrvTreeController,
  ConfigController,
  DeployTaskController,
  Semver<PERSON>ontroller,
  PodController,
  ContainerController,
};

// 直接导出SrvTreeController中的方法
export const {
  getUserSrvTree,
  convertOpsNodeToTreeNode,
  getFirstTwoLevelKeys,
} = SrvTreeController;

export const {
  getConfigFileList,
  getConfigFileContent,
  createConfigFile,
} = ConfigController;

export const {
  createDeployTask,
} = DeployTaskController;

export const {
  getSemverList,
} = SemverController;

export const {
  getPodList,
  getPodDetail,
  restartPods,
  startArthas,
  stopArthas,
  getArthasStatus,
} = PodController;

export const {
  getContainerConfig,
  createContainerConfig,
  updateContainerConfig,
  deleteContainerConfig,
  getContainerConfigList,
  validateContainerConfig,
  previewContainerYaml,
} = ContainerController;

