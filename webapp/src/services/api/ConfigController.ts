import { request } from 'umi';

// 配置文件相关的 API 接口

/**
 * 获取应用的配置文件列表
 * @param appId 应用ID
 * @param envId 环境ID
 */
export async function getConfigFileList(params: {
  app_id: number;
  env_id: number;
}) {
  return request('/api/config/getFileList', {
    method: 'POST',
    data: params,
  });
}


/**
 * 获取配置文件内容
 * @param fileId 配置文件ID
 */
export async function getConfigFileContent(fileId: number) {
  return request<API.StandardResponse<API.ConfigFileContent>>('/api/configfile/getContent', {
    method: 'POST',
    data: { fileId: fileId }
  });
}

/**
 * 创建配置文件
 * @param file 配置文件对象
 */
export async function createConfigFile(configFile: Config.CreateConfigFileRequest) {
  return request<API.StandardResponse<API.ConfigFile>>('/api/configfile/create', {
    method: 'POST',
    data: configFile
  });
}

/**
 * 更新配置文件
 * @param file 配置文件对象
 */
export async function updateConfigFile(file: API.ConfigFile) {
  return request('/api/configfile/update', {
    method: 'POST',
    data: file
  });
} 
