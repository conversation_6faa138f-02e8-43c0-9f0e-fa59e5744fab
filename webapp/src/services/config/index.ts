import { message } from 'antd';
import { getConfigFileList, getConfigFileContent, createConfigFile } from '@/services/api';

/**
 * 配置文件服务类
 * 提供获取、保存和管理配置文件的方法
 */
export class ConfigService {
  /**
   * 获取配置文件列表
   * @param appId 应用ID
   * @param envId 环境ID
   */
  static async getConfigFiles(appId: number, envId: number) {
    try {
      const response = await getConfigFileList({
        app_id: appId,
        env_id: envId
      });
      
      if (response?.success) {
        return response.data || [];
      } else {
        message.error(response?.errorMessage || '获取配置文件列表失败');
        return [];
      }
    } catch (error) {
      console.error('获取配置文件列表错误:', error);
      message.error('获取配置文件列表失败');
      return [];
    }
  }

  /**
   * 获取配置文件内容
   * @param fileId 配置文件ID
   */
  static async getConfigFileContent(fileId: number) {
    try {
      const response = await getConfigFileContent(fileId);
      
      if (response?.data?.content) {
        return response.data;
      } else {
        message.error('获取文件内容失败');
        return null;
      }
    } catch (error) {
      console.error('获取配置文件内容错误:', error);
      message.error('获取文件内容失败');
      return null;
    }
  }

  /**
   * 创建新的配置文件
   * @param file 配置文件对象
   */
  static async createConfigFile(configFile: Config.CreateConfigFileRequest) {
    try {
      const response = await createConfigFile(configFile);
      
      if (response?.code === 0) {
        message.success('配置文件创建成功');
        return response.data;
      } else {
        // 提取具体错误信息
        const errorMsg = response?.message || '创建配置文件失败';
        message.error(errorMsg);
        return null;
      }
    } catch (error) {
      // 提取并显示更具体的错误消息
      const errorMsg = error instanceof Error ? 
        `创建配置文件失败: ${error.message}` : 
        '创建配置文件失败';
      message.error(errorMsg);
      return null;
    }
  }

  /**
   * 更新配置文件
   * @param file 配置文件对象
   */
  static async updateConfigFile(file: API.ConfigFile) {

  }

  /**
   * 筛选应用级配置文件
   * @param configFiles 配置文件列表
   */
  static getApplicationConfigFiles(configFiles: API.ConfigFile[]) {
    if (!configFiles || configFiles.length === 0) return [];
    return configFiles.filter(file => !file.group_id);
  }

  /**
   * 筛选分组级配置文件
   * @param configFiles 配置文件列表
   * @param groupId 分组ID
   */
  static getGroupConfigFiles(configFiles: API.ConfigFile[], groupId: number) {
    if (!configFiles || configFiles.length === 0) return [];
    return configFiles.filter(file => file.group_id === groupId);
  }

  /**
   * 创建新的配置文件对象
   * @param appId 应用ID
   * @param envId 环境ID
   * @param groupId 分组ID (0表示应用级配置)
   * @param format 文件格式
   */
  static createNewConfigFile(appId: number, envId: number, groupId: number = 0, format: string = 'properties') {
    const isGroupLevel = groupId !== 0;
    
    const defaultContent = ConfigService.getDefaultContentByFormat(format);
    
    const newFile: API.ConfigFile = {
      id: 0, // 新建文件的临时ID
      app_id: appId,
      env_id: envId,
      group_id: groupId,
      name: '',
      path: '',
      format,
      description: '',
      content_payload: defaultContent,
    };
    
    // 为显示添加额外字段
    return {
      ...newFile,
      sourceType: isGroupLevel ? 'groupOnly' : 'inherited',
      isInherited: !isGroupLevel,
      description: isGroupLevel ? '分组专属配置' : '继承自全局配置',
    };
  }

  /**
   * 根据文件格式获取默认内容
   * @param format 文件格式
   */
  static getDefaultContentByFormat(format: string): API.ConfigFileContent {
    let contentStr = '';
    
    switch (format) {
      case 'properties':
        contentStr = '# 应用配置文件\n# 格式: key=value\n\napp.name=my-application\napp.version=1.0.0\nserver.port=8080\n';
        break;
      case 'yaml':
        contentStr = '# 应用配置文件\n# YAML 格式\n\napp:\n  name: my-application\n  version: 1.0.0\nserver:\n  port: 8080\n';
        break;
      case 'json':
        contentStr = '{\n  "app": {\n    "name": "my-application",\n    "version": "1.0.0"\n  },\n  "server": {\n    "port": 8080\n  }\n}';
        break;
      case 'xml':
        contentStr = '<?xml version="1.0" encoding="UTF-8"?>\n<configuration>\n  <app>\n    <n>my-application</n>\n    <version>1.0.0</version>\n  </app>\n  <server>\n    <port>8080</port>\n  </server>\n</configuration>';
        break;
      case 'shell':
        contentStr = '#!/bin/bash\n# 配置脚本\n\nexport APP_NAME="my-application"\nexport APP_VERSION="1.0.0"\nexport SERVER_PORT="8080"\n\necho "配置已加载"\n';
        break;
      case 'text':
      default:
        contentStr = '# 请输入配置文件内容\n';
    }
    
    // 创建符合 API.ConfigFileContent 类型的对象
    return {
      id: 0,
      file_id: 0,
      content: contentStr,
      version: 1,
      md5: '',
      size: contentStr.length,
      c_t: Date.now(),
      create_by: 0,
      remark: '新建配置文件',
      u_t: Date.now(),
      update_by: 0,
      status: 1
    };
  }
}

export default ConfigService; 