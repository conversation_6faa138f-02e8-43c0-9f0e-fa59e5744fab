package dto

import (
	"ci-gateway/pkg/restart"
)

// PodRestartRequest Pod重启请求
type PodRestartRequest struct {
	Namespace string                  `json:"namespace" binding:"required"`
	PodNames  []string                `json:"pod_names"` // 空数组表示重启所有Pod
	Strategy  restart.RestartStrategy `json:"strategy"`
	Config    *restart.RestartConfig  `json:"config,omitempty"`
}

// PodTerminalStartRequest 启动终端会话请求
type PodTerminalStartRequest struct {
	Namespace     string `json:"namespace" binding:"required"`
	PodName       string `json:"pod_name" binding:"required"`
	ContainerName string `json:"container_name"`
	Shell         string `json:"shell"`
	Cols          uint16 `json:"cols"`
	Rows          uint16 `json:"rows"`
	Timeout       int    `json:"timeout"`
}

// PodTerminalResizeRequest 调整终端大小请求
type PodTerminalResizeRequest struct {
	Namespace string `json:"namespace" binding:"required"`
	PodName   string `json:"pod_name" binding:"required"`
	SessionID string `json:"session_id" binding:"required"`
	Cols      uint16 `json:"cols"`
	Rows      uint16 `json:"rows"`
}

// PodHeapDumpRequest Java堆内存Dump请求
type PodHeapDumpRequest struct {
	Namespace     string `json:"namespace" binding:"required"`
	PodName       string `json:"pod_name" binding:"required"`
	ContainerName string `json:"container_name"`
	DumpPath      string `json:"dump_path"`
	AutoDownload  bool   `json:"auto_download"`
}

// PodArthasRequest Arthas相关请求
type PodArthasRequest struct {
	Namespace     string `json:"namespace" binding:"required"`
	PodName       string `json:"pod_name" binding:"required"`
	ContainerName string `json:"container_name"`
	Port          int    `json:"port,omitempty"`
}

// PodListRequest Pod列表查询请求
type PodListRequest struct {
	Namespace     string `json:"namespace" binding:"required"`
	LabelSelector string `json:"label_selector,omitempty"`
	AppID         int    `json:"app_id,omitempty"`
	EnvID         int    `json:"env_id,omitempty"`
}

// PodMetricsRequest Pod指标查询请求
type PodMetricsRequest struct {
	Namespace string `json:"namespace" binding:"required"`
	PodName   string `json:"pod_name" binding:"required"`
}

// ApiResponse 统一API响应格式
type ApiResponse struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// NewSuccessResponse 创建成功响应
func NewSuccessResponse(data interface{}) *ApiResponse {
	return &ApiResponse{
		Code:    0,
		Message: "success",
		Data:    data,
	}
}

// NewErrorResponse 创建错误响应
func NewErrorResponse(code int, message string) *ApiResponse {
	return &ApiResponse{
		Code:    code,
		Message: message,
	}
}

// NewErrorResponseWithData 创建带数据的错误响应
func NewErrorResponseWithData(code int, message string, data interface{}) *ApiResponse {
	return &ApiResponse{
		Code:    code,
		Message: message,
		Data:    data,
	}
}
