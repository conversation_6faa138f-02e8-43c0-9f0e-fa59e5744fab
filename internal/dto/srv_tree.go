package dto

// GetSrvTreeRequest 获取服务树请求结构体
type GetSrvTreeRequest struct {
	UserCode      string `json:"userCode" binding:"required"`
	Keyword       string `json:"keyword"`
	Schema        string `json:"schema"`
	IncludeBuffer bool   `json:"includeBuffer"`
	CurrentNodeId *int64 `json:"currentNodeId"`
}

// GetSrvTreeByIDRequest 根据ID获取服务树节点请求结构体
type GetSrvTreeByIDRequest struct {
	ID int64 `json:"id" binding:"required"`
}

// GetSrvTreeChildrenRequest 获取服务树子节点请求结构体
type GetSrvTreeChildrenRequest struct {
	ParentID int64 `json:"parentId" binding:"required"`
}

// OpsNode 服务树节点结构体
type OpsNode struct {
	ID       int64      `json:"id"`
	Label    string     `json:"label"`
	Type     string     `json:"type"`
	Children []*OpsNode `json:"children,omitempty"`
	IP       string     `json:"ip,omitempty"`
	Port     int        `json:"port,omitempty"`
}

// OpsNodesResult Ops服务返回的结果结构体
type OpsNodesResult struct {
	Status  int        `json:"status"`
	Message string     `json:"message"`
	Result  []*OpsNode `json:"result"`
}

// AppConfig 应用配置结构体
type AppConfig struct {
	Runtime    RuntimeConfig `json:"runtime"`
	ConfigMode string        `json:"configMode"`
}

// RuntimeConfig 运行时配置
type RuntimeConfig struct {
	Replicas      int    `json:"replicas"`
	CPU           string `json:"cpu"`
	Memory        string `json:"memory"`
	BaseImage     string `json:"baseImage,omitempty"`
	Namespace     string `json:"namespace,omitempty"`
	RequestCpu    string `json:"requestCpu,omitempty"`
	RequestMemory string `json:"requestMemory,omitempty"`
	CpuLimit      string `json:"cpuLimit,omitempty"`
	Timeout       int    `json:"timeout,omitempty"`
}

// Group 应用分组结构体
type Group struct {
	ID           string    `json:"id"`
	Title        string    `json:"title"`
	Description  string    `json:"description"`
	Status       string    `json:"status"`
	Tag          string    `json:"tag"`
	ConfigMode   string    `json:"configMode"`
	DeployStatus string    `json:"deployStatus"`
	Config       AppConfig `json:"config"`
}

// AppInfo 应用信息结构体
type AppInfo struct {
	ID            string    `json:"id"`
	Name          string    `json:"name"`
	Icon          string    `json:"icon"`
	Groups        []Group   `json:"groups"`
	DefaultConfig AppConfig `json:"defaultConfig"`
}

// AppInfoResult 获取应用信息结果
type AppInfoResult struct {
	Status  int      `json:"status"`
	Message string   `json:"message"`
	Result  *AppInfo `json:"result"`
}

// GetAppInfoByNodeIDRequest 根据服务树节点ID获取应用信息的请求结构体
type GetAppInfoByNodeIDRequest struct {
	NodeID int64 `json:"nodeId" binding:"required"`
	EnvID  int64 `json:"envId" binding:"required"`
}
