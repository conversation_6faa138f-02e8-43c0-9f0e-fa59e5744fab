package dto

type CreateEnvironmentVariableRequest struct {
	AppID        int64  `json:"app_id" binding:"required"`
	EnvID        int64  `json:"env_id" binding:"required"`
	KeyName      string `json:"key_name" binding:"required"`
	Value        string `json:"value" binding:"required"`
	Description  string `json:"description"`
	VariableType int8   `json:"variable_type" min:"0" max:"1"`
	Dependencies string `json:"dependencies"`
}

type UpdateEnvironmentVariableRequest struct {
	ID           int64  `json:"id" binding:"required"`
	KeyName      string `json:"key_name" binding:"required"`
	Value        string `json:"value" binding:"required"`
	Description  string `json:"description"`
	VariableType int8   `json:"variable_type" min:"0" max:"1"`
	Dependencies string `json:"dependencies"`
	Status       int8   `json:"status" min:"0" max:"1"`
}
