package dto

import "encoding/json"

// BuildTemplateRequest 构建模板请求
type BuildTemplateRequest struct {
	Language string `json:"language" form:"language"`
	Status   *int8  `json:"status" form:"status"`
}

// BuildTemplateResponse 构建模板响应
type BuildTemplateResponse struct {
	ID                   int64           `json:"id"`
	Name                 string          `json:"name"`
	Language             string          `json:"language"`
	TemplateType         string          `json:"template_type"`
	DisplayName          string          `json:"display_name"`
	Description          string          `json:"description"`
	Icon                 string          `json:"icon"`
	WorkflowTemplateName string          `json:"workflow_template_name"`
	DefaultParams        json.RawMessage `json:"default_params"`
	ParamSchema          json.RawMessage `json:"param_schema"`
	IsDefault            int8            `json:"is_default"`
	SortOrder            int             `json:"sort_order"`
	Status               int8            `json:"status"`
}

// ProjectBuildConfigRequest 项目构建配置请求
type ProjectBuildConfigRequest struct {
	AppID int64 `json:"app_id" binding:"required"`
	EnvID int64 `json:"env_id" binding:"required"`
}

// CreateProjectBuildConfigRequest 创建项目构建配置请求
type CreateProjectBuildConfigRequest struct {
	AppID             int64           `json:"app_id" binding:"required"`
	EnvID             int64           `json:"env_id" binding:"required"`
	TemplateID        int64           `json:"template_id" binding:"required"`
	Language          string          `json:"language" binding:"required"`
	TemplateType      string          `json:"template_type" binding:"required"`
	BuildParams       json.RawMessage `json:"build_params"`
	RuntimeParams     json.RawMessage `json:"runtime_params"`
	CustomBuildScript string          `json:"custom_build_script"`
	PreBuildScript    string          `json:"pre_build_script"`
	PostBuildScript   string          `json:"post_build_script"`
}

// UpdateProjectBuildConfigRequest 更新项目构建配置请求
type UpdateProjectBuildConfigRequest struct {
	BuildParams       json.RawMessage `json:"build_params"`
	RuntimeParams     json.RawMessage `json:"runtime_params"`
	CustomBuildScript string          `json:"custom_build_script"`
	PreBuildScript    string          `json:"pre_build_script"`
	PostBuildScript   string          `json:"post_build_script"`
}

// UpdateProjectBuildConfigWithAppEnvRequest 更新项目构建配置请求（包含应用和环境ID）
type UpdateProjectBuildConfigWithAppEnvRequest struct {
	AppID int64 `json:"app_id" binding:"required"`
	EnvID int64 `json:"env_id" binding:"required"`
	UpdateProjectBuildConfigRequest
}

// ProjectBuildConfigResponse 项目构建配置响应
type ProjectBuildConfigResponse struct {
	ID                int64                  `json:"id"`
	AppID             int64                  `json:"app_id"`
	EnvID             int64                  `json:"env_id"`
	TemplateID        int64                  `json:"template_id"`
	Language          string                 `json:"language"`
	TemplateType      string                 `json:"template_type"`
	BuildParams       json.RawMessage        `json:"build_params"`
	RuntimeParams     json.RawMessage        `json:"runtime_params"`
	CustomBuildScript string                 `json:"custom_build_script"`
	PreBuildScript    string                 `json:"pre_build_script"`
	PostBuildScript   string                 `json:"post_build_script"`
	Template          *BuildTemplateResponse `json:"template,omitempty"`
	CT                int64                  `json:"c_t"`
	UT                int64                  `json:"u_t"`
}

// BuildConfigPreviewRequest 构建配置预览请求
type BuildConfigPreviewRequest struct {
	Language      string          `json:"language" binding:"required"`
	TemplateType  string          `json:"template_type" binding:"required"`
	BuildParams   json.RawMessage `json:"build_params"`
	RuntimeParams json.RawMessage `json:"runtime_params"`
}

// BuildConfigPreviewResponse 构建配置预览响应
type BuildConfigPreviewResponse struct {
	BuildCommand      string `json:"build_command"`
	DockerfileContent string `json:"dockerfile_content"`
	WorkflowParams    string `json:"workflow_params"`
}
