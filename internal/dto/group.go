package dto

import "ci-gateway/internal/model"

// CreateGroupRequest 创建分组请求结构体
type CreateGroupRequest struct {
	Code        string `json:"code" binding:"required,max=50`
	Name        string `json:"name" binding:"required,max=50"`
	Description string `json:"description" binding:"max=500"`
	AppID       int64  `json:"app_id" binding:"required"`
	EnvID       int64  `json:"env_id" binding:"required"`
}

// UpdateGroupRequest 更新分组请求结构体
type UpdateGroupRequest struct {
	Code        string `json:"code" binding:"omitempty,max=50"`
	Name        string `json:"name" binding:"omitempty,max=100"`
	Description string `json:"description" binding:"omitempty,max=500"`
}

// ListGroupsRequest 分组列表请求结构体
type ListGroupsRequest struct {
	AppID int64  `form:"app_id" json:"app_id"`
	EnvID int64  `form:"env_id" json:"env_id"`
	Code  string `form:"code" json:"code"`
	Name  string `form:"name" json:"name"`
}

// ListGroupsResponse 分组列表响应结构体
type ListGroupsResponse struct {
	List  []*model.Group `json:"list"`
	Total int64          `json:"total"`
}

// GroupResponse 分组响应结构体
type GroupResponse struct {
	ID          int64  `json:"id"`
	Code        string `json:"code"`
	Name        string `json:"name"`
	Description string `json:"description"`
	AppID       int64  `json:"app_id"`
	EnvID       int64  `json:"env_id"`
	IsBranch    int8   `json:"is_branch"`

	Semver   string `json:"semver"`
	CommitID string `json:"commit_id"`
	Status   int    `json:"status"`

	// 审计字段
	CT       int64 `json:"c_t"`
	CreateBy int64 `json:"create_by"`
	UT       int64 `json:"u_t"`
	UpdateBy int64 `json:"update_by"`
}
