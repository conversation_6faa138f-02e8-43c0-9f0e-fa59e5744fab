package dto

type CreateDeployTaskRequest struct {
	AppID       int64   `json:"app_id" binding:"required"`
	EnvID       int64   `json:"env_id" binding:"required"`
	GroupIDs    []int64 `json:"group_ids" binding:"min=1"`
	Semver      string  `json:"semver" binding:"required"`
	CommitID    string  `json:"commit_id"`
	IsBranch    int8    `json:"is_branch"`
	DeployType  string  `json:"deploy_type" binding:"required"`
	Description string  `json:"description" binding:"required"`
}

// QueryDeployTasksRequest 查询部署任务列表请求
type QueryDeployTasksRequest struct {
	AppID      int64  `form:"app_id" binding:"required"`
	EnvID      int64  `form:"env_id" binding:"required"`
	Page       int    `form:"page" binding:"min=1"`
	PageSize   int    `form:"page_size" binding:"min=1,max=100"`
	Status     *int   `form:"status"`
	DeployType string `form:"deploy_type"`
	GroupID    *int64 `form:"group_id"`
	Keyword    string `form:"keyword"`
}

// PageInfo 分页信息
type PageInfo struct {
	Page     int `json:"page"`
	PageSize int `json:"page_size"`
	Total    int `json:"total"`
}

// DeployTasksResponse 部署任务列表响应
type DeployTasksResponse struct {
	PageInfo PageInfo    `json:"page_info"`
	List     interface{} `json:"list"`
}
