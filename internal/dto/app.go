package dto

import (
	"ci-gateway/internal/model"
	"ci-gateway/internal/model/cmdb"
)

// Application 包含应用及其分组信息的DTO结构体
type Application struct {
	Application          *cmdb.Application            `json:"application"`
	AppSettings          *model.AppSettings           `json:"app_settings"`
	Groups               []*GroupResponse             `json:"groups"`
	EnvironmentVariables []*model.EnvironmentVariable `json:"environment_variables"`
	ConfigFiles          []*model.ConfigFile          `json:"config_files"`
}
