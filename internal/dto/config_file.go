package dto

type CreateConfigFileRequest struct {
	Name    string `json:"name" binding:"required"`
	Path    string `json:"path" binding:"required"`
	Format  string `json:"format" binding:"required"`
	Content string `json:"content" binding:"required"`
	AppID   int64  `json:"app_id" binding:"required"`
	EnvID   int64  `json:"env_id" binding:"required"`
	GroupID int64  `json:"group_id" binding:"min=0"`
}
