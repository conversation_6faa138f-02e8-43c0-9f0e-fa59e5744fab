package service

import (
	"context"
	"fmt"
	"time"

	"ci-gateway/internal/dto"
	"ci-gateway/internal/model"
	"ci-gateway/internal/repository"
)

// DeployTaskService 部署任务服务接口
type DeployTaskService interface {
	CreateDeployTask(ctx context.Context, req *dto.CreateDeployTaskRequest, userID int64) (*model.DeployTask, error)
	GetDeployTaskByID(ctx context.Context, taskID int64) (*model.DeployTask, error)
	GetDeployTasksByAppAndEnvID(ctx context.Context, appID, envID int64) ([]*model.DeployTask, error)
	QueryDeployTasks(ctx context.Context, req *dto.QueryDeployTasksRequest) (*dto.DeployTasksResponse, error)
	UpdateTaskDetailStatus(ctx context.Context, taskID, groupID int64, status int, message string) error
	GetDeployTaskDetailByTaskIDAndGroupID(ctx context.Context, taskID, groupID int64) (*model.DeployTaskDetail, error)
}

// deployTaskService 部署任务服务实现
type deployTaskService struct {
	Service               *Service
	deployTaskRepo        repository.DeployTaskRepository
	workflowService       WorkflowService
	ConfigSnapshotService ConfigSnapshotService
}

// NewDeployTaskService 创建部署任务服务实例
func NewDeployTaskService(
	service *Service,
	deployTaskRepo repository.DeployTaskRepository,
	workflowService WorkflowService,
	ConfigSnapshotService ConfigSnapshotService,
) DeployTaskService {
	return &deployTaskService{
		Service:               service,
		deployTaskRepo:        deployTaskRepo,
		workflowService:       workflowService,
		ConfigSnapshotService: ConfigSnapshotService,
	}
}

// CreateDeployTask 创建部署任务
func (s *deployTaskService) CreateDeployTask(ctx context.Context, req *dto.CreateDeployTaskRequest, userID int64) (*model.DeployTask, error) {
	deployTask := &model.DeployTask{
		AppID:       req.AppID,
		EnvID:       req.EnvID,
		Status:      model.DeployTaskStatusPending,
		Semver:      req.Semver,
		CommitID:    req.CommitID,
		IsBranch:    req.IsBranch,
		CreateBy:    userID,
		UpdateBy:    userID,
		Description: req.Description,
	}

	snapshots, err := s.ConfigSnapshotService.GenerateConfigSnapshot(ctx, deployTask, req.GroupIDs)
	if err != nil {
		return nil, err
	}

	for _, groupID := range req.GroupIDs {
		snapshot := snapshots[groupID]

		taskDetail := &model.DeployTaskDetail{
			AppID:          req.AppID,
			EnvID:          req.EnvID,
			GroupID:        groupID,
			TaskID:         deployTask.ID,
			IsBranch:       req.IsBranch,
			Semver:         req.Semver,
			CommitID:       req.CommitID,
			Status:         model.DeployTaskDetailStatusPending,
			StartTime:      time.Now().Unix(),
			ConfigSnapshot: snapshot.ToJSON(),
		}

		deployTask.TaskDetails = append(deployTask.TaskDetails, taskDetail)
	}

	err = s.deployTaskRepo.CreateDeployTask(deployTask)
	if err != nil {
		return nil, fmt.Errorf("创建部署任务失败: %w", err)
	}

	// 任务创建后触发Argo工作流
	go func() {
		// 使用context.Background()是因为原始ctx可能已经取消
		triggerCtx := context.Background()

		// 根据部署类型选择对应的工作流
		var err error
		if req.DeployType == "rollback" {
			err = s.workflowService.TriggerRollbackWorkflow(triggerCtx, deployTask)
		} else {
			err = s.workflowService.TriggerWorkflow(triggerCtx, deployTask)
		}

		if err != nil {
			model.UpdateTaskStatusByWorkflow(deployTask, model.WorkflowStatusTriggerFailed)
			s.deployTaskRepo.UpdateDeployTask(deployTask)
		}
	}()

	return deployTask, nil
}

// GetDeployTaskByID 根据ID获取部署任务详情
func (s *deployTaskService) GetDeployTaskByID(ctx context.Context, taskID int64) (*model.DeployTask, error) {
	return s.deployTaskRepo.GetDeployTaskByID(taskID)
}

// GetDeployTasksByAppAndEnvID 根据应用ID和环境ID获取部署任务列表
func (s *deployTaskService) GetDeployTasksByAppAndEnvID(ctx context.Context, appID, envID int64) ([]*model.DeployTask, error) {
	return s.deployTaskRepo.GetDeployTaskByAppAndEnvID(appID, envID)
}

// QueryDeployTasks 查询部署任务列表（支持分页和过滤）
func (s *deployTaskService) QueryDeployTasks(ctx context.Context, req *dto.QueryDeployTasksRequest) (*dto.DeployTasksResponse, error) {
	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	// 调用仓库方法，获取总数和分页列表
	count, deployTasks, err := s.deployTaskRepo.QueryDeployTasks(
		req.AppID,
		req.EnvID,
		req.Status,
		req.DeployType,
		req.GroupID,
		req.Keyword,
		req.Page,
		req.PageSize,
	)
	if err != nil {
		return nil, fmt.Errorf("查询部署任务列表失败: %w", err)
	}

	// 构建响应
	response := &dto.DeployTasksResponse{
		PageInfo: dto.PageInfo{
			Page:     req.Page,
			PageSize: req.PageSize,
			Total:    count,
		},
		List: deployTasks,
	}

	return response, nil
}

// UpdateTaskDetailStatus 更新部署任务详情状态
func (s *deployTaskService) UpdateTaskDetailStatus(ctx context.Context, taskID, groupID int64, status int, message string) error {
	return s.deployTaskRepo.UpdateTaskDetailStatus(ctx, taskID, groupID, status, message)
}

// GetDeployTaskDetailByTaskIDAndGroupID 根据任务ID和分组ID获取部署任务详情配置
func (s *deployTaskService) GetDeployTaskDetailByTaskIDAndGroupID(ctx context.Context, taskID, groupID int64) (*model.DeployTaskDetail, error) {
	return s.deployTaskRepo.GetDeployTaskDetailByTaskIDAndGroupID(ctx, taskID, groupID)
}
