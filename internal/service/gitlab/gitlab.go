package gitlab

import (
	"ci-gateway/internal/service"
	"ci-gateway/pkg/gitlab"
	"errors"
	"fmt"
	"strings"

	gitlabAPI "gitlab.com/gitlab-org/api/client-go"
	"go.uber.org/zap"
)

var (
	ErrInvalidRepositoryURL = errors.New("无效的仓库URL")
	ErrGitLabClientNotFound = errors.New("GitLab客户端未初始化")
	ErrProjectNotFound      = errors.New("GitLab项目未找到")
)

type GitLabService interface {
	GetProjectIDFromURL(repositoryURL string) (int, error)
	GetTags(projectID int, search string, page, perPage int) ([]*gitlabAPI.Tag, error)
	GetBranches(projectID int, search string, page, perPage int) ([]*gitlabAPI.Branch, error)
}

// GitLabService 是GitLab API客户端的封装
type gitLabService struct {
	client *gitlab.Client
	*service.Service
}

// NewGitLabService
func NewGitLabService(service *service.Service) GitLabService {
	return &gitLabService{
		Service: service,
		client:  gitlab.GetClient(service.GetConfig(), service.GetLogger()),
	}
}

func (s *gitLabService) GetProjectIDFromURL(repositoryURL string) (int, error) {

	gitlabClient := s.client.GetGitLabAPI()

	// 检查URL是否为空
	if repositoryURL == "" {
		return 0, ErrInvalidRepositoryURL
	}

	// 尝试通过路径查询项目
	// 将仓库URL解析为项目路径，例如 https://git.example.com/group/project 转为 group/project
	parts := strings.Split(repositoryURL, "/")
	if len(parts) < 2 {
		return 0, ErrInvalidRepositoryURL
	}

	// 获取最后两段作为group/project
	projectPath := strings.Join(parts[len(parts)-2:], "/")

	// 移除.git后缀如果存在
	projectPath = strings.TrimSuffix(projectPath, ".git")

	// 通过路径查询项目
	project, _, err := gitlabClient.Projects.GetProject(projectPath, nil)
	if err != nil {
		s.GetLogger().Error("获取GitLab项目失败",
			zap.String("repository_url", repositoryURL),
			zap.String("project_path", projectPath),
			zap.Error(err))
		return 0, fmt.Errorf("获取项目信息失败: %w", err)
	}

	if project == nil {
		return 0, ErrProjectNotFound
	}

	return project.ID, nil
}

// GetTags 获取项目标签列表
func (s *gitLabService) GetTags(projectID int, search string, page, perPage int) ([]*gitlabAPI.Tag, error) {

	gitlabClient := s.client.GetGitLabAPI()

	options := &gitlabAPI.ListTagsOptions{
		ListOptions: gitlabAPI.ListOptions{
			Page:    page,
			PerPage: perPage,
		},
	}

	if search != "" {
		options.Search = &search
	}

	tags, _, err := gitlabClient.Tags.ListTags(projectID, options)

	if err != nil {
		return nil, fmt.Errorf("获取标签失败: %w", err)
	}

	if tags == nil {
		return []*gitlabAPI.Tag{}, nil
	}

	return tags, nil
}

// GetBranches 获取项目分支列表
func (s *gitLabService) GetBranches(projectID int, search string, page, perPage int) ([]*gitlabAPI.Branch, error) {
	gitlabClient := s.client.GetGitLabAPI()

	options := &gitlabAPI.ListBranchesOptions{
		ListOptions: gitlabAPI.ListOptions{
			Page:    page,
			PerPage: perPage,
		},
	}

	if search != "" {
		options.Search = &search
	}

	branches, _, err := gitlabClient.Branches.ListBranches(projectID, options)

	if err != nil {
		return nil, fmt.Errorf("获取分支失败: %w", err)
	}

	if branches == nil {
		return []*gitlabAPI.Branch{}, nil
	}

	return branches, nil
}
