package cmdb

import (
	"context"
	"fmt"

	"ci-gateway/internal/dto"
	"ci-gateway/pkg/http"

	"github.com/spf13/viper"
	"go.uber.org/zap"
)

const (
	OpsSrvtreeTreePath   = "/srv_tree/tree"
	OpsAppInfoByNodePath = "/srv_tree/app_info"
)

type SrvTreeService interface {
	GetUserSrvTree(ctx context.Context, userCode string) ([]*dto.OpsNode, error)
	GetAppInfoByNodeID(ctx context.Context, nodeID int64, userCode string) (*dto.AppInfo, error)
}

type srvTreeService struct {
	config *viper.Viper
	logger *zap.Logger
}

// NewSrvTreeService 创建服务树服务
func NewSrvTreeService(config *viper.Viper, logger *zap.Logger) SrvTreeService {
	return &srvTreeService{
		config: config,
		logger: logger,
	}
}

// GetUserSrvTree 查询用户服务树
func (s *srvTreeService) GetUserSrvTree(ctx context.Context, userCode string) ([]*dto.OpsNode, error) {
	baseUrl := s.config.GetString("rpc.srv_url")

	// 构建请求体
	requestBody := map[string]interface{}{
		"userCode": userCode,
	}

	// 使用封装的 OpsApiRequest 函数发送请求
	opsNodesResult, err := http.JsonRequest[dto.OpsNodesResult](
		ctx,
		fmt.Sprintf("%s%s", baseUrl, OpsSrvtreeTreePath),
		requestBody,
		s.logger,
	)

	if err != nil {
		return nil, fmt.Errorf("查询用户服务树失败: %w", err)
	}

	if opsNodesResult.Status != 200 {
		s.logger.Error("获取服务树失败", zap.String("message", opsNodesResult.Message))
		return nil, fmt.Errorf("获取服务树失败: %s", opsNodesResult.Message)
	}

	return opsNodesResult.Result, nil
}

// GetAppInfoByNodeID 根据节点ID获取应用信息
func (s *srvTreeService) GetAppInfoByNodeID(ctx context.Context, nodeID int64, userCode string) (*dto.AppInfo, error) {
	baseUrl := s.config.GetString("rpc.srv_url")

	// 构建请求体
	requestBody := map[string]interface{}{
		"nodeId":   nodeID,
		"userCode": userCode,
	}

	// 发送请求获取应用信息
	appInfoResult, err := http.JsonRequest[dto.AppInfoResult](
		ctx,
		fmt.Sprintf("%s%s", baseUrl, OpsAppInfoByNodePath),
		requestBody,
		s.logger,
	)

	if err != nil {
		return nil, fmt.Errorf("获取应用信息失败: %w", err)
	}

	if appInfoResult.Status != 200 {
		return nil, fmt.Errorf("获取应用信息失败: %s", appInfoResult.Message)
	}

	return appInfoResult.Result, nil
}
