package cmdb

import (
	"context"
	"fmt"

	"ci-gateway/internal/dto"
	"ci-gateway/internal/model/cmdb"
	cmdbRepo "ci-gateway/internal/repository/cmdb"
)

type ApplicationService interface {
	GetApplicationByID(ctx context.Context, id int64) (*cmdb.Application, error)
	GetApplicationByCode(ctx context.Context, appCode string) (*cmdb.Application, error)
	GetApplicationByEnvAndSrvTree(ctx context.Context, envID int64, srvTreeID int64) (*dto.Application, error)
}

type applicationService struct {
	applicationRepo cmdbRepo.ApplicationRepository
}

func NewApplicationService(applicationRepo cmdbRepo.ApplicationRepository) ApplicationService {
	return &applicationService{
		applicationRepo: applicationRepo,
	}
}

// GetApplicationByID 根据ID查询应用
func (s *applicationService) GetApplicationByID(ctx context.Context, id int64) (*cmdb.Application, error) {
	app, err := s.applicationRepo.GetApplicationByID(id)
	if err != nil {
		return nil, fmt.Errorf("根据ID查询CMDB应用失败: %w", err)
	}

	return app, nil
}

// GetApplicationByCode 根据应用代码查询应用
func (s *applicationService) GetApplicationByCode(ctx context.Context, appCode string) (*cmdb.Application, error) {
	app, err := s.applicationRepo.GetApplicationByCode(appCode)
	if err != nil {
		return nil, fmt.Errorf("查询CMDB应用失败: %w", err)
	}

	return app, nil
}

// GetApplicationByEnvAndSrvTree 根据环境ID和服务树ID获取应用及其分组信息
func (s *applicationService) GetApplicationByEnvAndSrvTree(ctx context.Context, envID int64, srvTreeID int64) (*dto.Application, error) {
	app, err := s.applicationRepo.GetApplicationBySrvTreeID(srvTreeID)
	if err != nil {
		return nil, fmt.Errorf("获取应用及其分组信息失败: %w", err)
	}

	return &dto.Application{
		Application: app,
	}, nil
}
