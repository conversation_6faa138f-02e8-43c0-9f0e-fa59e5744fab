package service

import (
	"ci-gateway/internal/dto"
	"ci-gateway/internal/model"
	"ci-gateway/internal/repository"
)

// EnvironmentVariableService 环境变量服务接口
type EnvironmentVariableService interface {
	GetEnvironmentVariablesByAppAndEnvID(appID, envID int64) ([]*model.EnvironmentVariable, error)
	CreateEnvironmentVariable(req dto.CreateEnvironmentVariableRequest, userID int64) (*model.EnvironmentVariable, error)
	UpdateEnvironmentVariable(req dto.UpdateEnvironmentVariableRequest, userID int64) (*model.EnvironmentVariable, error)
}

type environmentVariableService struct {
	*Service
	environmentVariableRepo repository.EnvironmentVariableRepository
}

// NewEnvironmentVariableService 创建环境变量服务实例
func NewEnvironmentVariableService(service *Service, environmentVariableRepo repository.EnvironmentVariableRepository) EnvironmentVariableService {
	return &environmentVariableService{
		Service:                 service,
		environmentVariableRepo: environmentVariableRepo,
	}
}

func (s *environmentVariableService) GetEnvironmentVariablesByAppAndEnvID(appID, envID int64) ([]*model.EnvironmentVariable, error) {
	return s.environmentVariableRepo.GetEnvironmentVariablesByAppAndEnvID(appID, envID)
}

func (s *environmentVariableService) CreateEnvironmentVariable(req dto.CreateEnvironmentVariableRequest, userID int64) (*model.EnvironmentVariable, error) {
	environmentVariable := &model.EnvironmentVariable{
		AppID:        req.AppID,
		EnvID:        req.EnvID,
		KeyName:      req.KeyName,
		Value:        req.Value,
		Description:  req.Description,
		VariableType: req.VariableType,
		Dependencies: req.Dependencies,
		CreateBy:     userID,
		UpdateBy:     userID,
	}

	err := s.environmentVariableRepo.CreateEnvironmentVariable(environmentVariable)
	if err != nil {
		return nil, err
	}

	return environmentVariable, nil
}

func (s *environmentVariableService) UpdateEnvironmentVariable(req dto.UpdateEnvironmentVariableRequest, userID int64) (*model.EnvironmentVariable, error) {
	environmentVariable := &model.EnvironmentVariable{
		ID:           req.ID,
		KeyName:      req.KeyName,
		Value:        req.Value,
		Description:  req.Description,
		VariableType: req.VariableType,
		Dependencies: req.Dependencies,
		UpdateBy:     userID,
		Status:       req.Status,
	}

	err := s.environmentVariableRepo.UpdateEnvironmentVariable(environmentVariable)
	if err != nil {
		return nil, err
	}

	return environmentVariable, nil
}
