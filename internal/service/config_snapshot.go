package service

import (
	"context"
	"fmt"
	"strings"

	"ci-gateway/internal/model"
	"ci-gateway/internal/service/cmdb"
)

// ConfigSnapshot 配置快照
type ConfigSnapshotService interface {
	// GenerateConfigSnapshot 生成配置快照
	GenerateConfigSnapshot(ctx context.Context, req *model.DeployTask, groupIDs []int64) (map[int64]*model.ConfigSnapshot, error)
}

type configSnapshotService struct {
	buildConfigService BuildConfigService
	probeService       ProbeService
	appSettingsService AppSettingsService
	groupService       GroupService
	containerService   ContainerService
	applicationService cmdb.ApplicationService
}

// NewConfigSnapshotService 创建配置快照服务
func NewConfigSnapshotService(
	buildConfigService BuildConfigService,
	probeService ProbeService,
	appSettingsService AppSettingsService,
	groupService GroupService,
	containerService ContainerService,
	applicationService cmdb.ApplicationService,
) ConfigSnapshotService {
	return &configSnapshotService{
		buildConfigService: buildConfigService,
		probeService:       probeService,
		appSettingsService: appSettingsService,
		groupService:       groupService,
		containerService:   containerService,
		applicationService: applicationService,
	}
}

// GenerateConfigSnapshot 生成配置快照
func (s *configSnapshotService) GenerateConfigSnapshot(ctx context.Context, deployTask *model.DeployTask, groupIDs []int64) (map[int64]*model.ConfigSnapshot, error) {
	snapshots := make(map[int64]*model.ConfigSnapshot)

	for _, groupID := range groupIDs {
		snapshot, err := s.generateConfigSnapshot(ctx, deployTask, groupID)
		if err != nil {
			return nil, err
		}
		snapshots[groupID] = snapshot
	}

	return snapshots, nil
}

func (s *configSnapshotService) generateConfigSnapshot(ctx context.Context, deployTask *model.DeployTask, groupID int64) (*model.ConfigSnapshot, error) {
	snapshot := &model.ConfigSnapshot{}

	snapshot.Metadata = model.MetadataConfig{
		AppID:    deployTask.AppID,
		EnvID:    deployTask.EnvID,
		GroupID:  groupID,
		CommitID: deployTask.CommitID,
		Semver:   deployTask.Semver,
		TaskID:   deployTask.ID,
	}

	probe, err := s.probeService.FindByAppAndEnvAndGroupID(ctx, deployTask.AppID, deployTask.EnvID, groupID)
	if err != nil {
		return nil, err
	}
	snapshot.Probe = probe

	buildConfig, err := s.buildConfigService.GetConfigByGroupID(ctx, groupID)
	if err != nil {
		return nil, err
	}
	snapshot.Build = buildConfig

	container, err := s.containerService.GetContainerByGroupID(ctx, groupID)
	if err != nil {
		return nil, err
	}
	snapshot.Container = container

	err = s.fillLabelsConfig(ctx, snapshot, deployTask, groupID)
	if err != nil {
		return nil, err
	}

	return snapshot, nil
}

// fillLabelsConfig 填充标签配置
func (s *configSnapshotService) fillLabelsConfig(ctx context.Context, snapshot *model.ConfigSnapshot, deployTask *model.DeployTask, groupID int64) error {
	app, err := s.applicationService.GetApplicationByID(ctx, deployTask.AppID)
	if err != nil {
		return fmt.Errorf("获取应用信息失败: %w", err)
	}

	appVersion := fmt.Sprintf("BUILD_DEPLOY-%d", deployTask.ID)

	snapshot.Labels = model.LabelConfig{
		AppCode:       app.Code,                            // 应用代码
		AppID:         fmt.Sprintf("%d", app.ID),           // 应用ID
		AppGroup:      fmt.Sprintf("%d", groupID),          // 分组代码
		AppLang:       strings.ToUpper(app.Language),       // 应用语言，转大写
		AppLevel:      fmt.Sprintf("%d", app.Level),        // 应用级别，默认1
		AppVersion:    appVersion,                          // 应用版本
		DeployID:      fmt.Sprintf("%d", deployTask.ID),    // 部署ID
		EnvironmentID: fmt.Sprintf("%d", deployTask.EnvID), // 环境ID
		OWT:           app.Owt,                             // 组织架构-团队
		PDL:           app.PDL,                             // 组织架构-产品线
		Role:          "active",                            // 角色
		SG:            app.SG,                              // 服务组
		SRV:           app.Srv,                             // 服务名
	}

	return nil
}
