package service

import (
	"ci-gateway/internal/model"
	"ci-gateway/internal/repository"
)

type AppSettingsService interface {
	GetAppSettingsByID(id int64) (*model.AppSettings, error)
	GetAppSettingsByAppID(appID int64) (*model.AppSettings, error)
	CreateAppSettings(settings *model.AppSettings) error
	UpdateAppSettings(settings *model.AppSettings) error
	DeleteAppSettings(id int64) error
	ListAppSettings(limit, offset int) ([]*model.AppSettings, error)
}

type appSettingsService struct {
	*Service
	repo repository.AppSettingsRepository
}

func NewAppSettingsService(service *Service, repo repository.AppSettingsRepository) AppSettingsService {
	return &appSettingsService{
		Service: service,
		repo:    repo,
	}
}

func (s *appSettingsService) GetAppSettingsByID(id int64) (*model.AppSettings, error) {
	return s.repo.GetAppSettingsByID(id)
}

func (s *appSettingsService) GetAppSettingsByAppID(appID int64) (*model.AppSettings, error) {
	return s.repo.GetAppSettingsByAppID(appID)
}

func (s *appSettingsService) CreateAppSettings(settings *model.AppSettings) error {
	return s.repo.CreateAppSettings(settings)
}

func (s *appSettingsService) UpdateAppSettings(settings *model.AppSettings) error {
	return s.repo.UpdateAppSettings(settings)
}

func (s *appSettingsService) DeleteAppSettings(id int64) error {
	return s.repo.DeleteAppSettings(id)
}

func (s *appSettingsService) ListAppSettings(limit, offset int) ([]*model.AppSettings, error) {
	return s.repo.ListAppSettings(limit, offset)
}
