package service

import (
	"ci-gateway/internal/model"
	"ci-gateway/internal/repository"
	"ci-gateway/pkg/k8sclient"
	"context"
	"encoding/json"
	"fmt"
	"time"

	wfv1 "github.com/argoproj/argo-workflows/v3/pkg/apis/workflow/v1alpha1"
	wfclientset "github.com/argoproj/argo-workflows/v3/pkg/client/clientset/versioned"
	"github.com/spf13/viper"
	"go.uber.org/zap"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// WorkflowService 工作流服务接口
type WorkflowService interface {
	// TriggerWorkflow 触发Argo Workflow
	TriggerWorkflow(ctx context.Context, task *model.DeployTask) error

	// TriggerRollbackWorkflow 触发回滚工作流
	TriggerRollbackWorkflow(ctx context.Context, task *model.DeployTask) error

	// GetWorkflowStatus 获取工作流状态
	GetWorkflowStatus(ctx context.Context, workflowName string) (string, error)
}

// workflowService 工作流服务实现
type workflowService struct {
	*Service
	deployTaskRepo     repository.DeployTaskRepository
	appSettingsService AppSettingsService
	wfClientset        *wfclientset.Clientset
	syncInterval       time.Duration
}

// NewWorkflowService 创建工作流服务实例
func NewWorkflowService(
	service *Service,
	deployTaskRepo repository.DeployTaskRepository,
	appSettingsService AppSettingsService,
	syncInterval time.Duration,
) (WorkflowService, error) {
	// 创建Argo Workflows客户端
	// 使用k8sclient包中的方法获取Kubernetes配置
	configProvider := k8sclient.GenerateK8sConfig()
	config, err := configProvider()
	if err != nil {
		return nil, fmt.Errorf("获取Kubernetes配置失败: %w", err)
	}

	// 创建Workflow客户端
	wfClientset, err := wfclientset.NewForConfig(config)
	if err != nil {
		return nil, fmt.Errorf("创建工作流客户端失败: %w", err)
	}

	return &workflowService{
		Service:            service,
		deployTaskRepo:     deployTaskRepo,
		appSettingsService: appSettingsService,
		wfClientset:        wfClientset,
		syncInterval:       syncInterval,
	}, nil
}

// NewWorkflowServiceFromConfig 基于配置创建工作流服务并启动状态同步器
func NewWorkflowServiceFromConfig(
	service *Service,
	deployTaskRepo repository.DeployTaskRepository,
	appSettingsService AppSettingsService,
	config *viper.Viper,
) (WorkflowService, error) {
	// 从配置中获取工作流相关设置
	syncInterval := config.GetDuration("workflow.sync_interval")
	if syncInterval == 0 {
		syncInterval = 30 * time.Second // 默认30秒
	}

	// 创建工作流服务
	workflowService, err := NewWorkflowService(
		service,
		deployTaskRepo,
		appSettingsService,
		syncInterval,
	)

	if err != nil {
		return nil, err
	}

	return workflowService, nil
}

// TriggerWorkflow 触发Argo Workflow
func (s *workflowService) TriggerWorkflow(ctx context.Context, task *model.DeployTask) error {
	// 获取应用信息
	appSettings, err := s.appSettingsService.GetAppSettingsByAppID(task.AppID)
	if err != nil {
		return fmt.Errorf("获取应用信息失败: %w", err)
	}

	// 获取组ID并确保正确的执行顺序
	var groupIDs []int64

	// 从TaskDetails中获取所有组ID
	for _, detail := range task.TaskDetails {
		groupIDs = append(groupIDs, detail.GroupID)
	}

	// 根据优先级排序组
	orderedGroupIDs, err := s.determineDeployOrder(ctx, task.AppID, task.EnvID, groupIDs)
	if err != nil {
		return fmt.Errorf("确定部署顺序失败: %w", err)
	}

	// 转换为JSON字符串
	groupIDsJSON, err := json.Marshal(orderedGroupIDs)
	if err != nil {
		return fmt.Errorf("序列化组ID失败: %w", err)
	}

	// 选择工作流模板
	workflowTemplateName := "sequential-group-deployment"

	// 获取镜像仓库信息
	imageRegistry := s.GetConfig().GetString("workflow.image_registry")

	// 创建工作流
	workflow := &wfv1.Workflow{
		ObjectMeta: metav1.ObjectMeta{
			GenerateName: fmt.Sprintf("deploy-%d-", task.ID),
			Namespace:    s.GetConfig().GetString("workflow.namespace"),
			Labels: map[string]string{
				"app-id":         fmt.Sprintf("%d", task.AppID),
				"env-id":         fmt.Sprintf("%d", task.EnvID),
				"deploy-task-id": fmt.Sprintf("%d", task.ID),
				"workflows.argoproj.io/controller-instanceid": "deploy-gateway",
			},
		},
		Spec: wfv1.WorkflowSpec{
			WorkflowTemplateRef: &wfv1.WorkflowTemplateRef{
				Name: workflowTemplateName,
			},
			Arguments: wfv1.Arguments{
				Parameters: []wfv1.Parameter{
					{Name: "app_id", Value: wfv1.AnyStringPtr(fmt.Sprintf("%d", task.AppID))},
					{Name: "env_id", Value: wfv1.AnyStringPtr(fmt.Sprintf("%d", task.EnvID))},
					{Name: "deploy_task_id", Value: wfv1.AnyStringPtr(fmt.Sprintf("%d", task.ID))},
					{Name: "commit_id", Value: wfv1.AnyStringPtr(task.CommitID)},
					{Name: "groups_order", Value: wfv1.AnyStringPtr(string(groupIDsJSON))},
					{Name: "image_registry", Value: wfv1.AnyStringPtr(imageRegistry)},
					{Name: "git_repo", Value: wfv1.AnyStringPtr(appSettings.CodeRepository)},
				},
			},
		},
	}

	// 提交工作流
	createdWf, err := s.wfClientset.ArgoprojV1alpha1().Workflows("argo").Create(ctx, workflow, metav1.CreateOptions{})
	if err != nil {
		return fmt.Errorf("创建工作流失败: %w", err)
	}

	// 使用标准化的工作流状态
	workflowStatus := model.WorkflowStatusRunning

	// 更新部署任务，记录工作流名称
	task.WorkflowName = createdWf.Name
	model.UpdateTaskStatusByWorkflow(task, workflowStatus)
	task.WorkflowStartTime = time.Now().Unix()

	err = s.deployTaskRepo.UpdateDeployTask(task)
	if err != nil {
		return fmt.Errorf("更新部署任务失败: %w", err)
	}

	s.GetLogger().Info("工作流已触发",
		zap.String("workflowName", task.WorkflowName),
		zap.Int64("taskID", task.ID))

	return nil
}

// TriggerRollbackWorkflow 触发回滚工作流
func (s *workflowService) TriggerRollbackWorkflow(ctx context.Context, task *model.DeployTask) error {
	// 获取部署配置
	deploymentConfig, err := s.getDeploymentConfig(ctx, task.AppID, task.EnvID)
	if err != nil {
		return fmt.Errorf("获取部署配置失败: %w", err)
	}

	// 将配置转换为JSON字符串
	configJSON, err := json.Marshal(deploymentConfig)
	if err != nil {
		return fmt.Errorf("序列化部署配置失败: %w", err)
	}

	// 创建回滚工作流
	workflow := &wfv1.Workflow{
		ObjectMeta: metav1.ObjectMeta{
			GenerateName: fmt.Sprintf("rollback-%d-", task.ID),
			Namespace:    s.GetConfig().GetString("workflow.namespace"),
			Labels: map[string]string{
				"app-id":         fmt.Sprintf("%d", task.AppID),
				"env-id":         fmt.Sprintf("%d", task.EnvID),
				"deploy-task-id": fmt.Sprintf("%d", task.ID),
				"operation-type": "rollback",
				"workflows.argoproj.io/controller-instanceid": "deploy-gateway",
			},
		},
		Spec: wfv1.WorkflowSpec{
			WorkflowTemplateRef: &wfv1.WorkflowTemplateRef{
				Name: "rollback-deployment",
			},
			Arguments: wfv1.Arguments{
				Parameters: []wfv1.Parameter{
					{Name: "app_id", Value: wfv1.AnyStringPtr(fmt.Sprintf("%d", task.AppID))},
					{Name: "env_id", Value: wfv1.AnyStringPtr(fmt.Sprintf("%d", task.EnvID))},
					{Name: "deploy_task_id", Value: wfv1.AnyStringPtr(fmt.Sprintf("%d", task.ID))},
					{Name: "target_revision", Value: wfv1.AnyStringPtr(task.Semver)},
					{Name: "rollback_type", Value: wfv1.AnyStringPtr("revision")},
					{Name: "deployment_config", Value: wfv1.AnyStringPtr(string(configJSON))},
				},
			},
		},
	}

	// 提交工作流
	createdWf, err := s.wfClientset.ArgoprojV1alpha1().Workflows("argo").Create(ctx, workflow, metav1.CreateOptions{})
	if err != nil {
		return fmt.Errorf("创建回滚工作流失败: %w", err)
	}

	// 使用标准化的工作流状态
	workflowStatus := model.WorkflowStatusRunning

	// 更新部署任务，记录工作流名称
	task.WorkflowName = createdWf.Name
	model.UpdateTaskStatusByWorkflow(task, workflowStatus)
	task.WorkflowStartTime = time.Now().Unix()

	err = s.deployTaskRepo.UpdateDeployTask(task)
	if err != nil {
		return fmt.Errorf("更新部署任务失败: %w", err)
	}

	s.GetLogger().Info("回滚工作流已触发",
		zap.String("workflowName", task.WorkflowName),
		zap.Int64("taskID", task.ID),
		zap.String("targetRevision", task.Semver))

	return nil
}

// getDeploymentConfig 获取部署配置（复用现有逻辑）
func (s *workflowService) getDeploymentConfig(ctx context.Context, appID, envID int64) (map[string]interface{}, error) {
	// 这里可以复用现有的获取部署配置的逻辑
	// 或者调用部署配置API获取
	config := map[string]interface{}{
		"rollout_name":   fmt.Sprintf("%d-rollout", appID),
		"namespace":      "default",
		"replica_count":  3,
		"service_port":   8080,
		"container_port": 8080,
	}

	return config, nil
}

// determineDeployOrder 确定部署组的执行顺序
func (s *workflowService) determineDeployOrder(ctx context.Context, appID, envID int64, groupIDs []int64) ([]int64, error) {
	// 如果没有配置优先级，则按原顺序返回
	return groupIDs, nil
}

// mapArgoWorkflowStatus 将Argo工作流状态映射到自定义工作流状态
func (s *workflowService) mapArgoWorkflowStatus(argoStatus wfv1.WorkflowPhase) model.WorkflowStatus {
	switch argoStatus {
	case wfv1.WorkflowSucceeded:
		return model.WorkflowStatusCompleted
	case wfv1.WorkflowFailed:
		return model.WorkflowStatusFailed
	case wfv1.WorkflowError:
		return model.WorkflowStatusFailed
	case wfv1.WorkflowRunning:
		return model.WorkflowStatusRunning
	case wfv1.WorkflowPending:
		return model.WorkflowStatusPending
	default:
		// 其他状态映射为运行中
		return model.WorkflowStatusRunning
	}
}

// GetWorkflowStatus 获取工作流状态
func (s *workflowService) GetWorkflowStatus(ctx context.Context, workflowName string) (string, error) {
	wf, err := s.wfClientset.ArgoprojV1alpha1().Workflows("argo").Get(ctx, workflowName, metav1.GetOptions{})
	if err != nil {
		return "", fmt.Errorf("获取工作流失败: %w", err)
	}

	workflowStatus := s.mapArgoWorkflowStatus(wf.Status.Phase)
	return string(workflowStatus), nil
}

// SyncWorkflowStatuses 同步所有工作流状态
func (s *workflowService) SyncWorkflowStatuses(ctx context.Context) error {
	// 获取所有具有workflow_name且状态为进行中的部署任务
	tasks, err := s.deployTaskRepo.GetTasksWithWorkflow(ctx)
	if err != nil {
		return fmt.Errorf("获取任务列表失败: %w", err)
	}

	for _, task := range tasks {
		if task.WorkflowName == "" {
			continue
		}

		// 查询工作流状态
		wf, err := s.wfClientset.ArgoprojV1alpha1().Workflows("argo").Get(ctx, task.WorkflowName, metav1.GetOptions{})
		if err != nil {
			s.logger.Error("获取工作流状态失败",
				zap.String("workflowName", task.WorkflowName),
				zap.Error(err))
			continue
		}

		// 更新任务状态
		updated := false

		// 将Argo工作流状态映射到自定义工作流状态
		workflowStatus := s.mapArgoWorkflowStatus(wf.Status.Phase)

		// 更新工作流状态
		if string(workflowStatus) != task.WorkflowStatus {
			model.UpdateTaskStatusByWorkflow(task, workflowStatus)
			updated = true
		}

		// 更新工作流结束时间
		if wf.Status.FinishedAt.Unix() > 0 && wf.Status.FinishedAt.Unix() != task.WorkflowEndTime {
			task.WorkflowEndTime = wf.Status.FinishedAt.Unix()
			updated = true
		}

		// 保存更新
		if updated {
			if err := s.deployTaskRepo.UpdateDeployTask(task); err != nil {
				s.logger.Error("更新部署任务状态失败",
					zap.Int64("taskID", task.ID),
					zap.Error(err))
			} else {
				s.logger.Info("已更新部署任务状态",
					zap.Int64("taskID", task.ID),
					zap.String("workflowStatus", task.WorkflowStatus),
					zap.String("statusDescription", workflowStatus.Description()))
			}
		}
	}

	return nil
}
