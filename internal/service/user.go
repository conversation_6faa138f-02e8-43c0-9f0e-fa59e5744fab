package service

import (
	"ci-gateway/internal/model"
	"ci-gateway/internal/repository"
)

type UserService interface {
	GetUserList(page int, pageSize int, dispname string, email string) ([]model.User, int64, error)
	UpdateUser(user *model.User) error
	UpdateCurrentApp(userID int64, appCode string) error
	GetByUsername(username string) (*model.User, error)
}

type userService struct {
	*Service
	userRepo repository.UserRepository
}

func NewUserService(service *Service, userRepo repository.UserRepository) UserService {
	return &userService{Service: service, userRepo: userRepo}
}

func (s *userService) GetUserList(page int, pageSize int, dispname string, email string) ([]model.User, int64, error) {
	users, err := s.userRepo.List(page, pageSize, dispname, email)
	if err != nil {
		return nil, 0, err
	}
	total, err := s.userRepo.Count(dispname, email)
	if err != nil {
		return nil, 0, err
	}
	return users, total, nil
}

func (s *userService) UpdateUser(user *model.User) error {
	return s.userRepo.Update(user)
}

func (s *userService) UpdateCurrentApp(userID int64, appCode string) error {
	return s.userRepo.UpdateCurrentApp(userID, appCode)
}

func (s *userService) GetByUsername(username string) (*model.User, error) {
	return s.userRepo.GetByUsername(username)
}
