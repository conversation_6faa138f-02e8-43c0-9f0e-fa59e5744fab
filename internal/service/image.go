package service

import (
	"ci-gateway/internal/model"
	"ci-gateway/internal/repository"
	"context"
)

type ImageService interface {
	GetImageList(ctx context.Context, offset int, pageSize int, name string, status int8, imageType string) ([]*model.Image, int64, error)
	Delete(ctx context.Context, id int64) error
	// UpdateUser(user *model.User) error
	// UpdateCurrentApp(userID int64, appCode string) error
	// GetByUsername(username string) (*model.User, error)
}

type imageService struct {
	*Service
	imageRepo repository.ImageRepository
}

func NewImageService(service *Service, imageRepo repository.ImageRepository) ImageService {
	return &imageService{Service: service, imageRepo: imageRepo}
}

func (s *imageService) GetImageList(ctx context.Context, offset int, pageSize int, name string, status int8, imageType string) ([]*model.Image, int64, error) {
	return s.imageRepo.ListImages(ctx, offset, pageSize, name, status, imageType)
}

func (s *imageService) Delete(ctx context.Context, id int64) error {
	return s.imageRepo.Delete(ctx, id)
}

// func (s *userService) UpdateCurrentApp(userID int64, appCode string) error {
// 	return s.userRepo.UpdateCurrentApp(userID, appCode)
// }

// func (s *userService) GetByUsername(username string) (*model.User, error) {
// 	return s.userRepo.GetByUsername(username)
// }
