package service

import (
	"ci-gateway/internal/dto"
	"ci-gateway/pkg/k8sclient"
	"ci-gateway/pkg/podexecutor"
	"ci-gateway/pkg/restart"
	"context"
	"encoding/base64"
	"fmt"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"go.uber.org/zap"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/labels"
)

// ListPodsRequest Pod列表请求
type ListPodsRequest struct {
	AppID     int64  `json:"app_id"`
	EnvID     int64  `json:"env_id"`
	Namespace string `json:"namespace"`
}

// ListPodsResponse Pod列表响应
type ListPodsResponse struct {
	Pods  []*PodInfo `json:"pods"`
	Total int        `json:"total"`
}

// PodInfo Pod信息
type PodInfo struct {
	ID          string            `json:"id"`
	Name        string            `json:"name"`
	Namespace   string            `json:"namespace"`
	Status      string            `json:"status"`
	Restarts    int32             `json:"restarts"`
	Age         string            `json:"age"`
	Ready       string            `json:"ready"`
	IP          string            `json:"ip"`
	Node        string            `json:"node"`
	CPU         string            `json:"cpu"`
	Memory      string            `json:"memory"`
	CreateTime  string            `json:"create_time"`
	Labels      map[string]string `json:"labels"`
	Annotations map[string]string `json:"annotations"`
}

// PodMetrics Pod指标
type PodMetrics struct {
	PodName    string             `json:"pod_name"`
	Namespace  string             `json:"namespace"`
	CPU        string             `json:"cpu"`
	Memory     string             `json:"memory"`
	Timestamp  time.Time          `json:"timestamp"`
	Containers []ContainerMetrics `json:"containers"`
}

// ContainerMetrics 容器指标
type ContainerMetrics struct {
	Name   string `json:"name"`
	CPU    string `json:"cpu"`
	Memory string `json:"memory"`
}

// StartArthasRequest Arthas启动请求
type StartArthasRequest struct {
	Namespace      string `json:"namespace"`
	PodName        string `json:"pod_name"`
	ContainerName  string `json:"container_name,omitempty"`
	JavaPID        string `json:"java_pid,omitempty"`        // 可选，不填自动检测
	HttpPort       int    `json:"http_port,omitempty"`       // 默认3658
	TelnetPort     int    `json:"telnet_port,omitempty"`     // 默认3658
	SessionTimeout int    `json:"session_timeout,omitempty"` // 默认1800秒
}

// ArthasStatusResponse Arthas状态响应
type ArthasStatusResponse struct {
	PodName        string    `json:"pod_name"`
	Namespace      string    `json:"namespace"`
	Status         string    `json:"status"` // starting/running/stopping/stopped/error
	JavaPID        string    `json:"java_pid"`
	HttpPort       int       `json:"http_port"`
	TelnetPort     int       `json:"telnet_port"`
	WebConsoleURL  string    `json:"web_console_url"` // WebSocket代理访问地址
	StartTime      time.Time `json:"start_time"`
	LastActiveTime time.Time `json:"last_active_time"`
	ErrorMessage   string    `json:"error_message,omitempty"`
}

// JavaProcessInfo Java进程信息
type JavaProcessInfo struct {
	PID       string `json:"pid"`
	MainClass string `json:"main_class"`
	Command   string `json:"command"`
}

// StartTerminalRequest 启动终端请求
type StartTerminalRequest struct {
	Namespace     string `json:"namespace"`
	PodName       string `json:"pod_name"`
	ContainerName string `json:"container_name,omitempty"`
	Shell         string `json:"shell,omitempty"`   // 默认 /bin/bash，可选 /bin/sh
	Cols          uint16 `json:"cols,omitempty"`    // 终端列数，默认80
	Rows          uint16 `json:"rows,omitempty"`    // 终端行数，默认24
	Timeout       int    `json:"timeout,omitempty"` // 会话超时（秒），默认3600
}

// TerminalStatusResponse 终端状态响应
type TerminalStatusResponse struct {
	PodName        string    `json:"pod_name"`
	Namespace      string    `json:"namespace"`
	ContainerName  string    `json:"container_name"`
	Status         string    `json:"status"` // starting/running/stopping/stopped/error
	SessionID      string    `json:"session_id"`
	Shell          string    `json:"shell"`
	WebSocketURL   string    `json:"websocket_url"` // WebSocket连接地址
	StartTime      time.Time `json:"start_time"`
	LastActiveTime time.Time `json:"last_active_time"`
	Cols           uint16    `json:"cols"`
	Rows           uint16    `json:"rows"`
	ErrorMessage   string    `json:"error_message,omitempty"`
}

// ResizeTerminalRequest 调整终端大小请求
type ResizeTerminalRequest struct {
	Namespace string `json:"namespace"`
	PodName   string `json:"pod_name"`
	SessionID string `json:"session_id"`
	Cols      uint16 `json:"cols"`
	Rows      uint16 `json:"rows"`
}

// PodService Pod管理服务接口
type PodService interface {
	// DumpJavaHeap 执行Java堆内存Dump
	DumpJavaHeap(ctx context.Context, namespace, podName, containerName, dumpPath string) (*HeapDumpResult, error)
	// DownloadFile 从Pod下载文件
	DownloadFile(ctx context.Context, namespace, podName, containerName, filePath string) ([]byte, error)
	// GetFileInfo 获取文件信息
	GetFileInfo(ctx context.Context, namespace, podName, containerName, filePath string) (*FileInfo, error)
	// CleanupDumpFile 清理dump文件
	CleanupDumpFile(ctx context.Context, namespace, podName, containerName, filePath string) error
	// ListPods 获取Pod列表
	ListPods(ctx context.Context, req *ListPodsRequest) (*ListPodsResponse, error)
	// GetPodMetrics 获取Pod指标
	GetPodMetrics(ctx context.Context, namespace, podName string) (*PodMetrics, error)

	// Arthas相关方法
	// StartArthas 启动Arthas
	StartArthas(ctx context.Context, req *StartArthasRequest) (*ArthasStatusResponse, error)
	// StopArthas 停止Arthas
	StopArthas(ctx context.Context, namespace, podName string) error
	// GetArthasStatus 获取Arthas状态
	GetArthasStatus(ctx context.Context, namespace, podName string) (*ArthasStatusResponse, error)
	// ListJavaProcesses 列出Java进程
	ListJavaProcesses(ctx context.Context, namespace, podName, containerName string) ([]*JavaProcessInfo, error)
	// CleanupArthas 清理Arthas资源
	CleanupArthas(ctx context.Context, namespace, podName string) error

	// Terminal相关方法
	// StartTerminal 启动终端会话
	StartTerminal(ctx context.Context, req *StartTerminalRequest) (*TerminalStatusResponse, error)
	// StopTerminal 停止终端会话
	StopTerminal(ctx context.Context, namespace, podName, sessionID string) error
	// GetTerminalStatus 获取终端状态
	GetTerminalStatus(ctx context.Context, namespace, podName, sessionID string) (*TerminalStatusResponse, error)
	// ResizeTerminal 调整终端大小
	ResizeTerminal(ctx context.Context, req *ResizeTerminalRequest) error
	// CleanupTerminal 清理终端资源
	CleanupTerminal(ctx context.Context, namespace, podName string) error

	// RestartPods 统一的Pod重启方法
	RestartPods(ctx context.Context, req *dto.PodRestartRequest) (*restart.RestartResult, error)
}

// HeapDumpResult Java堆内存Dump结果
type HeapDumpResult struct {
	DumpPath      string    `json:"dump_path"`
	FileSize      string    `json:"file_size"`
	JavaPID       string    `json:"java_pid"`
	ExecuteTime   time.Time `json:"execute_time"`
	CommandInfo   string    `json:"command_info"`
	DownloadToken string    `json:"download_token,omitempty"`
}

// FileInfo 文件信息
type FileInfo struct {
	Path    string `json:"path"`
	Size    string `json:"size"`
	ModTime string `json:"mod_time"`
	Exists  bool   `json:"exists"`
}

// podService 实现
type podService struct {
	*Service
	arthasStatusCache   map[string]*ArthasStatusResponse   // Pod状态缓存，key为namespace/podName
	terminalStatusCache map[string]*TerminalStatusResponse // 终端状态缓存，key为namespace/podName/sessionID
	statusMutex         sync.RWMutex
}

// NewPodService 创建Pod服务实例
func NewPodService(service *Service) PodService {
	podSvc := &podService{
		Service:             service,
		arthasStatusCache:   make(map[string]*ArthasStatusResponse),
		terminalStatusCache: make(map[string]*TerminalStatusResponse),
	}

	// 启动自动清理goroutine
	go podSvc.startArthasCleanupRoutine()
	go podSvc.startTerminalCleanupRoutine()

	return podSvc
}

// DumpJavaHeap 执行Java堆内存Dump
func (s *podService) DumpJavaHeap(ctx context.Context, namespace, podName, containerName, dumpPath string) (*HeapDumpResult, error) {
	s.GetLogger().Info("开始执行Java Heap Dump",
		zap.String("namespace", namespace),
		zap.String("podName", podName),
		zap.String("containerName", containerName),
		zap.String("dumpPath", dumpPath))

	// 创建Pod执行器
	podContext := podexecutor.PodContext{
		Namespace:     namespace,
		PodName:       podName,
		ContainerName: containerName,
	}
	executor := podexecutor.NewPodExec(s.GetLogger(), podContext)

	// 1. 查找Java进程
	s.GetLogger().Info("查找Java进程")
	findJavaCmd := []string{"ps aux | grep java | grep -v grep | head -1 | awk '{print $2}'"}
	javaPIDOutput, stderr, err := executor.Execute(ctx, findJavaCmd)
	if err != nil {
		s.GetLogger().Error("查找Java进程失败", zap.Error(err), zap.String("stderr", stderr))
		return nil, fmt.Errorf("查找Java进程失败: %w", err)
	}

	javaPID := strings.TrimSpace(javaPIDOutput)
	if javaPID == "" {
		return nil, fmt.Errorf("未找到Java进程，请确保应用程序正在运行")
	}

	s.GetLogger().Info("找到Java进程", zap.String("pid", javaPID))

	// 2. 检查jmap工具是否可用
	s.GetLogger().Info("检查jmap工具")
	checkJmapCmd := []string{"which jmap"}
	_, _, err = executor.Execute(ctx, checkJmapCmd)
	if err != nil {
		// 尝试使用完整路径
		s.GetLogger().Warn("jmap命令不在PATH中，尝试使用JAVA_HOME路径")
		checkJavaHomeCmd := []string{"find /usr -name jmap 2>/dev/null | head -1"}
		jmapPath, _, err := executor.Execute(ctx, checkJavaHomeCmd)
		if err != nil || strings.TrimSpace(jmapPath) == "" {
			return nil, fmt.Errorf("未找到jmap工具，请确保安装了JDK或包含调试工具的JRE")
		}
		// 使用找到的jmap路径替换默认的jmap命令
		jmapPath = strings.TrimSpace(jmapPath)
		s.GetLogger().Info("找到jmap工具", zap.String("path", jmapPath))
	}

	// 3. 创建目录（如果不存在）
	dumpDir := filepath.Dir(dumpPath)
	if dumpDir != "." && dumpDir != "/" {
		createDirCmd := []string{fmt.Sprintf("mkdir -p %s", dumpDir)}
		_, _, err = executor.Execute(ctx, createDirCmd)
		if err != nil {
			s.GetLogger().Warn("创建目录失败", zap.Error(err), zap.String("dir", dumpDir))
		}
	}

	// 4. 执行heap dump
	s.GetLogger().Info("开始执行heap dump", zap.String("javaPID", javaPID), zap.String("dumpPath", dumpPath))
	executeTime := time.Now()

	heapDumpCmd := []string{fmt.Sprintf("jmap -dump:format=b,file=%s %s", dumpPath, javaPID)}
	dumpOutput, dumpStderr, err := executor.Execute(ctx, heapDumpCmd)
	if err != nil {
		s.GetLogger().Error("执行heap dump失败",
			zap.Error(err),
			zap.String("stdout", dumpOutput),
			zap.String("stderr", dumpStderr))
		return nil, fmt.Errorf("执行heap dump失败: %s", dumpStderr)
	}

	// 5. 验证文件是否生成
	s.GetLogger().Info("验证dump文件")
	fileInfo, err := s.GetFileInfo(ctx, namespace, podName, containerName, dumpPath)
	if err != nil {
		return nil, fmt.Errorf("heap dump执行失败，文件未生成: %w", err)
	}

	if !fileInfo.Exists {
		return nil, fmt.Errorf("heap dump文件未生成: %s", dumpPath)
	}

	// 6. 生成下载令牌（简单的token，实际应用中可以使用更安全的方式）
	downloadToken := fmt.Sprintf("%s_%s_%d", podName, filepath.Base(dumpPath), executeTime.Unix())

	result := &HeapDumpResult{
		DumpPath:      dumpPath,
		FileSize:      fileInfo.Size,
		JavaPID:       javaPID,
		ExecuteTime:   executeTime,
		CommandInfo:   fmt.Sprintf("jmap -dump:format=b,file=%s %s", dumpPath, javaPID),
		DownloadToken: downloadToken,
	}

	s.GetLogger().Info("Java Heap Dump执行成功",
		zap.String("podName", podName),
		zap.String("dumpPath", dumpPath),
		zap.String("fileSize", fileInfo.Size),
		zap.String("downloadToken", downloadToken))

	return result, nil
}

// DownloadFile 从Pod下载文件
func (s *podService) DownloadFile(ctx context.Context, namespace, podName, containerName, filePath string) ([]byte, error) {
	s.GetLogger().Info("开始下载文件",
		zap.String("namespace", namespace),
		zap.String("podName", podName),
		zap.String("filePath", filePath))

	// 创建Pod执行器
	podContext := podexecutor.PodContext{
		Namespace:     namespace,
		PodName:       podName,
		ContainerName: containerName,
	}
	executor := podexecutor.NewPodExec(s.GetLogger(), podContext)

	// 首先检查文件是否存在
	fileInfo, err := s.GetFileInfo(ctx, namespace, podName, containerName, filePath)
	if err != nil {
		return nil, fmt.Errorf("检查文件失败: %w", err)
	}

	if !fileInfo.Exists {
		return nil, fmt.Errorf("文件不存在: %s", filePath)
	}

	// 使用base64编码传输二进制文件，避免字符编码问题
	s.GetLogger().Info("开始读取文件内容")
	readCmd := []string{fmt.Sprintf("base64 %s", filePath)}
	base64Content, stderr, err := executor.Execute(ctx, readCmd)
	if err != nil {
		s.GetLogger().Error("读取文件失败", zap.Error(err), zap.String("stderr", stderr))
		return nil, fmt.Errorf("读取文件失败: %w", err)
	}

	// 解码base64内容
	fileContent, err := base64.StdEncoding.DecodeString(strings.TrimSpace(base64Content))
	if err != nil {
		s.GetLogger().Error("解码文件内容失败", zap.Error(err))
		return nil, fmt.Errorf("解码文件内容失败: %w", err)
	}

	s.GetLogger().Info("文件下载成功",
		zap.String("filePath", filePath),
		zap.Int("fileSize", len(fileContent)))

	return fileContent, nil
}

// GetFileInfo 获取文件信息
func (s *podService) GetFileInfo(ctx context.Context, namespace, podName, containerName, filePath string) (*FileInfo, error) {
	// 创建Pod执行器
	podContext := podexecutor.PodContext{
		Namespace:     namespace,
		PodName:       podName,
		ContainerName: containerName,
	}
	executor := podexecutor.NewPodExec(s.GetLogger(), podContext)

	// 检查文件是否存在并获取信息
	statCmd := []string{fmt.Sprintf("ls -la %s 2>/dev/null || echo 'FILE_NOT_EXISTS'", filePath)}
	output, _, err := executor.Execute(ctx, statCmd)
	if err != nil {
		return nil, fmt.Errorf("获取文件信息失败: %w", err)
	}

	output = strings.TrimSpace(output)
	if output == "FILE_NOT_EXISTS" || output == "" {
		return &FileInfo{
			Path:   filePath,
			Exists: false,
		}, nil
	}

	// 解析ls -la输出
	// 格式: -rw-r--r-- 1 <USER> <GROUP> 1234567 Dec 25 10:30 filename
	parts := strings.Fields(output)
	if len(parts) < 5 {
		return &FileInfo{
			Path:   filePath,
			Exists: true,
			Size:   "unknown",
		}, nil
	}

	size := parts[4]
	var modTime string
	if len(parts) >= 8 {
		// 组合日期和时间
		modTime = fmt.Sprintf("%s %s %s", parts[5], parts[6], parts[7])
	}

	return &FileInfo{
		Path:    filePath,
		Size:    size,
		ModTime: modTime,
		Exists:  true,
	}, nil
}

// CleanupDumpFile 清理dump文件
func (s *podService) CleanupDumpFile(ctx context.Context, namespace, podName, containerName, filePath string) error {
	s.GetLogger().Info("清理dump文件",
		zap.String("namespace", namespace),
		zap.String("podName", podName),
		zap.String("filePath", filePath))

	// 创建Pod执行器
	podContext := podexecutor.PodContext{
		Namespace:     namespace,
		PodName:       podName,
		ContainerName: containerName,
	}
	executor := podexecutor.NewPodExec(s.GetLogger(), podContext)

	// 删除文件
	deleteCmd := []string{fmt.Sprintf("rm -f %s", filePath)}
	_, stderr, err := executor.Execute(ctx, deleteCmd)
	if err != nil {
		s.GetLogger().Error("删除文件失败", zap.Error(err), zap.String("stderr", stderr))
		return fmt.Errorf("删除文件失败: %w", err)
	}

	s.GetLogger().Info("文件清理成功", zap.String("filePath", filePath))
	return nil
}

// ListPods 获取Pod列表
func (s *podService) ListPods(ctx context.Context, req *ListPodsRequest) (*ListPodsResponse, error) {
	s.GetLogger().Info("获取Pod列表",
		zap.Int64("appID", req.AppID),
		zap.Int64("envID", req.EnvID),
		zap.String("namespace", req.Namespace))

	clientset := k8sclient.GetClientSet()
	if clientset == nil {
		return nil, fmt.Errorf("无法获取Kubernetes客户端")
	}

	// 构建标签选择器
	labelSelector := labels.NewSelector()
	if req.AppID > 0 {
		requirement, err := labels.NewRequirement("app_id", "=", []string{fmt.Sprintf("%d", req.AppID)})
		if err == nil {
			labelSelector = labelSelector.Add(*requirement)
		}
	}
	if req.EnvID > 0 {
		requirement, err := labels.NewRequirement("env_id", "=", []string{fmt.Sprintf("%d", req.EnvID)})
		if err == nil {
			labelSelector = labelSelector.Add(*requirement)
		}
	}

	// 获取Pod列表
	listOptions := metav1.ListOptions{
		LabelSelector: labelSelector.String(),
	}

	podList, err := clientset.CoreV1().Pods(req.Namespace).List(ctx, listOptions)
	if err != nil {
		s.GetLogger().Error("获取Pod列表失败", zap.Error(err))
		return nil, fmt.Errorf("获取Pod列表失败: %w", err)
	}

	// 转换Pod信息
	pods := make([]*PodInfo, 0, len(podList.Items))
	for _, pod := range podList.Items {
		podInfo := s.convertPodToPodInfo(&pod)
		pods = append(pods, podInfo)
	}

	s.GetLogger().Info("获取Pod列表成功",
		zap.Int("total", len(pods)),
		zap.String("namespace", req.Namespace))

	return &ListPodsResponse{
		Pods:  pods,
		Total: len(pods),
	}, nil
}

// GetPodMetrics 获取Pod指标
func (s *podService) GetPodMetrics(ctx context.Context, namespace, podName string) (*PodMetrics, error) {
	s.GetLogger().Info("获取Pod指标",
		zap.String("namespace", namespace),
		zap.String("podName", podName))

	// 暂时返回默认指标，后续可以集成metrics-server
	// TODO: 集成Kubernetes metrics-server获取真实的CPU/内存使用率
	s.GetLogger().Warn("当前使用默认指标值，需要配置metrics-server获取真实指标")

	result := &PodMetrics{
		PodName:    podName,
		Namespace:  namespace,
		CPU:        "0m",
		Memory:     "0Mi",
		Timestamp:  time.Now(),
		Containers: []ContainerMetrics{},
	}

	s.GetLogger().Info("获取Pod指标完成（默认值）",
		zap.String("podName", podName),
		zap.String("cpu", result.CPU),
		zap.String("memory", result.Memory))

	return result, nil
}

// convertPodToPodInfo 将Kubernetes Pod对象转换为PodInfo
func (s *podService) convertPodToPodInfo(pod *corev1.Pod) *PodInfo {
	// 计算重启次数
	var restarts int32
	for _, containerStatus := range pod.Status.ContainerStatuses {
		restarts += containerStatus.RestartCount
	}

	// 计算Ready状态
	readyContainers := 0
	totalContainers := len(pod.Spec.Containers)
	for _, containerStatus := range pod.Status.ContainerStatuses {
		if containerStatus.Ready {
			readyContainers++
		}
	}
	ready := fmt.Sprintf("%d/%d", readyContainers, totalContainers)

	// 计算Age
	age := time.Since(pod.CreationTimestamp.Time)
	ageStr := formatDuration(age)

	// 获取Node名称
	nodeName := pod.Spec.NodeName
	if nodeName == "" {
		nodeName = "Unknown"
	}

	// 获取Pod IP
	podIP := pod.Status.PodIP
	if podIP == "" {
		podIP = "Pending"
	}

	return &PodInfo{
		ID:          string(pod.UID),
		Name:        pod.Name,
		Namespace:   pod.Namespace,
		Status:      string(pod.Status.Phase),
		Restarts:    restarts,
		Age:         ageStr,
		Ready:       ready,
		IP:          podIP,
		Node:        nodeName,
		CPU:         "0m",  // 默认值，需要通过GetPodMetrics获取
		Memory:      "0Mi", // 默认值，需要通过GetPodMetrics获取
		CreateTime:  pod.CreationTimestamp.Format("2006-01-02 15:04:05"),
		Labels:      pod.Labels,
		Annotations: pod.Annotations,
	}
}

// formatDuration 格式化时间间隔
func formatDuration(d time.Duration) string {
	if d < time.Minute {
		return fmt.Sprintf("%ds", int(d.Seconds()))
	}
	if d < time.Hour {
		return fmt.Sprintf("%dm", int(d.Minutes()))
	}
	if d < 24*time.Hour {
		return fmt.Sprintf("%dh", int(d.Hours()))
	}
	return fmt.Sprintf("%dd", int(d.Hours()/24))
}

// StartArthas 启动Arthas
func (s *podService) StartArthas(ctx context.Context, req *StartArthasRequest) (*ArthasStatusResponse, error) {
	s.GetLogger().Info("启动Arthas",
		zap.String("namespace", req.Namespace),
		zap.String("podName", req.PodName),
		zap.String("containerName", req.ContainerName))

	// 检查是否已经在运行
	statusKey := fmt.Sprintf("%s/%s", req.Namespace, req.PodName)
	s.statusMutex.RLock()
	if status, exists := s.arthasStatusCache[statusKey]; exists && status.Status == "running" {
		s.statusMutex.RUnlock()
		s.GetLogger().Info("Arthas已在运行", zap.String("podName", req.PodName))
		return status, nil
	}
	s.statusMutex.RUnlock()

	// 设置默认值
	containerName := req.ContainerName
	if containerName == "" {
		containerName = "app" // 默认容器名
	}
	httpPort := req.HttpPort
	if httpPort == 0 {
		httpPort = 3658
	}
	telnetPort := req.TelnetPort
	if telnetPort == 0 {
		telnetPort = 3658
	}
	sessionTimeout := req.SessionTimeout
	if sessionTimeout == 0 {
		sessionTimeout = 1800
	}

	// 创建初始状态
	status := &ArthasStatusResponse{
		PodName:        req.PodName,
		Namespace:      req.Namespace,
		Status:         "starting",
		HttpPort:       httpPort,
		TelnetPort:     telnetPort,
		WebConsoleURL:  fmt.Sprintf("/api/pods/arthas/ws/%s/%s", req.Namespace, req.PodName),
		StartTime:      time.Now(),
		LastActiveTime: time.Now(),
	}

	// 保存状态
	s.statusMutex.Lock()
	s.arthasStatusCache[statusKey] = status
	s.statusMutex.Unlock()

	// 创建Pod执行器
	podContext := podexecutor.PodContext{
		Namespace:     req.Namespace,
		PodName:       req.PodName,
		ContainerName: containerName,
	}
	executor := podexecutor.NewPodExec(s.GetLogger(), podContext)

	// 1. 检测Java进程
	javaPID := req.JavaPID
	if javaPID == "" {
		processes, err := s.ListJavaProcesses(ctx, req.Namespace, req.PodName, containerName)
		if err != nil {
			return s.updateArthasStatus(statusKey, "error", "", "检测Java进程失败: "+err.Error())
		}
		if len(processes) == 0 {
			return s.updateArthasStatus(statusKey, "error", "", "未找到Java进程")
		}
		javaPID = processes[0].PID
		s.GetLogger().Info("自动检测到Java进程", zap.String("pid", javaPID))
	}

	// 2. 检查是否已有Arthas在运行
	checkCmd := []string{"ps aux | grep arthas-core | grep -v grep || echo 'NO_ARTHAS'"}
	output, _, err := executor.Execute(ctx, checkCmd)
	if err == nil && !strings.Contains(output, "NO_ARTHAS") {
		s.GetLogger().Warn("检测到已有Arthas在运行，先尝试停止")
		stopCmd := []string{"pkill -f arthas-core"}
		executor.Execute(ctx, stopCmd)
		time.Sleep(2 * time.Second)
	}

	// 3. 下载arthas-boot.jar
	s.GetLogger().Info("下载Arthas")
	downloadCmd := []string{
		"cd /tmp && " +
			"if [ ! -f arthas-boot.jar ]; then " +
			"curl -L -o arthas-boot.jar https://arthas.aliyun.com/arthas-boot.jar || " +
			"wget -O arthas-boot.jar https://arthas.aliyun.com/arthas-boot.jar; " +
			"fi && " +
			"ls -la arthas-boot.jar",
	}
	downloadOutput, downloadStderr, err := executor.Execute(ctx, downloadCmd)
	if err != nil {
		s.GetLogger().Error("下载Arthas失败", zap.Error(err), zap.String("stderr", downloadStderr))
		return s.updateArthasStatus(statusKey, "error", "", "下载Arthas失败: "+downloadStderr)
	}
	s.GetLogger().Info("Arthas下载完成", zap.String("output", downloadOutput))

	// 4. 启动Arthas
	s.GetLogger().Info("启动Arthas服务", zap.String("javaPID", javaPID))
	startCmd := []string{
		fmt.Sprintf("cd /tmp && nohup java -jar arthas-boot.jar "+
			"--target-ip 0.0.0.0 "+
			"--http-port %d "+
			"--telnet-port %d "+
			"--session-timeout %d "+
			"--select %s "+
			"> arthas.log 2>&1 & echo $!",
			httpPort, telnetPort, sessionTimeout, javaPID),
	}
	startOutput, startStderr, err := executor.Execute(ctx, startCmd)
	if err != nil {
		s.GetLogger().Error("启动Arthas失败", zap.Error(err), zap.String("stderr", startStderr))
		return s.updateArthasStatus(statusKey, "error", "", "启动Arthas失败: "+startStderr)
	}

	arthasPID := strings.TrimSpace(startOutput)
	s.GetLogger().Info("Arthas启动命令已执行", zap.String("arthasPID", arthasPID))

	// 5. 等待Arthas启动完成（检查HTTP端口）
	s.GetLogger().Info("等待Arthas Web服务启动")
	maxRetries := 30
	for i := 0; i < maxRetries; i++ {
		checkCmd := []string{fmt.Sprintf("curl -s http://localhost:%d/arthas-output/ | head -1", httpPort)}
		checkOutput, _, checkErr := executor.Execute(ctx, checkCmd)
		if checkErr == nil && !strings.Contains(checkOutput, "Connection refused") && checkOutput != "" {
			s.GetLogger().Info("Arthas Web服务启动成功")
			status.Status = "running"
			status.JavaPID = javaPID
			status.LastActiveTime = time.Now()

			s.statusMutex.Lock()
			s.arthasStatusCache[statusKey] = status
			s.statusMutex.Unlock()

			return status, nil
		}
		time.Sleep(1 * time.Second)
	}

	// 启动超时
	return s.updateArthasStatus(statusKey, "error", javaPID, "Arthas启动超时")
}

// StopArthas 停止Arthas
func (s *podService) StopArthas(ctx context.Context, namespace, podName string) error {
	s.GetLogger().Info("停止Arthas",
		zap.String("namespace", namespace),
		zap.String("podName", podName))

	statusKey := fmt.Sprintf("%s/%s", namespace, podName)

	// 更新状态为停止中
	s.statusMutex.Lock()
	if status, exists := s.arthasStatusCache[statusKey]; exists {
		status.Status = "stopping"
		status.LastActiveTime = time.Now()
	}
	s.statusMutex.Unlock()

	// 创建Pod执行器
	podContext := podexecutor.PodContext{
		Namespace:     namespace,
		PodName:       podName,
		ContainerName: "app", // 使用默认容器名
	}
	executor := podexecutor.NewPodExec(s.GetLogger(), podContext)

	// 停止Arthas进程
	stopCmd := []string{"pkill -f arthas-core || true"}
	stopOutput, stopStderr, err := executor.Execute(ctx, stopCmd)
	if err != nil {
		s.GetLogger().Error("停止Arthas失败", zap.Error(err), zap.String("stderr", stopStderr))
		s.updateArthasStatus(statusKey, "error", "", "停止Arthas失败: "+stopStderr)
		return fmt.Errorf("停止Arthas失败: %w", err)
	}

	s.GetLogger().Info("Arthas停止成功", zap.String("output", stopOutput))

	// 清理状态缓存
	s.statusMutex.Lock()
	delete(s.arthasStatusCache, statusKey)
	s.statusMutex.Unlock()

	return nil
}

// GetArthasStatus 获取Arthas状态
func (s *podService) GetArthasStatus(ctx context.Context, namespace, podName string) (*ArthasStatusResponse, error) {
	statusKey := fmt.Sprintf("%s/%s", namespace, podName)

	s.statusMutex.RLock()
	status, exists := s.arthasStatusCache[statusKey]
	s.statusMutex.RUnlock()

	if !exists {
		return &ArthasStatusResponse{
			PodName:   podName,
			Namespace: namespace,
			Status:    "stopped",
		}, nil
	}

	// 如果状态是运行中，验证进程是否真的在运行
	if status.Status == "running" {
		podContext := podexecutor.PodContext{
			Namespace:     namespace,
			PodName:       podName,
			ContainerName: "app",
		}
		executor := podexecutor.NewPodExec(s.GetLogger(), podContext)

		checkCmd := []string{"ps aux | grep arthas-core | grep -v grep || echo 'NO_ARTHAS'"}
		output, _, err := executor.Execute(ctx, checkCmd)
		if err != nil || strings.Contains(output, "NO_ARTHAS") {
			// 进程已不存在，更新状态
			s.statusMutex.Lock()
			delete(s.arthasStatusCache, statusKey)
			s.statusMutex.Unlock()

			return &ArthasStatusResponse{
				PodName:   podName,
				Namespace: namespace,
				Status:    "stopped",
			}, nil
		}

		// 更新最后活动时间
		status.LastActiveTime = time.Now()
		s.statusMutex.Lock()
		s.arthasStatusCache[statusKey] = status
		s.statusMutex.Unlock()
	}

	return status, nil
}

// ListJavaProcesses 列出Java进程
func (s *podService) ListJavaProcesses(ctx context.Context, namespace, podName, containerName string) ([]*JavaProcessInfo, error) {
	s.GetLogger().Info("列出Java进程",
		zap.String("namespace", namespace),
		zap.String("podName", podName))

	if containerName == "" {
		containerName = "app"
	}

	// 创建Pod执行器
	podContext := podexecutor.PodContext{
		Namespace:     namespace,
		PodName:       podName,
		ContainerName: containerName,
	}
	executor := podexecutor.NewPodExec(s.GetLogger(), podContext)

	// 查找Java进程，使用多种方法确保找到
	findJavaCmd := []string{
		"{ jps -l 2>/dev/null || ps aux | grep java | grep -v grep | awk '{print $2 \" \" $11}'; } | grep -v '^$'",
	}

	output, stderr, err := executor.Execute(ctx, findJavaCmd)
	if err != nil {
		s.GetLogger().Error("查找Java进程失败", zap.Error(err), zap.String("stderr", stderr))
		return nil, fmt.Errorf("查找Java进程失败: %w", err)
	}

	var processes []*JavaProcessInfo
	lines := strings.Split(strings.TrimSpace(output), "\n")

	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		parts := strings.Fields(line)
		if len(parts) >= 2 {
			pid := parts[0]
			mainClass := parts[1]

			// 过滤掉jps本身和其他工具
			if strings.Contains(mainClass, "jps") ||
				strings.Contains(mainClass, "arthas-boot") ||
				strings.Contains(mainClass, "grep") {
				continue
			}

			processes = append(processes, &JavaProcessInfo{
				PID:       pid,
				MainClass: mainClass,
				Command:   strings.Join(parts[1:], " "),
			})
		}
	}

	s.GetLogger().Info("找到Java进程", zap.Int("count", len(processes)))
	return processes, nil
}

// CleanupArthas 清理Arthas资源
func (s *podService) CleanupArthas(ctx context.Context, namespace, podName string) error {
	s.GetLogger().Info("清理Arthas资源",
		zap.String("namespace", namespace),
		zap.String("podName", podName))

	// 先停止Arthas
	if err := s.StopArthas(ctx, namespace, podName); err != nil {
		s.GetLogger().Warn("停止Arthas时出错", zap.Error(err))
	}

	// 创建Pod执行器清理临时文件
	podContext := podexecutor.PodContext{
		Namespace:     namespace,
		PodName:       podName,
		ContainerName: "app",
	}
	executor := podexecutor.NewPodExec(s.GetLogger(), podContext)

	// 清理临时文件
	cleanupCmd := []string{
		"rm -f /tmp/arthas-boot.jar /tmp/arthas.log /tmp/arthas-output/* 2>/dev/null || true",
	}
	_, _, err := executor.Execute(ctx, cleanupCmd)
	if err != nil {
		s.GetLogger().Warn("清理临时文件失败", zap.Error(err))
	}

	s.GetLogger().Info("Arthas资源清理完成")
	return nil
}

// updateArthasStatus 更新Arthas状态的辅助方法
func (s *podService) updateArthasStatus(statusKey, status, javaPID, errorMessage string) (*ArthasStatusResponse, error) {
	s.statusMutex.Lock()
	defer s.statusMutex.Unlock()

	if cachedStatus, exists := s.arthasStatusCache[statusKey]; exists {
		cachedStatus.Status = status
		cachedStatus.JavaPID = javaPID
		cachedStatus.ErrorMessage = errorMessage
		cachedStatus.LastActiveTime = time.Now()

		if status == "error" {
			s.GetLogger().Error("Arthas状态错误",
				zap.String("podName", cachedStatus.PodName),
				zap.String("error", errorMessage))
			return cachedStatus, fmt.Errorf(errorMessage)
		}

		return cachedStatus, nil
	}

	return nil, fmt.Errorf("未找到Arthas状态")
}

// startArthasCleanupRoutine 启动Arthas自动清理例程
func (s *podService) startArthasCleanupRoutine() {
	ticker := time.NewTicker(5 * time.Minute) // 每5分钟检查一次
	defer ticker.Stop()

	s.GetLogger().Info("Arthas自动清理例程已启动")

	for {
		select {
		case <-ticker.C:
			s.cleanupInactiveArthasInstances()
		}
	}
}

// cleanupInactiveArthasInstances 清理非活跃的Arthas实例
func (s *podService) cleanupInactiveArthasInstances() {
	s.statusMutex.RLock()

	var toCleanup []string
	now := time.Now()

	for key, status := range s.arthasStatusCache {
		// 检查最后活动时间，超过30分钟的实例标记为清理
		if status.Status == "running" && now.Sub(status.LastActiveTime) > 30*time.Minute {
			toCleanup = append(toCleanup, key)
			s.GetLogger().Info("发现非活跃Arthas实例",
				zap.String("key", key),
				zap.String("podName", status.PodName),
				zap.Time("lastActive", status.LastActiveTime))
		}

		// 清理错误状态超过1小时的实例
		if (status.Status == "error" || status.Status == "stopped") && now.Sub(status.LastActiveTime) > 1*time.Hour {
			toCleanup = append(toCleanup, key)
			s.GetLogger().Info("发现过期错误状态Arthas实例",
				zap.String("key", key),
				zap.String("status", status.Status))
		}
	}

	s.statusMutex.RUnlock()

	// 执行清理
	for _, key := range toCleanup {
		s.statusMutex.RLock()
		status, exists := s.arthasStatusCache[key]
		s.statusMutex.RUnlock()

		if !exists {
			continue
		}

		s.GetLogger().Info("开始清理非活跃Arthas实例",
			zap.String("namespace", status.Namespace),
			zap.String("podName", status.PodName))

		ctx, cancel := context.WithTimeout(context.Background(), 2*time.Minute)

		// 尝试清理Arthas资源
		if err := s.CleanupArthas(ctx, status.Namespace, status.PodName); err != nil {
			s.GetLogger().Error("自动清理Arthas失败",
				zap.String("namespace", status.Namespace),
				zap.String("podName", status.PodName),
				zap.Error(err))
		} else {
			s.GetLogger().Info("自动清理Arthas成功",
				zap.String("namespace", status.Namespace),
				zap.String("podName", status.PodName))
		}

		cancel()
	}

	if len(toCleanup) > 0 {
		s.GetLogger().Info("Arthas自动清理完成", zap.Int("cleanedCount", len(toCleanup)))
	}
}

// ===================== Terminal相关方法实现 =====================

// StartTerminal 启动终端会话
func (s *podService) StartTerminal(ctx context.Context, req *StartTerminalRequest) (*TerminalStatusResponse, error) {
	s.GetLogger().Info("启动终端会话",
		zap.String("namespace", req.Namespace),
		zap.String("podName", req.PodName),
		zap.String("containerName", req.ContainerName))

	// 设置默认值
	if req.Shell == "" {
		req.Shell = "/bin/bash"
	}
	if req.Cols == 0 {
		req.Cols = 80
	}
	if req.Rows == 0 {
		req.Rows = 24
	}
	if req.Timeout == 0 {
		req.Timeout = 3600 // 默认1小时
	}
	if req.ContainerName == "" {
		req.ContainerName = "app"
	}

	// 生成唯一的会话ID
	sessionID := fmt.Sprintf("%s-%s-%d", req.PodName, req.ContainerName, time.Now().Unix())

	// 创建终端状态响应
	terminalStatus := &TerminalStatusResponse{
		PodName:        req.PodName,
		Namespace:      req.Namespace,
		ContainerName:  req.ContainerName,
		Status:         "starting",
		SessionID:      sessionID,
		Shell:          req.Shell,
		WebSocketURL:   fmt.Sprintf("/api/pods/terminal/ws/%s/%s?session_id=%s", req.Namespace, req.PodName, sessionID),
		StartTime:      time.Now(),
		LastActiveTime: time.Now(),
		Cols:           req.Cols,
		Rows:           req.Rows,
	}

	// 缓存终端状态
	statusKey := fmt.Sprintf("%s/%s/%s", req.Namespace, req.PodName, sessionID)
	s.statusMutex.Lock()
	s.terminalStatusCache[statusKey] = terminalStatus
	s.statusMutex.Unlock()

	// 验证Pod是否存在和可访问
	if err := s.validatePodAccess(ctx, req.Namespace, req.PodName, req.ContainerName); err != nil {
		terminalStatus.Status = "error"
		terminalStatus.ErrorMessage = fmt.Sprintf("Pod访问验证失败: %v", err)
		return terminalStatus, err
	}

	// 设置状态为运行中
	terminalStatus.Status = "running"

	s.GetLogger().Info("终端会话创建成功",
		zap.String("sessionID", sessionID),
		zap.String("webSocketURL", terminalStatus.WebSocketURL))

	return terminalStatus, nil
}

// StopTerminal 停止终端会话
func (s *podService) StopTerminal(ctx context.Context, namespace, podName, sessionID string) error {
	s.GetLogger().Info("停止终端会话",
		zap.String("namespace", namespace),
		zap.String("podName", podName),
		zap.String("sessionID", sessionID))

	statusKey := fmt.Sprintf("%s/%s/%s", namespace, podName, sessionID)

	s.statusMutex.Lock()
	defer s.statusMutex.Unlock()

	if status, exists := s.terminalStatusCache[statusKey]; exists {
		status.Status = "stopped"
		status.LastActiveTime = time.Now()
		// 不立即删除缓存，让自动清理程序处理
	}

	s.GetLogger().Info("终端会话已停止", zap.String("sessionID", sessionID))
	return nil
}

// GetTerminalStatus 获取终端状态
func (s *podService) GetTerminalStatus(ctx context.Context, namespace, podName, sessionID string) (*TerminalStatusResponse, error) {
	statusKey := fmt.Sprintf("%s/%s/%s", namespace, podName, sessionID)

	s.statusMutex.RLock()
	defer s.statusMutex.RUnlock()

	if status, exists := s.terminalStatusCache[statusKey]; exists {
		// 更新最后活动时间（如果是运行状态）
		if status.Status == "running" {
			status.LastActiveTime = time.Now()
		}
		return status, nil
	}

	return nil, fmt.Errorf("未找到终端会话: %s", sessionID)
}

// ResizeTerminal 调整终端大小
func (s *podService) ResizeTerminal(ctx context.Context, req *ResizeTerminalRequest) error {
	s.GetLogger().Info("调整终端大小",
		zap.String("namespace", req.Namespace),
		zap.String("podName", req.PodName),
		zap.String("sessionID", req.SessionID),
		zap.Uint16("cols", req.Cols),
		zap.Uint16("rows", req.Rows))

	statusKey := fmt.Sprintf("%s/%s/%s", req.Namespace, req.PodName, req.SessionID)

	s.statusMutex.Lock()
	defer s.statusMutex.Unlock()

	if status, exists := s.terminalStatusCache[statusKey]; exists {
		status.Cols = req.Cols
		status.Rows = req.Rows
		status.LastActiveTime = time.Now()

		s.GetLogger().Info("终端大小已调整",
			zap.String("sessionID", req.SessionID),
			zap.Uint16("newCols", req.Cols),
			zap.Uint16("newRows", req.Rows))

		return nil
	}

	return fmt.Errorf("未找到终端会话: %s", req.SessionID)
}

// CleanupTerminal 清理终端资源
func (s *podService) CleanupTerminal(ctx context.Context, namespace, podName string) error {
	s.GetLogger().Info("清理终端资源",
		zap.String("namespace", namespace),
		zap.String("podName", podName))

	s.statusMutex.Lock()
	defer s.statusMutex.Unlock()

	prefix := fmt.Sprintf("%s/%s/", namespace, podName)
	var cleanedCount int

	for key := range s.terminalStatusCache {
		if strings.HasPrefix(key, prefix) {
			delete(s.terminalStatusCache, key)
			cleanedCount++
		}
	}

	s.GetLogger().Info("终端资源清理完成",
		zap.Int("cleanedCount", cleanedCount))

	return nil
}

// validatePodAccess 验证Pod访问权限
func (s *podService) validatePodAccess(ctx context.Context, namespace, podName, containerName string) error {
	// 获取K8s客户端
	clientSet := k8sclient.GetClientSet()

	// 检查Pod是否存在
	pod, err := clientSet.CoreV1().Pods(namespace).Get(ctx, podName, metav1.GetOptions{})
	if err != nil {
		return fmt.Errorf("获取Pod失败: %w", err)
	}

	// 检查Pod状态
	if pod.Status.Phase != corev1.PodRunning {
		return fmt.Errorf("Pod状态不是Running: %s", pod.Status.Phase)
	}

	// 检查容器是否存在
	if containerName != "" {
		found := false
		for _, container := range pod.Spec.Containers {
			if container.Name == containerName {
				found = true
				break
			}
		}
		if !found {
			return fmt.Errorf("容器 %s 不存在", containerName)
		}
	}

	return nil
}

// startTerminalCleanupRoutine 启动终端自动清理例程
func (s *podService) startTerminalCleanupRoutine() {
	ticker := time.NewTicker(10 * time.Minute) // 每10分钟检查一次
	defer ticker.Stop()

	s.GetLogger().Info("终端自动清理例程已启动")

	for {
		select {
		case <-ticker.C:
			s.cleanupInactiveTerminalSessions()
		}
	}
}

// cleanupInactiveTerminalSessions 清理非活跃的终端会话
func (s *podService) cleanupInactiveTerminalSessions() {
	s.statusMutex.Lock()
	defer s.statusMutex.Unlock()

	var toCleanup []string
	now := time.Now()

	for key, status := range s.terminalStatusCache {
		// 检查最后活动时间，超过2小时的会话标记为清理
		if status.Status == "running" && now.Sub(status.LastActiveTime) > 2*time.Hour {
			toCleanup = append(toCleanup, key)
			s.GetLogger().Info("发现非活跃终端会话",
				zap.String("key", key),
				zap.String("sessionID", status.SessionID),
				zap.Time("lastActive", status.LastActiveTime))
		}

		// 清理停止状态超过30分钟的会话
		if (status.Status == "stopped" || status.Status == "error") && now.Sub(status.LastActiveTime) > 30*time.Minute {
			toCleanup = append(toCleanup, key)
			s.GetLogger().Info("发现过期终端会话",
				zap.String("key", key),
				zap.String("status", status.Status))
		}
	}

	// 执行清理
	for _, key := range toCleanup {
		delete(s.terminalStatusCache, key)
	}

	if len(toCleanup) > 0 {
		s.GetLogger().Info("终端自动清理完成", zap.Int("cleanedCount", len(toCleanup)))
	}
}

// ===================== Pod重启功能实现 =====================

// ===================== 优化后的重启功能实现 =====================

// RestartPods 统一的Pod重启方法
func (s *podService) RestartPods(ctx context.Context, req *dto.PodRestartRequest) (*restart.RestartResult, error) {
	s.GetLogger().Info("开始执行Pod重启",
		zap.String("namespace", req.Namespace),
		zap.Int("podCount", len(req.PodNames)),
		zap.String("strategy", string(req.Strategy)))

	// 验证和设置默认配置
	config := req.Config
	if config == nil {
		config = restart.DefaultConfig()
	}
	if err := config.Validate(); err != nil {
		return nil, restart.NewRestartError("", req.Namespace, "validate_config", err)
	}

	// 创建结果对象
	result := restart.NewRestartResult()

	// 获取K8s客户端
	clientSet := k8sclient.GetClientSet()

	var podsToRestart []corev1.Pod

	// 如果没有指定Pod名称，获取命名空间下所有Pod
	if len(req.PodNames) == 0 {
		podList, err := clientSet.CoreV1().Pods(req.Namespace).List(ctx, metav1.ListOptions{})
		if err != nil {
			s.GetLogger().Error("获取Pod列表失败", zap.Error(err))
			return nil, restart.NewRestartError("", req.Namespace, "list_pods", err)
		}
		podsToRestart = podList.Items
	} else {
		// 验证指定的Pod是否存在
		for _, podName := range req.PodNames {
			pod, err := clientSet.CoreV1().Pods(req.Namespace).Get(ctx, podName, metav1.GetOptions{})
			if err != nil {
				s.GetLogger().Error("获取Pod失败", zap.String("podName", podName), zap.Error(err))
				result.AddFailure(podName, "pod_not_found", err.Error())
				continue
			}
			podsToRestart = append(podsToRestart, *pod)
		}
	}

	if len(podsToRestart) == 0 {
		result.Complete()
		result.Message = "没有找到需要重启的Pod"
		return result, nil
	}

	s.GetLogger().Info("找到待重启的Pod", zap.Int("count", len(podsToRestart)))

	// 并发控制
	semaphore := make(chan struct{}, config.MaxConcurrent)
	var wg sync.WaitGroup
	var mu sync.Mutex

	// 创建带超时的上下文
	restartCtx, cancel := context.WithTimeout(ctx, config.Timeout)
	defer cancel()

	// 并发重启Pod
	for _, pod := range podsToRestart {
		wg.Add(1)
		go func(pod corev1.Pod) {
			defer wg.Done()

			// 获取信号量
			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			podName := pod.Name
			event := restart.RestartEvent{
				Namespace: req.Namespace,
				PodName:   podName,
				Strategy:  req.Strategy,
				StartTime: time.Now(),
			}

			s.GetLogger().Info("开始重启Pod", zap.String("podName", podName))

			err := s.deletePodWithStrategy(restartCtx, req.Namespace, podName, config)

			event.EndTime = time.Now()
			event.Duration = event.EndTime.Sub(event.StartTime)

			mu.Lock()
			if err != nil {
				s.GetLogger().Error("重启Pod失败", zap.String("podName", podName), zap.Error(err))
				result.AddFailure(podName, "restart_failed", err.Error())
				event.Success = false
				event.Error = err.Error()
			} else {
				s.GetLogger().Info("Pod重启成功", zap.String("podName", podName))
				result.AddSuccess(podName)
				event.Success = true
			}
			result.Events = append(result.Events, event)
			mu.Unlock()
		}(pod)
	}

	// 等待所有重启操作完成
	wg.Wait()
	result.Complete()

	s.GetLogger().Info("Pod重启操作完成",
		zap.Int("totalPods", result.TotalPods),
		zap.Int("successfulPods", result.SuccessfulPods),
		zap.Int("failedPods", len(result.FailedPods)),
		zap.Duration("duration", result.Duration))

	return result, nil
}

// deletePodWithStrategy 根据策略删除Pod
func (s *podService) deletePodWithStrategy(ctx context.Context, namespace, podName string, config *restart.RestartConfig) error {
	clientSet := k8sclient.GetClientSet()

	// 构建删除选项
	deleteOptions := metav1.DeleteOptions{}

	if config.Strategy == restart.RestartStrategyGraceful && config.GracePeriodSeconds != nil {
		deleteOptions.GracePeriodSeconds = config.GracePeriodSeconds
	} else if config.Strategy == restart.RestartStrategyForce {
		// 强制删除，设置优雅期为0
		gracePeriod := int64(0)
		deleteOptions.GracePeriodSeconds = &gracePeriod
	}

	// 删除Pod
	err := clientSet.CoreV1().Pods(namespace).Delete(ctx, podName, deleteOptions)
	if err != nil {
		return fmt.Errorf("删除Pod失败: %w", err)
	}

	return nil
}
