package service

import (
	"context"
	"encoding/json"
	"fmt"

	"ci-gateway/internal/dto"
	"ci-gateway/internal/model"
	"ci-gateway/internal/repository"

	"go.uber.org/zap"
)

// BuildConfigService 构建配置服务接口
type BuildConfigService interface {
	// 构建模板相关
	GetBuildTemplates(ctx context.Context, language string) ([]*dto.BuildTemplateResponse, error)
	GetBuildTemplateByID(ctx context.Context, id int64) (*dto.BuildTemplateResponse, error)
	GetDefaultTemplate(ctx context.Context, language string) (*dto.BuildTemplateResponse, error)

	// 项目构建配置相关
	GetProjectBuildConfig(ctx context.Context, appID, envID int64) (*dto.ProjectBuildConfigResponse, error)
	GetConfigByGroupID(ctx context.Context, groupID int64) (*model.BuildConfig, error)
	CreateProjectBuildConfig(ctx context.Context, req *dto.CreateProjectBuildConfigRequest, userID int64) (*dto.ProjectBuildConfigResponse, error)
	UpdateProjectBuildConfig(ctx context.Context, appID, envID int64, req *dto.UpdateProjectBuildConfigRequest, userID int64) (*dto.ProjectBuildConfigResponse, error)
	DeleteProjectBuildConfig(ctx context.Context, appID, envID int64) error

	// 构建配置预览
	PreviewBuildConfig(ctx context.Context, req *dto.BuildConfigPreviewRequest) (*dto.BuildConfigPreviewResponse, error)
}

type buildConfigService struct {
	*Service
	buildTemplateRepo repository.BuildTemplateRepository
	buildConfigRepo   repository.BuildConfigRepository
}

// NewBuildConfigService 创建构建配置服务实例
func NewBuildConfigService(
	service *Service,
	buildTemplateRepo repository.BuildTemplateRepository,
	buildConfigRepo repository.BuildConfigRepository,
) BuildConfigService {
	return &buildConfigService{
		Service:           service,
		buildTemplateRepo: buildTemplateRepo,
		buildConfigRepo:   buildConfigRepo,
	}
}

// GetBuildTemplates 获取构建模板列表
func (s *buildConfigService) GetBuildTemplates(ctx context.Context, language string) ([]*dto.BuildTemplateResponse, error) {
	s.logger.Info("获取构建模板列表", zap.String("language", language))

	var templates []*model.BuildTemplate
	var err error

	if language != "" {
		templates, err = s.buildTemplateRepo.GetTemplatesByLanguage(language)
	} else {
		templates, err = s.buildTemplateRepo.GetAllTemplates()
	}

	if err != nil {
		s.logger.Error("获取构建模板失败", zap.Error(err))
		return nil, fmt.Errorf("获取构建模板失败: %w", err)
	}

	// 转换为响应格式
	result := make([]*dto.BuildTemplateResponse, 0, len(templates))
	for _, template := range templates {
		resp := s.convertBuildTemplateToResponse(template)
		result = append(result, resp)
	}

	return result, nil
}

// GetBuildTemplateByID 根据ID获取构建模板
func (s *buildConfigService) GetBuildTemplateByID(ctx context.Context, id int64) (*dto.BuildTemplateResponse, error) {
	s.logger.Info("获取构建模板", zap.Int64("id", id))

	template, err := s.buildTemplateRepo.GetTemplateByID(id)
	if err != nil {
		s.logger.Error("获取构建模板失败", zap.Error(err))
		return nil, fmt.Errorf("获取构建模板失败: %w", err)
	}

	if template == nil {
		return nil, fmt.Errorf("构建模板不存在")
	}

	return s.convertBuildTemplateToResponse(template), nil
}

// GetDefaultTemplate 获取指定语言的默认模板
func (s *buildConfigService) GetDefaultTemplate(ctx context.Context, language string) (*dto.BuildTemplateResponse, error) {
	s.logger.Info("获取默认构建模板", zap.String("language", language))

	template, err := s.buildTemplateRepo.GetDefaultTemplate(language)
	if err != nil {
		s.logger.Error("获取默认构建模板失败", zap.Error(err))
		return nil, fmt.Errorf("获取默认构建模板失败: %w", err)
	}

	if template == nil {
		return nil, fmt.Errorf("该语言暂无默认模板")
	}

	return s.convertBuildTemplateToResponse(template), nil
}

// GetProjectBuildConfig 获取项目构建配置
func (s *buildConfigService) GetProjectBuildConfig(ctx context.Context, appID, envID int64) (*dto.ProjectBuildConfigResponse, error) {
	config, err := s.buildConfigRepo.GetConfigByAppAndEnv(appID, envID)
	if err != nil {
		return nil, fmt.Errorf("获取项目构建配置失败: %w", err)
	}

	if config == nil {
		return nil, nil
	}

	// 获取关联的模板信息
	template, err := s.buildTemplateRepo.GetTemplateByID(config.TemplateID)
	if err != nil {
		s.logger.Warn("获取关联模板失败", zap.Error(err))
	}

	resp := s.convertBuildConfigToResponse(config)
	if template != nil {
		resp.Template = s.convertBuildTemplateToResponse(template)
	}

	return resp, nil
}

func (s *buildConfigService) GetConfigByGroupID(ctx context.Context, groupID int64) (*model.BuildConfig, error) {
	config, err := s.buildConfigRepo.GetConfigByGroupID(groupID)
	if err != nil {
		return nil, err
	}

	return config, nil
}

// CreateProjectBuildConfig 创建项目构建配置
func (s *buildConfigService) CreateProjectBuildConfig(ctx context.Context, req *dto.CreateProjectBuildConfigRequest, userID int64) (*dto.ProjectBuildConfigResponse, error) {
	s.logger.Info("创建项目构建配置",
		zap.Int64("appID", req.AppID),
		zap.Int64("envID", req.EnvID),
		zap.String("language", req.Language))

	// 检查是否已存在配置
	existingConfig, err := s.buildConfigRepo.GetConfigByAppAndEnv(req.AppID, req.EnvID)
	if err != nil {
		return nil, fmt.Errorf("检查现有配置失败: %w", err)
	}

	if existingConfig != nil {
		return nil, fmt.Errorf("该应用在此环境下已存在构建配置")
	}

	// 验证模板是否存在
	template, err := s.buildTemplateRepo.GetTemplateByID(req.TemplateID)
	if err != nil {
		return nil, fmt.Errorf("验证模板失败: %w", err)
	}

	if template == nil {
		return nil, fmt.Errorf("指定的构建模板不存在")
	}

	// 创建配置
	config := &model.BuildConfig{
		AppID:             req.AppID,
		EnvID:             req.EnvID,
		TemplateID:        req.TemplateID,
		Language:          req.Language,
		TemplateType:      req.TemplateType,
		BuildParams:       string(req.BuildParams),
		RuntimeParams:     string(req.RuntimeParams),
		CustomBuildScript: req.CustomBuildScript,
		PreBuildScript:    req.PreBuildScript,
		PostBuildScript:   req.PostBuildScript,
		CreateBy:          userID,
		UpdateBy:          userID,
	}

	err = s.buildConfigRepo.CreateConfig(config)
	if err != nil {
		s.logger.Error("创建项目构建配置失败", zap.Error(err))
		return nil, fmt.Errorf("创建项目构建配置失败: %w", err)
	}

	resp := s.convertBuildConfigToResponse(config)
	resp.Template = s.convertBuildTemplateToResponse(template)

	return resp, nil
}

// UpdateProjectBuildConfig 更新项目构建配置
func (s *buildConfigService) UpdateProjectBuildConfig(ctx context.Context, appID, envID int64, req *dto.UpdateProjectBuildConfigRequest, userID int64) (*dto.ProjectBuildConfigResponse, error) {
	s.logger.Info("更新项目构建配置", zap.Int64("appID", appID), zap.Int64("envID", envID))

	config, err := s.buildConfigRepo.GetConfigByAppAndEnv(appID, envID)
	if err != nil {
		return nil, fmt.Errorf("获取项目构建配置失败: %w", err)
	}

	if config == nil {
		return nil, fmt.Errorf("项目构建配置不存在")
	}

	// 更新字段
	if req.BuildParams != nil {
		config.BuildParams = string(req.BuildParams)
	}
	if req.RuntimeParams != nil {
		config.RuntimeParams = string(req.RuntimeParams)
	}
	if req.CustomBuildScript != "" {
		config.CustomBuildScript = req.CustomBuildScript
	}
	if req.PreBuildScript != "" {
		config.PreBuildScript = req.PreBuildScript
	}
	if req.PostBuildScript != "" {
		config.PostBuildScript = req.PostBuildScript
	}

	config.UpdateBy = userID

	err = s.buildConfigRepo.UpdateConfig(config)
	if err != nil {
		s.logger.Error("更新项目构建配置失败", zap.Error(err))
		return nil, fmt.Errorf("更新项目构建配置失败: %w", err)
	}

	// 获取关联的模板信息
	template, _ := s.buildTemplateRepo.GetTemplateByID(config.TemplateID)

	resp := s.convertBuildConfigToResponse(config)
	if template != nil {
		resp.Template = s.convertBuildTemplateToResponse(template)
	}

	return resp, nil
}

// DeleteProjectBuildConfig 删除项目构建配置
func (s *buildConfigService) DeleteProjectBuildConfig(ctx context.Context, appID, envID int64) error {
	s.logger.Info("删除项目构建配置", zap.Int64("appID", appID), zap.Int64("envID", envID))

	config, err := s.buildConfigRepo.GetConfigByAppAndEnv(appID, envID)
	if err != nil {
		return fmt.Errorf("获取项目构建配置失败: %w", err)
	}

	if config == nil {
		return fmt.Errorf("项目构建配置不存在")
	}

	err = s.buildConfigRepo.DeleteConfig(config.ID)
	if err != nil {
		s.logger.Error("删除项目构建配置失败", zap.Error(err))
		return fmt.Errorf("删除项目构建配置失败: %w", err)
	}

	return nil
}

// PreviewBuildConfig 预览构建配置
func (s *buildConfigService) PreviewBuildConfig(ctx context.Context, req *dto.BuildConfigPreviewRequest) (*dto.BuildConfigPreviewResponse, error) {
	s.logger.Info("预览构建配置", zap.String("language", req.Language), zap.String("templateType", req.TemplateType))

	// 根据语言和模板类型生成预览内容
	buildCommand := s.generateBuildCommand(req)
	dockerfileContent := s.generateDockerfileContent(req)
	workflowParams := s.generateWorkflowParams(req)

	return &dto.BuildConfigPreviewResponse{
		BuildCommand:      buildCommand,
		DockerfileContent: dockerfileContent,
		WorkflowParams:    workflowParams,
	}, nil
}

// 转换方法
func (s *buildConfigService) convertBuildTemplateToResponse(template *model.BuildTemplate) *dto.BuildTemplateResponse {
	return &dto.BuildTemplateResponse{
		ID:                   template.ID,
		Name:                 template.Name,
		Language:             template.Language,
		TemplateType:         template.TemplateType,
		DisplayName:          template.DisplayName,
		Description:          template.Description,
		Icon:                 template.Icon,
		WorkflowTemplateName: template.WorkflowTemplateName,
		DefaultParams:        json.RawMessage(template.DefaultParams),
		ParamSchema:          json.RawMessage(template.ParamSchema),
		IsDefault:            template.IsDefault,
		SortOrder:            template.SortOrder,
		Status:               template.Status,
	}
}

func (s *buildConfigService) convertBuildConfigToResponse(config *model.BuildConfig) *dto.ProjectBuildConfigResponse {
	return &dto.ProjectBuildConfigResponse{
		ID:                config.ID,
		AppID:             config.AppID,
		EnvID:             config.EnvID,
		TemplateID:        config.TemplateID,
		Language:          config.Language,
		TemplateType:      config.TemplateType,
		BuildParams:       json.RawMessage(config.BuildParams),
		RuntimeParams:     json.RawMessage(config.RuntimeParams),
		CustomBuildScript: config.CustomBuildScript,
		PreBuildScript:    config.PreBuildScript,
		PostBuildScript:   config.PostBuildScript,
		CT:                config.CT,
		UT:                config.UT,
	}
}

// 生成预览内容的辅助方法
func (s *buildConfigService) generateBuildCommand(req *dto.BuildConfigPreviewRequest) string {
	switch req.Language {
	case "java":
		return "mvn clean compile package -DskipTests"
	case "go":
		return "go build -o app ."
	case "nodejs":
		return "npm install && npm run build"
	case "python":
		return "pip install -r requirements.txt"
	case "php":
		return "composer install --no-dev --optimize-autoloader"
	default:
		return "# 构建命令将根据项目类型自动生成"
	}
}

func (s *buildConfigService) generateDockerfileContent(req *dto.BuildConfigPreviewRequest) string {
	switch req.Language {
	case "java":
		return `FROM eclipse-temurin:17-jre
WORKDIR /app
COPY target/*.jar app.jar
EXPOSE 8080
ENTRYPOINT ["java", "-jar", "app.jar"]`
	case "go":
		return `FROM golang:1.21-alpine AS builder
WORKDIR /app
COPY . .
RUN go build -o app .

FROM alpine:latest
WORKDIR /app
COPY --from=builder /app/app .
EXPOSE 8080
CMD ["./app"]`
	case "nodejs":
		return `FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
EXPOSE 3000
CMD ["npm", "start"]`
	default:
		return "# Dockerfile内容将根据语言和配置自动生成"
	}
}

func (s *buildConfigService) generateWorkflowParams(req *dto.BuildConfigPreviewRequest) string {
	params := map[string]interface{}{
		"language":       req.Language,
		"template_type":  req.TemplateType,
		"build_params":   req.BuildParams,
		"runtime_params": req.RuntimeParams,
	}

	jsonData, _ := json.MarshalIndent(params, "", "  ")
	return string(jsonData)
}
