package service

import (
	"errors"
	"fmt"

	"ci-gateway/internal/dto"
	"ci-gateway/internal/model"
	"ci-gateway/internal/repository"
	"ci-gateway/pkg/utils"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// GroupService 分组服务接口
type GroupService interface {
	// 查询操作
	GetGroupByID(id int64) (*model.Group, error)
	GetGroupByCode(code string) (*model.Group, error)
	GetGroupsByAppID(appID int64) ([]*model.Group, error)
	GetGroupsByAppIDAndEnvID(appID, envID int64) ([]*model.Group, error)
	GetDefaultGroupByAppIDAndEnvID(appID, envID int64) (*model.Group, error)

	ListGroups(appID, envID int64, code, name string) ([]*model.Group, error)
	ListGroupsDeployInfoByAppIDAndEnvID(ctx *gin.Context, appID, envID int64, code, name string) ([]*dto.GroupResponse, error)

	CountByAppID(appID int64) (int64, error)

	GetGroupsDeployInfoByAppIDAndEnvID(ctx *gin.Context, appID, envID int64) ([]*dto.GroupResponse, error)

	// 写操作
	CreateGroup(ctx *gin.Context, req *dto.CreateGroupRequest) (*model.Group, error)
	DeleteGroup(ctx *gin.Context, id int64) error
	SetDefaultGroup(ctx *gin.Context, id int64) error
}

type groupService struct {
	*Service
	groupRepo  repository.GroupRepository
	deployRepo repository.DeployTaskRepository // 这里不能为 nil
	logger     *zap.Logger
}

// NewGroupService 创建分组服务实例
func NewGroupService(service *Service, groupRepo repository.GroupRepository, deployRepo repository.DeployTaskRepository, logger *zap.Logger) GroupService {
	return &groupService{
		Service:    service,
		groupRepo:  groupRepo,
		deployRepo: deployRepo, // 这里必须传入
		logger:     logger,
	}
}

// GetGroupByID 通过ID获取部署分组
func (s *groupService) GetGroupByID(id int64) (*model.Group, error) {
	return s.groupRepo.GetGroupByID(id)
}

// GetGroupByCode 通过编码获取部署分组
func (s *groupService) GetGroupByCode(code string) (*model.Group, error) {
	return s.groupRepo.GetGroupByCode(code)
}

// GetGroupsByAppID 通过应用ID获取部署分组列表
func (s *groupService) GetGroupsByAppID(appID int64) ([]*model.Group, error) {
	return s.groupRepo.GetGroupsByAppID(appID)
}

// GetGroupsByAppIDAndEnvID 通过应用ID和环境ID获取部署分组列表
func (s *groupService) GetGroupsByAppIDAndEnvID(appID, envID int64) ([]*model.Group, error) {
	return s.groupRepo.GetGroupsByAppIDAndEnvID(appID, envID)
}

func (s *groupService) GetGroupsDeployInfoByAppIDAndEnvID(ctx *gin.Context, appID, envID int64) ([]*dto.GroupResponse, error) {
	groups, err := s.groupRepo.GetGroupsByAppIDAndEnvID(appID, envID)
	if err != nil {
		return nil, err
	}
	if len(groups) == 0 {
		return []*dto.GroupResponse{}, nil
	}

	// 收集所有分组ID
	var groupIDs []int64
	for _, g := range groups {
		groupIDs = append(groupIDs, g.ID)
	}

	// 获取每个分组的最新 DeployTaskDetail
	detailMap, err := s.deployRepo.GetLatestDeployTaskDetailsByGroupIDs(ctx, groupIDs)
	if err != nil {
		return nil, err
	}

	// 组装返回数据
	var result []*dto.GroupResponse
	for _, g := range groups {
		resp := &dto.GroupResponse{
			ID:          g.ID,
			Code:        g.Code,
			Name:        g.Name,
			Description: g.Description,
			AppID:       g.AppID,
			EnvID:       g.EnvID,
			CT:          g.CT,
			CreateBy:    g.CreateBy,
			UT:          g.UT,
			UpdateBy:    g.UpdateBy,
		}
		if detail, ok := detailMap[g.ID]; ok && detail != nil {
			resp.Semver = detail.Semver
			resp.CommitID = detail.CommitID
			resp.Status = detail.Status
			resp.IsBranch = int8(detail.IsBranch)
		}
		result = append(result, resp)
	}
	return result, nil
}

// GetDefaultGroupByAppIDAndEnvID 获取应用环境下的默认分组
func (s *groupService) GetDefaultGroupByAppIDAndEnvID(appID, envID int64) (*model.Group, error) {
	return s.groupRepo.GetDefaultGroupByAppIDAndEnvID(appID, envID)
}

// ListGroups 分页获取部署分组列表

func (s *groupService) ListGroups(appID, envID int64, code, name string) ([]*model.Group, error) {
	groups, err := s.groupRepo.ListGroups(appID, envID, code, name)
	if err != nil {
		return nil, err
	}

	return groups, nil
}

func (s *groupService) ListGroupsDeployInfoByAppIDAndEnvID(ctx *gin.Context, appID, envID int64, code, name string) ([]*dto.GroupResponse, error) {
	groups, err := s.groupRepo.ListGroups(appID, envID, code, name)
	if err != nil {
		return nil, err
	}
	if len(groups) == 0 {
		return []*dto.GroupResponse{}, nil
	}

	// 收集所有分组ID
	var groupIDs []int64
	for _, g := range groups {
		groupIDs = append(groupIDs, g.ID)
	}

	// 获取每个分组的最新 DeployTaskDetail
	detailMap, err := s.deployRepo.GetLatestDeployTaskDetailsByGroupIDs(ctx, groupIDs)
	if err != nil {
		return nil, err
	}

	// 组装返回数据
	var result []*dto.GroupResponse
	for _, g := range groups {
		resp := &dto.GroupResponse{
			ID:          g.ID,
			Code:        g.Code,
			Name:        g.Name,
			Description: g.Description,
			AppID:       g.AppID,
			EnvID:       g.EnvID,
			CT:          g.CT,
			CreateBy:    g.CreateBy,
			UT:          g.UT,
			UpdateBy:    g.UpdateBy,
		}
		if detail, ok := detailMap[g.ID]; ok && detail != nil {
			resp.Semver = detail.Semver
			resp.CommitID = detail.CommitID
			resp.Status = detail.Status
			resp.IsBranch = int8(detail.IsBranch)
		}
		result = append(result, resp)
	}
	return result, nil
}

// CountByAppID 统计应用下的分组数量
func (s *groupService) CountByAppID(appID int64) (int64, error) {
	return s.groupRepo.CountByAppID(appID)
}

// CreateGroup 创建部署分组
func (s *groupService) CreateGroup(ctx *gin.Context, req *dto.CreateGroupRequest) (*model.Group, error) {
	res, err := s.groupRepo.GetGroupsByAppIDAndEnvIDAndCode(req.AppID, req.EnvID, req.Code)
	if err != nil {
		return nil, err
	}
	if res != nil { // 更新
		fmt.Print(" 更新 更新 更新 更新 更新 更新 更新 更新 更新")
		res.Description = req.Description
		res.Code = req.Code
		res.Name = req.Name
		err = s.groupRepo.UpdateGroup(res)
		if err != nil {
			return nil, errors.New("分组添加失败")
		}
		return res, nil
	}
	fmt.Print(" 新建 新建 新建 新建")
	modelGroup := model.Group{
		AppID:       req.AppID,
		Code:        req.Code,
		Name:        req.Name,
		Description: req.Description,
		EnvID:       req.EnvID,
		// 如有其它字段可继续补充
	}

	err = s.groupRepo.CreateGroup(&modelGroup)
	if err != nil {
		return nil, errors.New("分组添加失败")
	}
	return &modelGroup, nil
}

// DeleteGroup 删除部署分组
func (s *groupService) DeleteGroup(ctx *gin.Context, id int64) error {
	// 1. 检查分组是否存在
	group, err := s.groupRepo.GetGroupByID(id)
	if err != nil {
		return err
	}
	if group == nil {
		return errors.New("分组不存在")
	}

	// 3. 获取当前用户ID
	userID := utils.GetUserIDFromContext(ctx)

	// 4. 执行删除操作
	return s.groupRepo.DeleteGroup(id, userID)
}

// SetDefaultGroup 设置默认分组
func (s *groupService) SetDefaultGroup(ctx *gin.Context, id int64) error {
	// 1. 检查分组是否存在
	group, err := s.groupRepo.GetGroupByID(id)
	if err != nil {
		return err
	}
	if group == nil {
		return errors.New("分组不存在")
	}

	// 2. 获取当前用户ID
	userID := utils.GetUserIDFromContext(ctx)

	// 3. 执行设置默认分组操作
	return s.groupRepo.SetDefaultGroup(id, group.AppID, group.EnvID, userID)
}
