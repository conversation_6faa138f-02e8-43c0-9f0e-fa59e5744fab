package service

import (
	"crypto/md5"
	"encoding/hex"
	"fmt"

	"ci-gateway/internal/dto"
	"ci-gateway/internal/model"
	configFileRepo "ci-gateway/internal/repository"

	"go.uber.org/zap"
)

type ConfigFileService interface {
	GetConfigFilesByAppAndEnvID(appID, envID int64) ([]*model.ConfigFile, error)
	GetConfigContentByFileID(fileID int64) (*model.ConfigFileContent, error)
	CreateConfigFile(req dto.CreateConfigFileRequest, userId int64) (*model.ConfigFile, error)
}

type configFileService struct {
	*Service
	configFileRepo configFileRepo.ConfigFileRepository
}

func NewConfigFileService(service *Service, configFileRepo configFileRepo.ConfigFileRepository) ConfigFileService {
	return &configFileService{
		Service:        service,
		configFileRepo: configFileRepo,
	}
}

// GetConfigFilesByAppAndEnvID 根据应用ID和环境ID获取配置文件列表（不包含content字段）
func (s *configFileService) GetConfigFilesByAppAndEnvID(appID, envID int64) ([]*model.ConfigFile, error) {

	configFiles, err := s.configFileRepo.GetConfigFilesByAppAndEnvID(appID, envID)
	if err != nil {
		return nil, fmt.Errorf("根据应用ID和环境ID获取配置文件列表失败: %w", err)
	}

	s.GetLogger().Info("成功根据应用ID和环境ID获取配置文件列表", zap.Int64("appID", appID), zap.Int64("envID", envID), zap.Int("count", len(configFiles)))
	return configFiles, nil
}

func (s *configFileService) GetConfigContentByFileID(fileID int64) (*model.ConfigFileContent, error) {
	return s.configFileRepo.GetConfigContentByFileID(fileID)
}

// CreateConfigFile 创建配置文件
func (s *configFileService) CreateConfigFile(req dto.CreateConfigFileRequest, userId int64) (*model.ConfigFile, error) {
	// 计算内容MD5
	contentMd5 := calculateMd5(req.Content)

	// 构建完整配置文件对象（包含内容）
	configFile := &model.ConfigFile{
		Name:           req.Name,
		Path:           req.Path,
		Format:         req.Format,
		AppID:          req.AppID,
		EnvID:          req.EnvID,
		GroupID:        req.GroupID,
		Description:    "",
		CurrentVersion: 1,
		CreateBy:       userId,
		UpdateBy:       userId,
		Status:         1,
		Content: model.ConfigFileContent{
			Content:  req.Content,
			Version:  1,
			MD5:      contentMd5,
			Size:     int64(len(req.Content)),
			CreateBy: userId,
			UpdateBy: userId,
			Remark:   "初始创建",
			Status:   1,
		},
	}

	// 一次性保存配置文件及其内容
	if err := s.configFileRepo.CreateConfigFileWithAssociations(configFile); err != nil {
		s.GetLogger().Error("创建配置文件失败",
			zap.String("name", req.Name),
			zap.String("path", req.Path),
			zap.Error(err))
		return nil, fmt.Errorf("创建配置文件失败: %w", err)
	}

	s.GetLogger().Info("成功创建配置文件",
		zap.String("name", configFile.Name),
		zap.String("path", configFile.Path),
		zap.Int64("id", configFile.ID))

	return configFile, nil
}

// 计算字符串的MD5值
func calculateMd5(content string) string {
	hash := md5.Sum([]byte(content))
	return hex.EncodeToString(hash[:])
}
