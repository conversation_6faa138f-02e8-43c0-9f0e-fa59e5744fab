package service

import (
	"context"
	"fmt"

	"ci-gateway/internal/model"
	"ci-gateway/internal/repository"
)

// ContainerService 容器服务接口
type ContainerService interface {
	// 查询操作
	GetContainerByGroupID(ctx context.Context, groupID int64) (*model.Container, error)
	GetContainerByAppAndEnv(ctx context.Context, appID, envID int64) (*model.Container, error)
	GetContainersByAppAndEnv(ctx context.Context, appID, envID int64) ([]*model.Container, error)

	// 写入操作
	CreateContainer(ctx context.Context, container *model.Container) (*model.Container, error)
	UpdateContainer(ctx context.Context, container *model.Container) (*model.Container, error)
	DeleteContainer(ctx context.Context, appID, envID int64) error

	// 验证和预览
	ValidateContainer(ctx context.Context, container *model.Container) (bool, []string, []string, error)
	PreviewYAML(ctx context.Context, container *model.Container) (map[string]string, error)
}

type containerService struct {
	*Service
	containerRepo repository.ContainerRepository
}

// NewContainerService 创建容器服务实例
func NewContainerService(service *Service, containerRepo repository.ContainerRepository) ContainerService {
	return &containerService{
		Service:       service,
		containerRepo: containerRepo,
	}
}

func (s *containerService) GetContainerByGroupID(ctx context.Context, groupID int64) (*model.Container, error) {
	return s.containerRepo.GetContainerByGroupID(ctx, groupID)
}

func (s *containerService) GetContainerByAppAndEnv(ctx context.Context, appID, envID int64) (*model.Container, error) {
	return s.containerRepo.GetContainerByAppAndEnv(ctx, appID, envID)
}

func (s *containerService) GetContainersByAppAndEnv(ctx context.Context, appID, envID int64) ([]*model.Container, error) {
	return s.containerRepo.GetContainersByAppAndEnv(ctx, appID, envID)
}

func (s *containerService) CreateContainer(ctx context.Context, container *model.Container) (*model.Container, error) {
	// 验证配置
	valid, errors, _, err := s.ValidateContainer(ctx, container)
	if err != nil {
		return nil, fmt.Errorf("验证容器配置失败: %v", err)
	}
	if !valid {
		return nil, fmt.Errorf("容器配置验证失败: %v", errors)
	}

	// 检查是否已存在配置
	existing, err := s.containerRepo.GetContainerByAppAndEnv(ctx, container.AppID, container.EnvID)
	if err != nil {
		return nil, fmt.Errorf("检查现有配置失败: %v", err)
	}
	if existing != nil {
		return nil, fmt.Errorf("应用ID %d 在环境ID %d 下已存在容器配置", container.AppID, container.EnvID)
	}

	// 创建配置
	err = s.containerRepo.CreateContainer(ctx, container)
	if err != nil {
		return nil, fmt.Errorf("创建容器配置失败: %v", err)
	}

	return container, nil
}

func (s *containerService) UpdateContainer(ctx context.Context, container *model.Container) (*model.Container, error) {
	// 验证配置
	valid, errors, _, err := s.ValidateContainer(ctx, container)
	if err != nil {
		return nil, fmt.Errorf("验证容器配置失败: %v", err)
	}
	if !valid {
		return nil, fmt.Errorf("容器配置验证失败: %v", errors)
	}

	// 检查配置是否存在
	existing, err := s.containerRepo.GetContainerByAppAndEnv(ctx, container.AppID, container.EnvID)
	if err != nil {
		return nil, fmt.Errorf("检查现有配置失败: %v", err)
	}
	if existing == nil {
		return nil, fmt.Errorf("应用ID %d 在环境ID %d 下不存在容器配置", container.AppID, container.EnvID)
	}

	// 保持ID不变
	container.ID = existing.ID

	// 更新配置
	err = s.containerRepo.UpdateContainer(ctx, container)
	if err != nil {
		return nil, fmt.Errorf("更新容器配置失败: %v", err)
	}

	return container, nil
}

func (s *containerService) DeleteContainer(ctx context.Context, appID, envID int64) error {
	// 检查配置是否存在
	existing, err := s.containerRepo.GetContainerByAppAndEnv(ctx, appID, envID)
	if err != nil {
		return fmt.Errorf("检查现有配置失败: %v", err)
	}
	if existing == nil {
		return fmt.Errorf("应用ID %d 在环境ID %d 下不存在容器配置", appID, envID)
	}

	return s.containerRepo.DeleteContainer(ctx, appID, envID)
}

// ValidateContainer 验证容器配置
func (s *containerService) ValidateContainer(ctx context.Context, container *model.Container) (bool, []string, []string, error) {
	var errors []string
	var warnings []string

	// 验证基本字段
	if container.AppID <= 0 {
		errors = append(errors, "应用ID不能为空")
	}
	if container.EnvID <= 0 {
		errors = append(errors, "环境ID不能为空")
	}
	if container.BaseImage == "" {
		errors = append(errors, "基础镜像不能为空")
	}
	if container.Replicas <= 0 {
		errors = append(errors, "副本数必须大于0")
	}
	if container.Replicas > 10 {
		warnings = append(warnings, "副本数较高，请确认是否需要")
	}
	if container.Namespace == "" {
		errors = append(errors, "命名空间不能为空")
	}

	// 验证端口配置
	if len(container.Ports) == 0 {
		errors = append(errors, "至少需要配置一个端口")
	} else {
		for i, port := range container.Ports {
			if port.Name == "" {
				errors = append(errors, fmt.Sprintf("端口%d名称不能为空", i+1))
			}
			if port.ContainerPort <= 0 || port.ContainerPort > 65535 {
				errors = append(errors, fmt.Sprintf("端口%d容器端口无效", i+1))
			}
			if port.ServicePort <= 0 || port.ServicePort > 65535 {
				errors = append(errors, fmt.Sprintf("端口%d服务端口无效", i+1))
			}
			if port.Protocol != "TCP" && port.Protocol != "UDP" {
				errors = append(errors, fmt.Sprintf("端口%d协议无效", i+1))
			}
		}
	}

	// 验证资源配置
	if container.Resources.Requests.CPU == "" {
		errors = append(errors, "CPU请求不能为空")
	}
	if container.Resources.Requests.Memory == "" {
		errors = append(errors, "内存请求不能为空")
	}
	if container.Resources.Limits.CPU == "" {
		errors = append(errors, "CPU限制不能为空")
	}
	if container.Resources.Limits.Memory == "" {
		errors = append(errors, "内存限制不能为空")
	}

	// 验证部署策略
	if container.Strategy != "RollingUpdate" && container.Strategy != "Recreate" {
		errors = append(errors, "部署策略必须是RollingUpdate或Recreate")
	}

	return len(errors) == 0, errors, warnings, nil
}

// PreviewYAML 预览容器配置的Kubernetes YAML
func (s *containerService) PreviewYAML(ctx context.Context, container *model.Container) (map[string]string, error) {
	// 这里应该生成实际的Kubernetes YAML
	// 为简化实现，返回模拟的YAML

	deploymentYAML := fmt.Sprintf(`apiVersion: apps/v1
kind: Deployment
metadata:
  name: app-%d
  namespace: %s
spec:
  replicas: %d
  strategy:
    type: %s
  selector:
    matchLabels:
      app: app-%d
  template:
    metadata:
      labels:
        app: app-%d
    spec:
      containers:
      - name: app
        image: %s
        resources:
          requests:
            cpu: %s
            memory: %s
          limits:
            cpu: %s
            memory: %s`,
		container.AppID,
		container.Namespace,
		container.Replicas,
		container.Strategy,
		container.AppID,
		container.AppID,
		container.BaseImage,
		container.Resources.Requests.CPU,
		container.Resources.Requests.Memory,
		container.Resources.Limits.CPU,
		container.Resources.Limits.Memory,
	)

	serviceYAML := fmt.Sprintf(`apiVersion: v1
kind: Service
metadata:
  name: app-%d-svc
  namespace: %s
spec:
  selector:
    app: app-%d
  ports:`,
		container.AppID,
		container.Namespace,
		container.AppID,
	)

	for _, port := range container.Ports {
		serviceYAML += fmt.Sprintf(`
  - name: %s
    port: %d
    targetPort: %d
    protocol: %s`,
			port.Name,
			port.ServicePort,
			port.ContainerPort,
			port.Protocol,
		)
	}

	return map[string]string{
		"deployment_yaml": deploymentYAML,
		"service_yaml":    serviceYAML,
	}, nil
}
