package service

import (
	"github.com/spf13/viper"
	"go.uber.org/zap"
)

type Service struct {
	logger *zap.Logger
	config *viper.Viper
}

func NewService(logger *zap.Logger, config *viper.Viper) *Service {
	return &Service{
		logger: logger,
		config: config,
	}
}

// GetLogger 获取日志实例
func (s *Service) GetLogger() *zap.Logger {
	return s.logger
}

// GetConfig 获取配置实例
func (s *Service) GetConfig() *viper.Viper {
	return s.config
}
