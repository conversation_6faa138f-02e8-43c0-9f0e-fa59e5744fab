package service

import (
	"ci-gateway/pkg/k8sclient"
	"fmt"
	"io"
	"net/http"
	"sync"
	"time"

	"github.com/gorilla/websocket"
	"go.uber.org/zap"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/client-go/kubernetes/scheme"
	"k8s.io/client-go/tools/remotecommand"
)

// TerminalMessage WebSocket消息类型
type TerminalMessage struct {
	Operation string `json:"operation"` // stdin, resize, ping, pong
	Data      string `json:"data,omitempty"`
	Rows      uint16 `json:"rows,omitempty"`
	Cols      uint16 `json:"cols,omitempty"`
}

// TerminalSession 终端会话
type TerminalSession struct {
	ID            string
	Namespace     string
	PodName       string
	ContainerName string
	Shell         string
	WebSocket     *websocket.Conn
	SizeChan      chan remotecommand.TerminalSize
	DoneChan      chan struct{}
	Logger        *zap.Logger
	mutex         sync.Mutex
	lastActivity  time.Time
	streamHandler *TerminalStreamHandler // 添加流处理器引用
}

// TerminalService 终端服务接口
type TerminalService interface {
	// HandleWebSocket 处理WebSocket连接
	HandleWebSocket(w http.ResponseWriter, r *http.Request, sessionID, namespace, podName, containerName, shell string, cols, rows uint16) error
	// GetSession 获取终端会话
	GetSession(sessionID string) (*TerminalSession, bool)
	// CloseSession 关闭终端会话
	CloseSession(sessionID string) error
	// ResizeSession 调整会话大小
	ResizeSession(sessionID string, cols, rows uint16) error
}

// terminalService 终端服务实现
type terminalService struct {
	*Service
	sessions map[string]*TerminalSession
	mutex    sync.RWMutex
	upgrader websocket.Upgrader
}

// NewTerminalService 创建终端服务实例
func NewTerminalService(service *Service) TerminalService {
	ts := &terminalService{
		Service:  service,
		sessions: make(map[string]*TerminalSession),
		upgrader: websocket.Upgrader{
			CheckOrigin: func(r *http.Request) bool {
				// 在生产环境中应该检查Origin
				return true
			},
			ReadBufferSize:  1024,
			WriteBufferSize: 1024,
		},
	}

	// 启动会话清理例程
	go ts.startSessionCleanup()

	return ts
}

// HandleWebSocket 处理WebSocket连接
func (s *terminalService) HandleWebSocket(w http.ResponseWriter, r *http.Request, sessionID, namespace, podName, containerName, shell string, cols, rows uint16) error {
	s.GetLogger().Info("处理终端WebSocket连接",
		zap.String("sessionID", sessionID),
		zap.String("namespace", namespace),
		zap.String("podName", podName),
		zap.String("containerName", containerName))

	// 升级HTTP连接为WebSocket
	ws, err := s.upgrader.Upgrade(w, r, nil)
	if err != nil {
		s.GetLogger().Error("WebSocket升级失败", zap.Error(err))
		return fmt.Errorf("WebSocket升级失败: %w", err)
	}

	// 创建终端会话
	session := &TerminalSession{
		ID:            sessionID,
		Namespace:     namespace,
		PodName:       podName,
		ContainerName: containerName,
		Shell:         shell,
		WebSocket:     ws,
		SizeChan:      make(chan remotecommand.TerminalSize),
		DoneChan:      make(chan struct{}),
		Logger:        s.GetLogger(),
		lastActivity:  time.Now(),
	}

	// 注册会话
	s.mutex.Lock()
	s.sessions[sessionID] = session
	s.mutex.Unlock()

	// 启动终端执行
	go s.startTerminalExec(session, cols, rows)

	// 处理WebSocket消息
	s.handleWebSocketMessages(session)

	return nil
}

// GetSession 获取终端会话
func (s *terminalService) GetSession(sessionID string) (*TerminalSession, bool) {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	session, exists := s.sessions[sessionID]
	return session, exists
}

// CloseSession 关闭终端会话
func (s *terminalService) CloseSession(sessionID string) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if session, exists := s.sessions[sessionID]; exists {
		close(session.DoneChan)
		session.WebSocket.Close()
		delete(s.sessions, sessionID)

		s.GetLogger().Info("终端会话已关闭", zap.String("sessionID", sessionID))
		return nil
	}

	return fmt.Errorf("会话不存在: %s", sessionID)
}

// ResizeSession 调整会话大小
func (s *terminalService) ResizeSession(sessionID string, cols, rows uint16) error {
	s.mutex.RLock()
	session, exists := s.sessions[sessionID]
	s.mutex.RUnlock()

	if !exists {
		return fmt.Errorf("会话不存在: %s", sessionID)
	}

	select {
	case session.SizeChan <- remotecommand.TerminalSize{
		Width:  cols,
		Height: rows,
	}:
		session.lastActivity = time.Now()
		s.GetLogger().Info("终端大小已调整",
			zap.String("sessionID", sessionID),
			zap.Uint16("cols", cols),
			zap.Uint16("rows", rows))
		return nil
	case <-time.After(5 * time.Second):
		return fmt.Errorf("调整终端大小超时")
	}
}

// startTerminalExec 启动终端执行
func (s *terminalService) startTerminalExec(session *TerminalSession, cols, rows uint16) {
	defer func() {
		s.CloseSession(session.ID)
	}()

	// 获取K8s客户端
	clientSet := k8sclient.GetClientSet()
	config, err := k8sclient.GenerateK8sConfig()()
	if err != nil {
		session.Logger.Error("获取K8s配置失败", zap.Error(err))
		s.sendErrorMessage(session, fmt.Sprintf("获取K8s配置失败: %v", err))
		return
	}

	// 构建执行请求
	req := clientSet.CoreV1().RESTClient().Post().
		Resource("pods").
		Name(session.PodName).
		Namespace(session.Namespace).
		SubResource("exec")

	// 设置执行参数
	execOptions := &corev1.PodExecOptions{
		Container: session.ContainerName,
		Command:   []string{session.Shell},
		Stdin:     true,
		Stdout:    true,
		Stderr:    true,
		TTY:       true,
	}
	req.VersionedParams(execOptions, scheme.ParameterCodec)

	// 创建SPDY执行器
	exec, err := remotecommand.NewSPDYExecutor(config, "POST", req.URL())
	if err != nil {
		session.Logger.Error("创建SPDY执行器失败", zap.Error(err))
		s.sendErrorMessage(session, fmt.Sprintf("创建执行器失败: %v", err))
		return
	}

	// 创建终端大小队列
	session.SizeChan <- remotecommand.TerminalSize{
		Width:  cols,
		Height: rows,
	}

	// 创建流处理器
	streamHandler := &TerminalStreamHandler{
		session:  session,
		service:  s,
		stdin:    make(chan []byte, 1024),
		resizeCh: session.SizeChan,
		done:     session.DoneChan,
	}

	// 将流处理器存储到会话中
	session.streamHandler = streamHandler

	// 启动输入处理
	go streamHandler.handleStdin()

	session.Logger.Info("开始执行终端命令",
		zap.String("sessionID", session.ID),
		zap.String("shell", session.Shell))

	// 执行终端命令
	err = exec.Stream(remotecommand.StreamOptions{
		Stdin:             streamHandler,
		Stdout:            streamHandler,
		Stderr:            streamHandler,
		TerminalSizeQueue: streamHandler,
		Tty:               true,
	})

	if err != nil {
		session.Logger.Error("终端命令执行失败", zap.Error(err))
		s.sendErrorMessage(session, fmt.Sprintf("终端执行失败: %v", err))
	}
}

// handleWebSocketMessages 处理WebSocket消息
func (s *terminalService) handleWebSocketMessages(session *TerminalSession) {
	defer session.WebSocket.Close()

	for {
		select {
		case <-session.DoneChan:
			return
		default:
			var msg TerminalMessage
			err := session.WebSocket.ReadJSON(&msg)
			if err != nil {
				if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
					session.Logger.Error("WebSocket读取错误", zap.Error(err))
				}
				return
			}

			session.lastActivity = time.Now()

			switch msg.Operation {
			case "stdin":
				// 将输入发送到终端
				if streamHandler, exists := s.getStreamHandler(session.ID); exists {
					select {
					case streamHandler.stdin <- []byte(msg.Data):
					case <-time.After(1 * time.Second):
						session.Logger.Warn("发送stdin超时")
					}
				}

			case "resize":
				// 调整终端大小
				if msg.Cols > 0 && msg.Rows > 0 {
					s.ResizeSession(session.ID, msg.Cols, msg.Rows)
				}

			case "ping":
				// 心跳检测
				s.sendMessage(session, TerminalMessage{Operation: "pong"})

			default:
				session.Logger.Warn("未知消息类型", zap.String("operation", msg.Operation))
			}
		}
	}
}

// sendMessage 发送WebSocket消息
func (s *terminalService) sendMessage(session *TerminalSession, msg TerminalMessage) error {
	session.mutex.Lock()
	defer session.mutex.Unlock()

	return session.WebSocket.WriteJSON(msg)
}

// sendErrorMessage 发送错误消息
func (s *terminalService) sendErrorMessage(session *TerminalSession, errorMsg string) {
	s.sendMessage(session, TerminalMessage{
		Operation: "error",
		Data:      errorMsg,
	})
}

// sendOutputMessage 发送输出消息
func (s *terminalService) sendOutputMessage(session *TerminalSession, data []byte) error {
	return s.sendMessage(session, TerminalMessage{
		Operation: "stdout",
		Data:      string(data),
	})
}

// getStreamHandler 获取流处理器
func (s *terminalService) getStreamHandler(sessionID string) (*TerminalStreamHandler, bool) {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	if session, exists := s.sessions[sessionID]; exists && session.streamHandler != nil {
		return session.streamHandler, true
	}
	return nil, false
}

// startSessionCleanup 启动会话清理例程
func (s *terminalService) startSessionCleanup() {
	ticker := time.NewTicker(5 * time.Minute)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			s.cleanupInactiveSessions()
		}
	}
}

// cleanupInactiveSessions 清理非活跃会话
func (s *terminalService) cleanupInactiveSessions() {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	now := time.Now()
	var toCleanup []string

	for sessionID, session := range s.sessions {
		if now.Sub(session.lastActivity) > 30*time.Minute {
			toCleanup = append(toCleanup, sessionID)
		}
	}

	for _, sessionID := range toCleanup {
		if session, exists := s.sessions[sessionID]; exists {
			close(session.DoneChan)
			session.WebSocket.Close()
			delete(s.sessions, sessionID)
			s.GetLogger().Info("清理非活跃会话", zap.String("sessionID", sessionID))
		}
	}
}

// TerminalStreamHandler 终端流处理器
type TerminalStreamHandler struct {
	session  *TerminalSession
	service  *terminalService
	stdin    chan []byte
	resizeCh chan remotecommand.TerminalSize
	done     chan struct{}
}

// Read 实现io.Reader接口（从WebSocket读取输入）
func (h *TerminalStreamHandler) Read(p []byte) (int, error) {
	select {
	case data := <-h.stdin:
		n := copy(p, data)
		return n, nil
	case <-h.done:
		return 0, io.EOF
	}
}

// Write 实现io.Writer接口（向WebSocket写入输出）
func (h *TerminalStreamHandler) Write(p []byte) (int, error) {
	if err := h.service.sendOutputMessage(h.session, p); err != nil {
		return 0, err
	}
	return len(p), nil
}

// Next 实现TerminalSizeQueue接口
func (h *TerminalStreamHandler) Next() *remotecommand.TerminalSize {
	select {
	case size := <-h.resizeCh:
		return &size
	case <-h.done:
		return nil
	}
}

// handleStdin 处理stdin输入
func (h *TerminalStreamHandler) handleStdin() {
	// 这个方法用于处理WebSocket到stdin的数据流
	// 实际的输入通过Read方法处理
}
