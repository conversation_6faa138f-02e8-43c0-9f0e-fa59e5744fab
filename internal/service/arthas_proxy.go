package service

import (
	"ci-gateway/pkg/k8sclient"
	"fmt"
	"io"
	"net"
	"net/http"
	"net/url"
	"strings"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
	"go.uber.org/zap"
	"k8s.io/client-go/tools/portforward"
	"k8s.io/client-go/transport/spdy"
)

// ArthasProxyService Arthas代理服务接口
type ArthasProxyService interface {
	// HandleWebSocketProxy 处理WebSocket代理连接
	HandleWebSocketProxy(c *gin.Context, namespace, podName string) error
	// HandleHTTPProxy 处理HTTP代理请求
	HandleHTTPProxy(c *gin.Context, namespace, podName string, targetPath string) error
	// CleanupPortForwarder 清理端口转发器
	CleanupPortForwarder(namespace, podName string)
}

// arthasProxyService Arthas代理服务实现
type arthasProxyService struct {
	logger       *zap.Logger
	upgrader     websocket.Upgrader
	forwarders   map[string]*PortForwarder // key: namespace/podName
	forwarderMux sync.RWMutex
}

// PortForwarder 端口转发器
type PortForwarder struct {
	localPort int
	stopCh    chan struct{}
	readyCh   chan struct{}
	errorCh   chan error
	isReady   bool
	namespace string
	podName   string
	mutex     sync.RWMutex
}

// NewArthasProxyService 创建Arthas代理服务
func NewArthasProxyService(logger *zap.Logger) ArthasProxyService {
	proxyService := &arthasProxyService{
		logger: logger,
		upgrader: websocket.Upgrader{
			CheckOrigin: func(r *http.Request) bool {
				return true // 允许跨域连接
			},
			ReadBufferSize:  1024,
			WriteBufferSize: 1024,
		},
		forwarders: make(map[string]*PortForwarder),
	}

	// 启动端口转发器清理例程
	go proxyService.startPortForwarderCleanup()

	return proxyService
}

// HandleWebSocketProxy 处理WebSocket代理连接
func (s *arthasProxyService) HandleWebSocketProxy(c *gin.Context, namespace, podName string) error {
	s.logger.Info("处理Arthas WebSocket代理",
		zap.String("namespace", namespace),
		zap.String("podName", podName))

	// 升级为WebSocket连接
	conn, err := s.upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		s.logger.Error("WebSocket升级失败", zap.Error(err))
		return fmt.Errorf("WebSocket升级失败: %w", err)
	}
	defer conn.Close()

	// 获取或创建端口转发器
	forwarder, err := s.getOrCreatePortForwarder(namespace, podName, 3658)
	if err != nil {
		s.logger.Error("创建端口转发器失败", zap.Error(err))
		conn.WriteMessage(websocket.TextMessage, []byte("错误: 无法连接到Pod"))
		return fmt.Errorf("创建端口转发器失败: %w", err)
	}

	// 等待端口转发就绪
	select {
	case <-forwarder.readyCh:
		s.logger.Info("端口转发已就绪", zap.Int("localPort", forwarder.localPort))
	case err := <-forwarder.errorCh:
		s.logger.Error("端口转发启动失败", zap.Error(err))
		conn.WriteMessage(websocket.TextMessage, []byte("错误: 端口转发启动失败"))
		return fmt.Errorf("端口转发启动失败: %w", err)
	case <-time.After(30 * time.Second):
		s.logger.Error("端口转发启动超时")
		conn.WriteMessage(websocket.TextMessage, []byte("错误: 连接超时"))
		return fmt.Errorf("端口转发启动超时")
	}

	// 建立到Arthas Web服务的WebSocket连接
	arthasURL := fmt.Sprintf("ws://localhost:%d/arthas-output/", forwarder.localPort)
	s.logger.Info("连接到Arthas WebSocket", zap.String("url", arthasURL))

	arthasConn, _, err := websocket.DefaultDialer.Dial(arthasURL, nil)
	if err != nil {
		s.logger.Error("连接Arthas WebSocket失败", zap.Error(err))
		conn.WriteMessage(websocket.TextMessage, []byte("错误: 无法连接到Arthas服务"))
		return fmt.Errorf("连接Arthas WebSocket失败: %w", err)
	}
	defer arthasConn.Close()

	s.logger.Info("WebSocket代理连接建立成功")

	// 启动双向数据转发
	errCh := make(chan error, 2)

	// 客户端 -> Arthas
	go func() {
		defer func() {
			if r := recover(); r != nil {
				s.logger.Error("客户端到Arthas转发panic", zap.Any("panic", r))
			}
		}()
		for {
			messageType, data, err := conn.ReadMessage()
			if err != nil {
				if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
					s.logger.Warn("客户端连接异常关闭", zap.Error(err))
				}
				errCh <- err
				return
			}

			if err := arthasConn.WriteMessage(messageType, data); err != nil {
				s.logger.Error("向Arthas写入消息失败", zap.Error(err))
				errCh <- err
				return
			}
		}
	}()

	// Arthas -> 客户端
	go func() {
		defer func() {
			if r := recover(); r != nil {
				s.logger.Error("Arthas到客户端转发panic", zap.Any("panic", r))
			}
		}()
		for {
			messageType, data, err := arthasConn.ReadMessage()
			if err != nil {
				if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
					s.logger.Warn("Arthas连接异常关闭", zap.Error(err))
				}
				errCh <- err
				return
			}

			if err := conn.WriteMessage(messageType, data); err != nil {
				s.logger.Error("向客户端写入消息失败", zap.Error(err))
				errCh <- err
				return
			}
		}
	}()

	// 等待其中一个方向的连接关闭
	err = <-errCh
	s.logger.Info("WebSocket代理连接结束", zap.Error(err))

	return nil
}

// HandleHTTPProxy 处理HTTP代理请求
func (s *arthasProxyService) HandleHTTPProxy(c *gin.Context, namespace, podName string, targetPath string) error {
	s.logger.Info("处理Arthas HTTP代理",
		zap.String("namespace", namespace),
		zap.String("podName", podName),
		zap.String("path", targetPath))

	// 获取或创建端口转发器
	forwarder, err := s.getOrCreatePortForwarder(namespace, podName, 3658)
	if err != nil {
		s.logger.Error("创建端口转发器失败", zap.Error(err))
		c.JSON(500, gin.H{"error": "无法连接到Pod"})
		return fmt.Errorf("创建端口转发器失败: %w", err)
	}

	// 等待端口转发就绪
	select {
	case <-forwarder.readyCh:
		// 转发就绪
	case err := <-forwarder.errorCh:
		s.logger.Error("端口转发启动失败", zap.Error(err))
		c.JSON(500, gin.H{"error": "端口转发启动失败"})
		return fmt.Errorf("端口转发启动失败: %w", err)
	case <-time.After(30 * time.Second):
		s.logger.Error("端口转发启动超时")
		c.JSON(500, gin.H{"error": "连接超时"})
		return fmt.Errorf("端口转发启动超时")
	}

	// 构建目标URL
	targetURL := fmt.Sprintf("http://localhost:%d%s", forwarder.localPort, targetPath)
	if c.Request.URL.RawQuery != "" {
		targetURL += "?" + c.Request.URL.RawQuery
	}

	s.logger.Info("转发HTTP请求", zap.String("targetURL", targetURL))

	// 创建HTTP请求
	client := &http.Client{Timeout: 30 * time.Second}
	req, err := http.NewRequest(c.Request.Method, targetURL, c.Request.Body)
	if err != nil {
		s.logger.Error("创建HTTP请求失败", zap.Error(err))
		c.JSON(500, gin.H{"error": "创建请求失败"})
		return fmt.Errorf("创建HTTP请求失败: %w", err)
	}

	// 复制请求头
	for key, values := range c.Request.Header {
		for _, value := range values {
			req.Header.Add(key, value)
		}
	}

	// 执行请求
	resp, err := client.Do(req)
	if err != nil {
		s.logger.Error("HTTP请求失败", zap.Error(err))
		c.JSON(500, gin.H{"error": "请求失败"})
		return fmt.Errorf("HTTP请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 复制响应头
	for key, values := range resp.Header {
		for _, value := range values {
			c.Header(key, value)
		}
	}

	// 设置状态码
	c.Status(resp.StatusCode)

	// 复制响应体
	_, err = io.Copy(c.Writer, resp.Body)
	if err != nil {
		s.logger.Error("复制响应体失败", zap.Error(err))
	}

	return nil
}

// getOrCreatePortForwarder 获取或创建端口转发器
func (s *arthasProxyService) getOrCreatePortForwarder(namespace, podName string, remotePort int) (*PortForwarder, error) {
	key := fmt.Sprintf("%s/%s", namespace, podName)

	s.forwarderMux.RLock()
	if forwarder, exists := s.forwarders[key]; exists {
		s.forwarderMux.RUnlock()
		return forwarder, nil
	}
	s.forwarderMux.RUnlock()

	s.forwarderMux.Lock()
	defer s.forwarderMux.Unlock()

	// 双重检查
	if forwarder, exists := s.forwarders[key]; exists {
		return forwarder, nil
	}

	// 创建新的端口转发器
	forwarder, err := s.createPortForwarder(namespace, podName, remotePort)
	if err != nil {
		return nil, err
	}

	s.forwarders[key] = forwarder
	return forwarder, nil
}

// createPortForwarder 创建端口转发器
func (s *arthasProxyService) createPortForwarder(namespace, podName string, remotePort int) (*PortForwarder, error) {
	// 获取可用的本地端口
	localPort, err := s.getAvailablePort()
	if err != nil {
		return nil, fmt.Errorf("获取可用端口失败: %w", err)
	}

	forwarder := &PortForwarder{
		localPort: localPort,
		stopCh:    make(chan struct{}),
		readyCh:   make(chan struct{}),
		errorCh:   make(chan error, 1),
		namespace: namespace,
		podName:   podName,
	}

	// 启动端口转发
	go func() {
		if err := s.startPortForward(forwarder, remotePort); err != nil {
			s.logger.Error("端口转发启动失败", zap.Error(err))
			select {
			case forwarder.errorCh <- err:
			default:
			}
		}
	}()

	return forwarder, nil
}

// startPortForward 启动端口转发
func (s *arthasProxyService) startPortForward(forwarder *PortForwarder, remotePort int) error {
	config := k8sclient.GenerateK8sConfig()
	restConfig, err := config()
	if err != nil {
		return fmt.Errorf("获取Kubernetes配置失败: %w", err)
	}

	// 创建到API server的连接
	roundTripper, upgrader, err := spdy.RoundTripperFor(restConfig)
	if err != nil {
		return fmt.Errorf("创建SPDY连接失败: %w", err)
	}

	// 构建端口转发URL
	serverURL := fmt.Sprintf("%s/api/v1/namespaces/%s/pods/%s/portforward",
		strings.TrimSuffix(restConfig.Host, "/"), forwarder.namespace, forwarder.podName)

	dialer := spdy.NewDialer(upgrader, &http.Client{Transport: roundTripper}, http.MethodPost, &url.URL{Scheme: "https", Path: serverURL, Host: restConfig.Host})

	ports := []string{fmt.Sprintf("%d:%d", forwarder.localPort, remotePort)}

	readyCh := make(chan struct{})
	pf, err := portforward.New(dialer, ports, forwarder.stopCh, readyCh, io.Discard, io.Discard)
	if err != nil {
		return fmt.Errorf("创建端口转发器失败: %w", err)
	}

	// 启动端口转发
	go func() {
		if err := pf.ForwardPorts(); err != nil {
			s.logger.Error("端口转发运行失败", zap.Error(err))
			select {
			case forwarder.errorCh <- err:
			default:
			}
		}
	}()

	// 等待端口转发就绪
	select {
	case <-readyCh:
		forwarder.mutex.Lock()
		forwarder.isReady = true
		forwarder.mutex.Unlock()
		close(forwarder.readyCh)
		s.logger.Info("端口转发启动成功",
			zap.String("namespace", forwarder.namespace),
			zap.String("podName", forwarder.podName),
			zap.Int("localPort", forwarder.localPort),
			zap.Int("remotePort", remotePort))

		// 等待停止信号
		<-forwarder.stopCh
		return nil
	case <-time.After(30 * time.Second):
		return fmt.Errorf("端口转发启动超时")
	}
}

// getAvailablePort 获取可用的本地端口
func (s *arthasProxyService) getAvailablePort() (int, error) {
	listener, err := net.Listen("tcp", ":0")
	if err != nil {
		return 0, err
	}
	defer listener.Close()

	addr := listener.Addr().(*net.TCPAddr)
	return addr.Port, nil
}

// CleanupPortForwarder 清理端口转发器
func (s *arthasProxyService) CleanupPortForwarder(namespace, podName string) {
	key := fmt.Sprintf("%s/%s", namespace, podName)

	s.forwarderMux.Lock()
	defer s.forwarderMux.Unlock()

	if forwarder, exists := s.forwarders[key]; exists {
		close(forwarder.stopCh)
		delete(s.forwarders, key)
		s.logger.Info("端口转发器已清理",
			zap.String("namespace", namespace),
			zap.String("podName", podName))
	}
}

// startPortForwarderCleanup 启动端口转发器清理例程
func (s *arthasProxyService) startPortForwarderCleanup() {
	ticker := time.NewTicker(10 * time.Minute) // 每10分钟检查一次
	defer ticker.Stop()

	s.logger.Info("端口转发器自动清理例程已启动")

	for {
		select {
		case <-ticker.C:
			s.cleanupUnusedPortForwarders()
		}
	}
}

// cleanupUnusedPortForwarders 清理未使用的端口转发器
func (s *arthasProxyService) cleanupUnusedPortForwarders() {
	s.forwarderMux.Lock()
	defer s.forwarderMux.Unlock()

	var toCleanup []string

	for key, forwarder := range s.forwarders {
		// 检查端口转发器是否还在运行
		select {
		case <-forwarder.stopCh:
			// 通道已关闭，标记清理
			toCleanup = append(toCleanup, key)
			s.logger.Info("发现已停止的端口转发器", zap.String("key", key))
		default:
			// 通道未关闭，检查是否健康
			// 这里可以添加更多的健康检查逻辑
		}
	}

	// 清理已停止的端口转发器
	for _, key := range toCleanup {
		delete(s.forwarders, key)
		s.logger.Info("清理端口转发器", zap.String("key", key))
	}

	if len(toCleanup) > 0 {
		s.logger.Info("端口转发器清理完成", zap.Int("cleanedCount", len(toCleanup)))
	}
}
