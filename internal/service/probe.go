package service

import (
	"context"

	"ci-gateway/internal/model"
	"ci-gateway/internal/repository"
)

// ProbeService 探针服务接口
type ProbeService interface {
	// GetProbesByAppAndEnv 根据应用ID和环境ID获取探针配置列表
	GetProbesByAppAndEnv(ctx context.Context, appID, envID int64) ([]*model.Probe, error)
	FindByAppAndEnvAndGroupID(ctx context.Context, appID, envID, groupID int64) (*model.Probe, error)
}

// probeService 探针服务实现
type probeService struct {
	probeRepo repository.ProbeRepository
}

// NewProbeService 创建探针服务
func NewProbeService(probeRepo repository.ProbeRepository) ProbeService {
	return &probeService{
		probeRepo: probeRepo,
	}
}

// GetProbesByAppAndEnv 根据应用ID和环境ID获取探针配置列表
func (s *probeService) GetProbesByAppAndEnv(ctx context.Context, appID, envID int64) ([]*model.Probe, error) {
	return s.probeRepo.FindByAppAndEnv(ctx, appID, envID)
}

func (s *probeService) FindByAppAndEnvAndGroupID(ctx context.Context, appID, envID, groupID int64) (*model.Probe, error) {
	return s.probeRepo.FindByAppAndEnvAndGroupID(ctx, appID, envID, groupID)
}
