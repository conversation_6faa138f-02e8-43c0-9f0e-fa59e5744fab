package repository

import (
	deploy "ci-gateway/internal/model"

	"gorm.io/gorm"
)

type AppSettingsRepository interface {
	GetAppSettingsByID(id int64) (*deploy.AppSettings, error)
	GetAppSettingsByAppID(appID int64) (*deploy.AppSettings, error)
	CreateAppSettings(settings *deploy.AppSettings) error
	UpdateAppSettings(settings *deploy.AppSettings) error
	DeleteAppSettings(id int64) error
	ListAppSettings(limit, offset int) ([]*deploy.AppSettings, error)
}

type appSettingsRepository struct {
	*Repository
}

func NewAppSettingsRepository(repository *Repository) AppSettingsRepository {
	return &appSettingsRepository{
		Repository: repository,
	}
}

func (r *appSettingsRepository) GetAppSettingsByID(id int64) (*deploy.AppSettings, error) {
	var settings deploy.AppSettings
	err := r.DB.Where("id = ? AND status = 1", id).First(&settings).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &settings, nil
}

func (r *appSettingsRepository) GetAppSettingsByAppID(appID int64) (*deploy.AppSettings, error) {
	var settings deploy.AppSettings
	err := r.DB.Where("app_id = ? AND status = 1", appID).First(&settings).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &settings, nil
}

func (r *appSettingsRepository) CreateAppSettings(settings *deploy.AppSettings) error {
	return r.DB.Create(settings).Error
}

func (r *appSettingsRepository) UpdateAppSettings(settings *deploy.AppSettings) error {
	return r.DB.Save(settings).Error
}

func (r *appSettingsRepository) DeleteAppSettings(id int64) error {
	return r.DB.Model(&deploy.AppSettings{}).Where("id = ?", id).Update("status", 0).Error
}

func (r *appSettingsRepository) ListAppSettings(limit, offset int) ([]*deploy.AppSettings, error) {
	var settingsList []*deploy.AppSettings
	err := r.DB.Where("status = 1").Limit(limit).Offset(offset).Find(&settingsList).Error
	return settingsList, err
}
