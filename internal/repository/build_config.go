package repository

import (
	"ci-gateway/internal/model"
	"errors"

	"gorm.io/gorm"
)

// BuildTemplateRepository 构建模板仓库接口
type BuildTemplateRepository interface {
	GetTemplateByID(id int64) (*model.BuildTemplate, error)
	GetTemplatesByLanguage(language string) ([]*model.BuildTemplate, error)
	GetDefaultTemplate(language string) (*model.BuildTemplate, error)
	GetAllTemplates() ([]*model.BuildTemplate, error)
	CreateTemplate(template *model.BuildTemplate) error
	UpdateTemplate(template *model.BuildTemplate) error
	DeleteTemplate(id int64) error
	SetDefaultTemplate(id int64, language string) error
}

// buildTemplateRepository 构建模板仓库实现
type buildTemplateRepository struct {
	*Repository
}

// NewBuildTemplateRepository 创建构建模板仓库实例
func NewBuildTemplateRepository(repository *Repository) BuildTemplateRepository {
	return &buildTemplateRepository{
		Repository: repository,
	}
}

// GetTemplateByID 根据ID获取构建模板
func (r *buildTemplateRepository) GetTemplateByID(id int64) (*model.BuildTemplate, error) {
	var template model.BuildTemplate
	err := r.DB.Where("id = ? AND status = 1 AND is_deleted = 0", id).First(&template).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &template, nil
}

// GetTemplatesByLanguage 根据语言获取构建模板列表
func (r *buildTemplateRepository) GetTemplatesByLanguage(language string) ([]*model.BuildTemplate, error) {
	var templates []*model.BuildTemplate
	err := r.DB.Where("language = ? AND status = 1 AND is_deleted = 0", language).
		Order("is_default DESC, sort_order ASC").
		Find(&templates).Error
	return templates, err
}

// GetDefaultTemplate 获取指定语言的默认模板
func (r *buildTemplateRepository) GetDefaultTemplate(language string) (*model.BuildTemplate, error) {
	var template model.BuildTemplate
	err := r.DB.Where("language = ? AND is_default = 1 AND status = 1 AND is_deleted = 0", language).
		First(&template).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &template, nil
}

// GetAllTemplates 获取所有构建模板
func (r *buildTemplateRepository) GetAllTemplates() ([]*model.BuildTemplate, error) {
	var templates []*model.BuildTemplate
	err := r.DB.Where("status = 1 AND is_deleted = 0").
		Order("language ASC, is_default DESC, sort_order ASC").
		Find(&templates).Error
	return templates, err
}

// CreateTemplate 创建构建模板
func (r *buildTemplateRepository) CreateTemplate(template *model.BuildTemplate) error {
	return r.DB.Create(template).Error
}

// UpdateTemplate 更新构建模板
func (r *buildTemplateRepository) UpdateTemplate(template *model.BuildTemplate) error {
	return r.DB.Save(template).Error
}

// DeleteTemplate 删除构建模板（软删除）
func (r *buildTemplateRepository) DeleteTemplate(id int64) error {
	return r.DB.Model(&model.BuildTemplate{}).
		Where("id = ?", id).
		Updates(map[string]interface{}{
			"is_deleted": 1,
			"status":     0,
		}).Error
}

// SetDefaultTemplate 设置默认模板
func (r *buildTemplateRepository) SetDefaultTemplate(id int64, language string) error {
	return r.DB.Transaction(func(tx *gorm.DB) error {
		// 先清除该语言的所有默认标记
		if err := tx.Model(&model.BuildTemplate{}).
			Where("language = ?", language).
			Update("is_default", 0).Error; err != nil {
			return err
		}

		// 设置新的默认模板
		return tx.Model(&model.BuildTemplate{}).
			Where("id = ?", id).
			Update("is_default", 1).Error
	})
}

// BuildConfigRepository 项目构建配置仓库接口
type BuildConfigRepository interface {
	GetConfigByAppAndEnv(appID, envID int64) (*model.BuildConfig, error)
	GetConfigByGroupID(groupID int64) (*model.BuildConfig, error)
	GetConfigByID(id int64) (*model.BuildConfig, error)
	CreateConfig(config *model.BuildConfig) error
	UpdateConfig(config *model.BuildConfig) error
	DeleteConfig(id int64) error
	GetConfigsByAppID(appID int64) ([]*model.BuildConfig, error)
}

// buildConfigRepository 项目构建配置仓库实现
type buildConfigRepository struct {
	*Repository
}

// NewBuildConfigRepository 创建项目构建配置仓库实例
func NewBuildConfigRepository(repository *Repository) BuildConfigRepository {
	return &buildConfigRepository{
		Repository: repository,
	}
}

// GetConfigByAppAndEnv 根据应用ID和环境ID获取构建配置
func (r *buildConfigRepository) GetConfigByAppAndEnv(appID, envID int64) (*model.BuildConfig, error) {
	var config model.BuildConfig
	err := r.DB.Where("app_id = ? AND env_id = ? AND status = 1 AND is_deleted = 0", appID, envID).
		First(&config).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &config, nil
}

// GetConfigByID 根据ID获取构建配置
func (r *buildConfigRepository) GetConfigByID(id int64) (*model.BuildConfig, error) {
	var config model.BuildConfig
	err := r.DB.Where("id = ? AND status = 1 AND is_deleted = 0", id).First(&config).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &config, nil
}

// CreateConfig 创建构建配置
func (r *buildConfigRepository) CreateConfig(config *model.BuildConfig) error {
	return r.DB.Create(config).Error
}

// UpdateConfig 更新构建配置
func (r *buildConfigRepository) UpdateConfig(config *model.BuildConfig) error {
	return r.DB.Save(config).Error
}

// DeleteConfig 删除构建配置（软删除）
func (r *buildConfigRepository) DeleteConfig(id int64) error {
	return r.DB.Model(&model.BuildConfig{}).
		Where("id = ?", id).
		Updates(map[string]interface{}{
			"is_deleted": 1,
			"status":     0,
		}).Error
}

// GetConfigsByAppID 根据应用ID获取所有构建配置
func (r *buildConfigRepository) GetConfigsByAppID(appID int64) ([]*model.BuildConfig, error) {
	var configs []*model.BuildConfig
	err := r.DB.Where("app_id = ? AND status = 1 AND is_deleted = 0", appID).
		Find(&configs).Error
	return configs, err
}

func (r *buildConfigRepository) GetConfigByGroupID(groupID int64) (*model.BuildConfig, error) {
	var config model.BuildConfig
	err := r.DB.Where("group_id = ? AND status = 1 AND is_deleted = 0", groupID).
		First(&config).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &config, err
}
