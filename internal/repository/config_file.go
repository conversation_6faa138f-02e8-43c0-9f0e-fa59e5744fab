package repository

import (
	"ci-gateway/internal/model"

	"gorm.io/gorm"
)

// ConfigFileRepository 配置文件仓库接口
type ConfigFileRepository interface {
	GetConfigFileByID(id int64) (*model.ConfigFile, error)
	GetConfigFilesByAppID(appID int64) ([]*model.ConfigFile, error)
	GetConfigFilesByEnvID(envID int64) ([]*model.ConfigFile, error)
	GetConfigFilesByAppAndEnvID(appID, envID int64) ([]*model.ConfigFile, error)
	GetConfigContentByFileID(fileID int64) (*model.ConfigFileContent, error)
	CreateConfigFile(configFile *model.ConfigFile) error
	CreateConfigContent(content *model.ConfigFileContent) error
	CreateConfigFileWithAssociations(configFile *model.ConfigFile) error
	CreateConfigFileWithTx(tx *gorm.DB, configFile *model.ConfigFile) error
	CreateConfigContentWithTx(tx *gorm.DB, content *model.ConfigFileContent) error
	BatchCreateConfigFiles(configFiles []*model.ConfigFile) error
	UpdateConfigFile(configFile *model.ConfigFile) error
	DeleteConfigFile(id int64) error
	DeleteConfigFilesByAppAndEnvID(appID, envID int64) error
	ListConfigFiles(limit, offset int) ([]*model.ConfigFile, error)
}

// configFileRepository 配置文件仓库实现
type configFileRepository struct {
	*Repository
}

// NewConfigFileRepository 创建配置文件仓库实例
func NewConfigFileRepository(repository *Repository) ConfigFileRepository {
	return &configFileRepository{
		Repository: repository,
	}
}

// GetConfigFileByID 根据ID获取配置文件
func (r *configFileRepository) GetConfigFileByID(id int64) (*model.ConfigFile, error) {
	var configFile model.ConfigFile
	err := r.DB.Where("id = ? AND status = 1", id).First(&configFile).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &configFile, nil
}

// GetConfigFilesByAppID 根据应用ID获取配置文件列表
func (r *configFileRepository) GetConfigFilesByAppID(appID int64) ([]*model.ConfigFile, error) {
	var configFiles []*model.ConfigFile
	err := r.DB.Where("app_id = ? AND status = 1", appID).Find(&configFiles).Error
	if err != nil {
		return nil, err
	}
	return configFiles, nil
}

// GetConfigFilesByEnvID 根据环境ID获取配置文件列表
func (r *configFileRepository) GetConfigFilesByEnvID(envID int64) ([]*model.ConfigFile, error) {
	var configFiles []*model.ConfigFile
	err := r.DB.Where("env_id = ? AND status = 1", envID).Find(&configFiles).Error
	if err != nil {
		return nil, err
	}
	return configFiles, nil
}

// GetConfigFilesByAppAndEnvID 根据应用ID和环境ID获取配置文件列表
func (r *configFileRepository) GetConfigFilesByAppAndEnvID(appID, envID int64) ([]*model.ConfigFile, error) {
	var configFiles []*model.ConfigFile
	err := r.DB.Where("app_id = ? AND env_id = ? AND status = 1", appID, envID).Find(&configFiles).Error
	if err != nil {
		return nil, err
	}
	return configFiles, nil
}

func (r *configFileRepository) GetConfigContentByFileID(fileID int64) (*model.ConfigFileContent, error) {
	var configFileContent model.ConfigFileContent
	err := r.DB.Where("file_id = ? AND status = 1", fileID).Order("version DESC").First(&configFileContent).Error
	if err != nil {
		return nil, err
	}
	return &configFileContent, nil
}

// CreateConfigFile 创建配置文件
func (r *configFileRepository) CreateConfigFile(configFile *model.ConfigFile) error {
	return r.DB.Create(configFile).Error
}

// CreateConfigFileWithTx 使用事务创建配置文件
func (r *configFileRepository) CreateConfigFileWithTx(tx *gorm.DB, configFile *model.ConfigFile) error {
	return tx.Create(configFile).Error
}

// CreateConfigContent 创建配置文件内容
func (r *configFileRepository) CreateConfigContent(content *model.ConfigFileContent) error {
	return r.DB.Create(content).Error
}

// CreateConfigContentWithTx 使用事务创建配置文件内容
func (r *configFileRepository) CreateConfigContentWithTx(tx *gorm.DB, content *model.ConfigFileContent) error {
	return tx.Create(content).Error
}

// CreateConfigFileWithAssociations 一次性创建配置文件及其关联内容
func (r *configFileRepository) CreateConfigFileWithAssociations(configFile *model.ConfigFile) error {
	return r.DB.Session(&gorm.Session{FullSaveAssociations: true}).Create(configFile).Error
}

// BatchCreateConfigFiles 批量创建配置文件
func (r *configFileRepository) BatchCreateConfigFiles(configFiles []*model.ConfigFile) error {
	return r.DB.Create(configFiles).Error
}

// UpdateConfigFile 更新配置文件
func (r *configFileRepository) UpdateConfigFile(configFile *model.ConfigFile) error {
	return r.DB.Save(configFile).Error
}

// DeleteConfigFile 删除配置文件（软删除）
func (r *configFileRepository) DeleteConfigFile(id int64) error {
	return r.DB.Model(&model.ConfigFile{}).Where("id = ?", id).Update("status", 0).Error
}

// DeleteConfigFilesByAppAndEnvID 根据应用ID和环境ID删除配置文件（软删除）
func (r *configFileRepository) DeleteConfigFilesByAppAndEnvID(appID, envID int64) error {
	return r.DB.Model(&model.ConfigFile{}).Where("app_id = ? AND env_id = ?", appID, envID).Update("status", 0).Error
}

// ListConfigFiles 分页获取配置文件列表
func (r *configFileRepository) ListConfigFiles(limit, offset int) ([]*model.ConfigFile, error) {
	var configFiles []*model.ConfigFile
	err := r.DB.Where("status = 1").Limit(limit).Offset(offset).Find(&configFiles).Error
	return configFiles, err
}
