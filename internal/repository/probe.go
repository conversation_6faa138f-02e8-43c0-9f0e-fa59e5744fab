package repository

import (
	"context"
	"errors"

	"ci-gateway/internal/model"

	"gorm.io/gorm"
)

// ProbeRepository 探针配置仓库接口
type ProbeRepository interface {
	// FindByAppAndEnv 根据应用ID和环境ID查询探针配置
	FindByAppAndEnv(ctx context.Context, appID, envID int64) ([]*model.Probe, error)
	FindByAppAndEnvAndGroupID(ctx context.Context, appID, envID, groupID int64) (*model.Probe, error)
}

// probeRepo 探针配置仓库实现
type probeRepo struct {
	*Repository
}

// NewProbeRepository 创建探针配置仓库
func NewProbeRepository(repo *Repository) ProbeRepository {
	return &probeRepo{Repository: repo}
}

// FindByAppAndEnv 根据应用ID和环境ID查询探针配置
func (r *probeRepo) FindByAppAndEnv(ctx context.Context, appID, envID int64) ([]*model.Probe, error) {
	var probes []*model.Probe

	err := r.DB.WithContext(ctx).
		Where("app_id = ? AND env_id = ? AND status = 1", appID, envID).
		Find(&probes).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}

	return probes, nil
}

func (r *probeRepo) FindByAppAndEnvAndGroupID(ctx context.Context, appID, envID, groupID int64) (*model.Probe, error) {
	var probe model.Probe

	err := r.DB.WithContext(ctx).
		Where("app_id = ? AND env_id = ? AND status = 1 AND (group_id = ? OR group_id = 0)", appID, envID, groupID).
		Order("group_id DESC").
		First(&probe).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}

	return &probe, nil
}
