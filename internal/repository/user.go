package repository

import (
	"ci-gateway/internal/model"

	"gorm.io/gorm"
)

type UserRepository interface {
	GetByUsername(username string) (*model.User, error)
	Create(user *model.User) error
	Update(user *model.User) error
	Delete(username string) error
	List(page int, pageSize int, dispname string, email string) ([]model.User, error)
	Count(dispname string, email string) (int64, error)
	UpdateRole(userID int64, role int) error
	UpdateCurrentApp(userID int64, appCode string) error
}

type userRepository struct {
	*Repository
}

func NewUserRepository(repository *Repository) UserRepository {
	return &userRepository{
		Repository: repository,
	}
}

func (r *userRepository) GetByUsername(username string) (*model.User, error) {
	var user model.User
	result := r.DB.Where("username = ?", username).First(&user)
	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, result.Error
	}
	return &user, nil
}

func (r *userRepository) Create(user *model.User) error {
	return r.DB.Create(user).Error
}

func (r *userRepository) Update(user *model.User) error {
	return r.DB.Save(user).Error
}

func (r *userRepository) Delete(username string) error {
	return r.DB.Where("username = ?", username).Delete(&model.User{}).Error
}

func (r *userRepository) List(page int, pageSize int, dispname string, email string) ([]model.User, error) {
	var users []model.User
	query := r.DB
	if dispname != "" {
		query = query.Where("dispname LIKE ?", "%"+dispname+"%")
	}
	if email != "" {
		query = query.Where("email LIKE ?", "%"+email+"%")
	}
	result := query.Offset((page - 1) * pageSize).Limit(pageSize).Find(&users)
	if result.Error != nil {
		return nil, result.Error
	}
	return users, nil
}

func (r *userRepository) Count(dispname string, email string) (int64, error) {
	var count int64
	query := r.DB
	if dispname != "" {
		query = query.Where("dispname LIKE ?", "%"+dispname+"%")
	}
	if email != "" {
		query = query.Where("email LIKE ?", "%"+email+"%")
	}
	result := query.Model(&model.User{}).Count(&count)
	if result.Error != nil {
		return 0, result.Error
	}
	return count, nil
}

func (r *userRepository) UpdateRole(userID int64, role int) error {
	return r.DB.Model(&model.User{}).Where("id = ?", userID).Update("role", role).Error
}

func (r *userRepository) UpdateCurrentApp(userID int64, appCode string) error {
	return r.DB.Model(&model.User{}).Where("id = ?", userID).Update("current_app", appCode).Error
}
