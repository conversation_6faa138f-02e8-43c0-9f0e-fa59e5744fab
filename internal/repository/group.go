package repository

import (
	"ci-gateway/internal/model"
	"time"

	"gorm.io/gorm"
)

// GetTimestamp 获取当前时间戳
func GetTimestamp() int64 {
	return time.Now().Unix()
}

// GroupRepository 部署分组仓库接口
type GroupRepository interface {
	// 查询操作
	GetGroupByID(id int64) (*model.Group, error)
	GetGroupByCode(code string) (*model.Group, error)
	GetGroupsByAppID(appID int64) ([]*model.Group, error)
	GetGroupsByEnvID(envID int64) ([]*model.Group, error)
	GetGroupsByAppIDAndEnvID(appID, envID int64) ([]*model.Group, error)
	GetDefaultGroupByAppIDAndEnvID(appID, envID int64) (*model.Group, error)
	ListGroups(appID, envID int64, code, name string) ([]*model.Group, error)

	CountByAppID(appID int64) (int64, error)
	GetGroupsByAppIDAndEnvIDAndCode(appID, envID int64, code string) (*model.Group, error)

	// 写操作
	CreateGroup(group *model.Group) error
	UpdateGroup(group *model.Group) error
	DeleteGroup(id int64, userID int64) error
	SetDefaultGroup(id, appID, envID, userID int64) error
}

type groupRepository struct {
	*Repository
}

// NewGroupRepository 创建部署分组仓库
func NewGroupRepository(repository *Repository) GroupRepository {
	return &groupRepository{
		Repository: repository,
	}
}

// GetGroupByID 通过ID获取部署分组
func (r *groupRepository) GetGroupByID(id int64) (*model.Group, error) {
	var group model.Group
	err := r.DB.Where("id = ? AND is_deleted = 0", id).First(&group).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &group, nil
}

// GetGroupByCode 通过编码获取部署分组
func (r *groupRepository) GetGroupByCode(code string) (*model.Group, error) {
	var group model.Group
	err := r.DB.Where("code = ? AND is_deleted = 0", code).First(&group).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &group, nil
}

// GetGroupsByAppID 通过应用ID获取部署分组列表
func (r *groupRepository) GetGroupsByAppID(appID int64) ([]*model.Group, error) {
	var groups []*model.Group
	err := r.DB.Where("app_id = ? AND is_deleted = 0", appID).
		Order("id ASC").
		Find(&groups).Error
	return groups, err
}

// GetGroupsByEnvID 通过环境ID获取部署分组列表
func (r *groupRepository) GetGroupsByEnvID(envID int64) ([]*model.Group, error) {
	var groups []*model.Group
	err := r.DB.Where("env_id = ? AND is_deleted = 0", envID).
		Order("id ASC").
		Find(&groups).Error
	return groups, err
}

// GetGroupsByAppIDAndEnvID 通过应用ID和环境ID获取部署分组列表
func (r *groupRepository) GetGroupsByAppIDAndEnvID(appID, envID int64) ([]*model.Group, error) {
	var groups []*model.Group
	err := r.DB.Where("app_id = ? AND env_id = ? AND is_deleted = 0", appID, envID).
		Order("id ASC").
		Find(&groups).Error
	return groups, err
}

// GetDefaultGroupByAppIDAndEnvID 获取应用环境下的默认分组
func (r *groupRepository) GetDefaultGroupByAppIDAndEnvID(appID, envID int64) (*model.Group, error) {
	var group model.Group
	err := r.DB.Where("app_id = ? AND env_id = ? AND is_de	leted = 0", appID, envID).First(&group).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &group, nil
}

// ListGroups 分页获取部署分组列表

func (r *groupRepository) ListGroups(appID, envID int64, code, name string) ([]*model.Group, error) {
	var groups []*model.Group
	query := r.DB.Model(&model.Group{}).Where("is_deleted = 0 and app_id = ? AND env_id = ?", appID, envID).
		Where("code LIKE ? OR name LIKE ?", "%"+code+"%", "%"+name+"%")

	// 获取分页数据
	if err := query.
		Order("id ASC").
		Find(&groups).Error; err != nil {
		return nil, err
	}

	return groups, nil
}

// CountByAppID 统计应用下的分组数量
func (r *groupRepository) CountByAppID(appID int64) (int64, error) {
	var count int64
	err := r.DB.Model(&model.Group{}).Where("app_id = ? AND is_deleted = 0", appID).Count(&count).Error
	return count, err
}

func (r *groupRepository) GetGroupsByAppIDAndEnvIDAndCode(appID int64, envID int64, code string) (*model.Group, error) {
	var group *model.Group
	err := r.DB.Model(&model.Group{}).Where("app_id = ? AND env_id = ? AND code = ? AND is_deleted = 0", appID, envID, code).First(&group).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return group, err
}

// CreateGroup 创建部署分组
func (r *groupRepository) CreateGroup(group *model.Group) error {
	return r.DB.Create(group).Error
}

// UpdateGroup 更新部署分组
func (r *groupRepository) UpdateGroup(group *model.Group) error {
	return r.DB.Save(group).Error
}

// DeleteGroup 删除部署分组(软删除)
func (r *groupRepository) DeleteGroup(id int64, userID int64) error {
	return r.DB.Model(&model.Group{}).
		Where("id = ?", id).
		Updates(map[string]interface{}{
			"is_deleted": 1,
			"update_by":  userID,
			"u_t":        GetTimestamp(),
		}).Error
}

// SetDefaultGroup 设置默认分组
func (r *groupRepository) SetDefaultGroup(id, appID, envID, userID int64) error {
	// 开始事务
	tx := r.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 1. 将该应用环境下所有分组设置为非默认
	if err := tx.Model(&model.Group{}).
		Where("app_id = ? AND env_id = ? AND is_deleted = 0", appID, envID).
		Updates(map[string]interface{}{
			"update_by": userID,
			"u_t":       GetTimestamp(),
		}).Error; err != nil {
		tx.Rollback()
		return err
	}

	// 2. 将指定分组设置为默认
	if err := tx.Model(&model.Group{}).
		Where("id = ? AND is_deleted = 0", id).
		Updates(map[string]interface{}{
			"update_by": userID,
			"u_t":       GetTimestamp(),
		}).Error; err != nil {
		tx.Rollback()
		return err
	}

	// 提交事务
	return tx.Commit().Error
}
