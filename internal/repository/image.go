package repository

import (
	"ci-gateway/internal/model"
	"context"
)

type ImageRepository interface {
	ListImages(ctx context.Context, offset int, pageSize int, name string, status int8, imageType string) ([]*model.Image, int64, error)
	Delete(ctx context.Context, imageID int64) error
	// Count(name string, status int8) (int64, error)
	// ListVersions(ctx context.Context) ([]string, error)
	// ListVersionsAndCPUs(ctx context.Context) ([]map[string]interface{}, error)
	// CheckImageExists(ctx context.Context, imageName string) (bool, error)
	// Create(ctx context.Context, image model.RedisImage) (int64, error)
	// GetByID(ctx context.Context, id int64) (*model.RedisImage, error)

	// ChangeStatus(ctx context.Context, imageID int64, status int8) error
	// Update(ctx context.Context, image model.RedisImage) error
}

type imageRepository struct {
	*Repository
}

func NewImageRepository(repository *Repository) ImageRepository {
	return &imageRepository{
		Repository: repository,
	}
}

func (r *imageRepository) ListImages(ctx context.Context, offset int, pageSize int, name string, status int8, imageType string) ([]*model.Image, int64, error) {
	var images []*model.Image
	var total int64

	db := r.DB.WithContext(ctx).Model(&model.Image{}).Where("is_deleted = 0")
	if name != "" {
		db = db.Where("name LIKE ?", "%"+name+"%")
	}
	if imageType != "" {
		db = db.Where("type = ?", "%"+imageType+"%")
	}

	if status > 0 {
		db = db.Where("status = ?", status)
	}

	err := db.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	err = db.Order("id DESC").Offset(offset).Limit(pageSize).Find(&images).Error
	if err != nil {
		return nil, 0, err
	}

	return images, total, nil
}

// func (r *imageRepository) Count(name string, status int8) (int64, error) {
// 	var count int64
// 	query := r.DB
// 	if name != "" {
// 		query = query.Where("name LIKE ?", "%"+name+"%")
// 	}
// 	if status > 0 {
// 		query = query.Where("status = ?", status)
// 	}
// 	result := query.Model(&model.Image{}).Count(&count)
// 	if result.Error != nil {
// 		return 0, result.Error
// 	}
// 	return count, nil
// }

// func (r *imageRepository) ListVersions(ctx context.Context) ([]string, error) {
// 	var versions []string
// 	err := r.db.Model(&model.RedisImage{}).
// 		Select("version").
// 		Where("is_deleted = ?", 0).
// 		Order("version DESC").
// 		Pluck("version", &versions).
// 		Error
// 	return versions, err
// }

// func (r *imageRepository) ListVersionsAndCPUs(ctx context.Context) ([]map[string]interface{}, error) {
// 	var versionsAndCPUs []map[string]interface{}
// 	err := r.db.Model(&model.RedisImage{}).
// 		Select("version", "cpu").
// 		Where("is_deleted = ? AND status = ?", 0, model.ImageStatusActive).
// 		Order("version DESC").
// 		Find(&versionsAndCPUs).
// 		Error

// 	return versionsAndCPUs, err
// }

// func (r *imageRepository) CheckImageExists(ctx context.Context, imageName string) (bool, error) {
// 	var count int64
// 	err := r.db.Model(&model.RedisImage{}).Where("image_name = ?", imageName).Count(&count).Error
// 	return count > 0, err
// }

// func (r *imageRepository) Create(ctx context.Context, image model.RedisImage) (int64, error) {
// 	result := r.db.Create(&image)
// 	return image.ID, result.Error
// }

// func (r *imageRepository) GetByID(ctx context.Context, id int64) (*model.RedisImage, error) {
// 	var image model.RedisImage
// 	err := r.db.First(&image, id).Error
// 	if err != nil {
// 		return nil, err
// 	}
// 	return &image, nil
// }

func (r *imageRepository) Delete(ctx context.Context, imageID int64) error {
	return r.DB.Model(&model.Image{}).Where("id = ?", imageID).Update("is_deleted", 1).Error
}

// func (r *imageRepository) ChangeStatus(ctx context.Context, imageID int64, status int8) error {
// 	return r.db.Model(&model.RedisImage{}).Where("id = ?", imageID).Update("status", status).Error
// }

// func (r *imageRepository) Update(ctx context.Context, image model.RedisImage) error {
// 	return r.db.Save(&image).Error
// }
