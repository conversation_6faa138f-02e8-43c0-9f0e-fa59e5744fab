package repository

import (
	"ci-gateway/internal/model"
	"context"
	"strings"

	"gorm.io/gorm"
)

// DeployTaskRepository 部署任务
type DeployTaskRepository interface {
	// 部署任务相关
	GetDeployTaskByID(id int64) (*model.DeployTask, error)
	GetDeployTaskByAppID(appID int64, envID int64) ([]*model.DeployTask, error)
	GetDeployTaskByAppAndEnvID(appID, envID int64) ([]*model.DeployTask, error)
	GetDeployTaskByGroupID(groupID int64) ([]*model.DeployTask, error)
	CreateDeployTask(deployTask *model.DeployTask) error
	UpdateDeployTask(deployTask *model.DeployTask) error
	DeleteDeployTask(id int64) error
	QueryDeployTasks(appID, envID int64, status *int, deployType string, groupID *int64, keyword string, page, pageSize int) (int, []*model.DeployTask, error)

	// 部署任务详情相关
	GetDeployTaskDetailByTaskIDAndGroupID(ctx context.Context, taskID, groupID int64) (*model.DeployTaskDetail, error)

	// Workflow相关
	GetTasksWithWorkflow(ctx context.Context) ([]*model.DeployTask, error)
	GetTaskByWorkflowName(ctx context.Context, workflowName string) (*model.DeployTask, error)
	UpdateTaskDetailStatus(ctx context.Context, taskID, groupID int64, status int, message string) error
	GetLatestDeployTaskDetailsByGroupIDs(ctx context.Context, groupIDs []int64) (map[int64]*model.DeployTaskDetail, error)
}

// deployTaskRepository 部署任务仓库实现
type deployTaskRepository struct {
	*Repository
}

// NewDeployTaskRepository 创建部署任务仓库实例
func NewDeployTaskRepository(repository *Repository) DeployTaskRepository {
	return &deployTaskRepository{
		Repository: repository,
	}
}

// GetDeployTaskByID 根据ID获取部署任务
func (r *deployTaskRepository) GetDeployTaskByID(id int64) (*model.DeployTask, error) {
	var deployTask model.DeployTask
	err := r.DB.Where("id = ? AND is_deleted = 0", id).First(&deployTask).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &deployTask, nil
}

// GetDeployTaskByAppID 根据应用ID获取部署任务列表
func (r *deployTaskRepository) GetDeployTaskByAppID(appID int64, envID int64) ([]*model.DeployTask, error) {
	var deployTasks []*model.DeployTask
	err := r.DB.Where("app_id = ? AND env_id = ? AND is_deleted = 0", appID, envID).
		Order("start_time DESC").
		Find(&deployTasks).Error
	if err != nil {
		return nil, err
	}
	return deployTasks, nil
}

// GetDeployTaskByAppAndEnvID 根据应用ID和环境ID获取部署任务列表
func (r *deployTaskRepository) GetDeployTaskByAppAndEnvID(appID, envID int64) ([]*model.DeployTask, error) {
	var deployTasks []*model.DeployTask
	err := r.DB.Where("app_id = ? AND env_id = ? AND is_deleted = 0", appID, envID).
		Order("start_time DESC").
		Find(&deployTasks).Error
	if err != nil {
		return nil, err
	}
	return deployTasks, nil
}

// GetDeployTaskByGroupID 根据分组ID获取部署任务列表
func (r *deployTaskRepository) GetDeployTaskByGroupID(groupID int64) ([]*model.DeployTask, error) {
	var deployTasks []*model.DeployTask
	err := r.DB.Where("group_id = ? AND is_deleted = 0", groupID).
		Order("start_time DESC").
		Find(&deployTasks).Error
	if err != nil {
		return nil, err
	}
	return deployTasks, nil
}

// CreateDeployTask 创建部署任务
func (r *deployTaskRepository) CreateDeployTask(req *model.DeployTask) error {
	return r.DB.Create(req).Error
}

// UpdateDeployTask 更新部署任务
func (r *deployTaskRepository) UpdateDeployTask(deployTask *model.DeployTask) error {
	return r.DB.Save(deployTask).Error
}

// DeleteDeployTask 删除部署任务（软删除）
func (r *deployTaskRepository) DeleteDeployTask(id int64) error {
	return r.DB.Model(&model.DeployTask{}).Where("id = ?", id).Update("is_deleted", 1).Error
}

// QueryDeployTasks 查询部署任务列表（支持分页和过滤）
func (r *deployTaskRepository) QueryDeployTasks(appID, envID int64, status *int, deployType string, groupID *int64, keyword string, page, pageSize int) (int, []*model.DeployTask, error) {
	// 构建查询条件
	query := r.DB.Model(&model.DeployTask{}).Where("app_id = ? AND env_id = ? AND is_deleted = 0", appID, envID)

	// 添加状态过滤
	if status != nil {
		query = query.Where("status = ?", *status)
	}

	// 联表查询详情表以应用部署类型过滤
	if deployType != "" || groupID != nil {
		query = query.Joins("JOIN deploy_task_detail ON deploy_task.id = deploy_task_detail.task_id")

		if deployType != "" {
			query = query.Where("deploy_task_detail.deploy_type = ?", deployType)
		}

		if groupID != nil {
			query = query.Where("deploy_task_detail.group_id = ?", *groupID)
		}
	}

	// 添加关键字搜索
	if keyword != "" {
		keyword = "%" + strings.ToLower(keyword) + "%"
		query = query.Where("(LOWER(deploy_task.semver) LIKE ? OR LOWER(deploy_task.commit_id) LIKE ? OR LOWER(deploy_task.description) LIKE ?)", keyword, keyword, keyword)
	}

	// 查询总数
	var count int64
	err := query.Count(&count).Error
	if err != nil {
		return 0, nil, err
	}

	// 查询分页数据
	var deployTasks []*model.DeployTask
	err = query.Order("t_deploy_task.c_t DESC").
		Limit(pageSize).
		Offset((page - 1) * pageSize).
		Find(&deployTasks).Error
	if err != nil {
		return 0, nil, err
	}

	// 加载任务详情
	if len(deployTasks) > 0 {
		var taskIDs []int64
		for _, task := range deployTasks {
			taskIDs = append(taskIDs, task.ID)
		}

		var taskDetails []*model.DeployTaskDetail
		err = r.DB.Where("task_id IN ?", taskIDs).Find(&taskDetails).Error
		if err != nil {
			return 0, nil, err
		}

		// 将详情关联到任务
		detailsMap := make(map[int64][]*model.DeployTaskDetail)
		for _, detail := range taskDetails {
			detailsMap[detail.TaskID] = append(detailsMap[detail.TaskID], detail)
		}

		for _, task := range deployTasks {
			if details, ok := detailsMap[task.ID]; ok {
				task.TaskDetails = details
			}
		}
	}

	return int(count), deployTasks, nil
}

// GetDeployTaskDetailByTaskIDAndGroupID 根据task_id和group_id获取部署任务详情
func (r *deployTaskRepository) GetDeployTaskDetailByTaskIDAndGroupID(ctx context.Context, taskID, groupID int64) (*model.DeployTaskDetail, error) {
	var taskDetail model.DeployTaskDetail
	err := r.DB.WithContext(ctx).
		Where("task_id = ? AND group_id = ? AND is_deleted = 0", taskID, groupID).
		First(&taskDetail).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}

	return &taskDetail, nil
}

// GetDeployLogsByTaskID 根据部署任务ID获取部署日志列表
func (r *deployTaskRepository) GetDeployLogsByTaskID(deployTaskID int64) ([]*model.DeployLog, error) {
	var deployLogs []*model.DeployLog
	err := r.DB.Where("deploy_task_id = ? AND is_deleted = 0", deployTaskID).
		Order("timestamp ASC").
		Find(&deployLogs).Error
	if err != nil {
		return nil, err
	}
	return deployLogs, nil
}

// CreateDeployLog 创建部署日志
func (r *deployTaskRepository) CreateDeployLog(deployLog *model.DeployLog) error {
	return r.DB.Create(deployLog).Error
}

// BatchCreateDeployLogs 批量创建部署日志
func (r *deployTaskRepository) BatchCreateDeployLogs(deployLogs []*model.DeployLog) error {
	return r.DB.Create(deployLogs).Error
}

// DeleteDeployLogsByTaskID 根据部署任务ID删除部署日志（软删除）
func (r *deployTaskRepository) DeleteDeployLogsByTaskID(deployTaskID int64) error {
	return r.DB.Model(&model.DeployLog{}).Where("deploy_task_id = ?", deployTaskID).Update("is_deleted", 1).Error
}

// GetTasksWithWorkflow 获取关联了工作流且状态为进行中的任务
func (r *deployTaskRepository) GetTasksWithWorkflow(ctx context.Context) ([]*model.DeployTask, error) {
	var tasks []*model.DeployTask
	err := r.DB.WithContext(ctx).
		Where("workflow_name != '' AND workflow_name IS NOT NULL AND status = ? AND is_deleted = 0",
			model.DeployTaskStatusRunning).
		Find(&tasks).Error

	return tasks, err
}

// GetTaskByWorkflowName 根据工作流名称获取部署任务
func (r *deployTaskRepository) GetTaskByWorkflowName(ctx context.Context, workflowName string) (*model.DeployTask, error) {
	var task model.DeployTask
	err := r.DB.WithContext(ctx).
		Where("workflow_name = ? AND is_deleted = 0", workflowName).
		First(&task).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}

	return &task, nil
}

// UpdateTaskDetailStatus 更新部署任务详情状态
func (r *deployTaskRepository) UpdateTaskDetailStatus(ctx context.Context, taskID, groupID int64, status int, message string) error {
	tx := r.DB.WithContext(ctx).Begin()
	if tx.Error != nil {
		return tx.Error
	}

	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 更新任务详情状态
	err := tx.Model(&model.DeployTaskDetail{}).
		Where("task_id = ? AND group_id = ? AND is_deleted = 0", taskID, groupID).
		Updates(map[string]interface{}{
			"status":       status,
			"current_step": message,
		}).Error

	if err != nil {
		tx.Rollback()
		return err
	}

	// 记录日志
	if message != "" {
		// 查询详情ID
		var detail model.DeployTaskDetail
		err := tx.Where("task_id = ? AND group_id = ? AND is_deleted = 0", taskID, groupID).
			First(&detail).Error

		if err == nil {
			logType := "info"
			if status == model.DeployTaskDetailStatusFailed {
				logType = "error"
			} else if status == model.DeployTaskDetailStatusCompleted {
				logType = "success"
			}

			deployLog := &model.DeployLog{
				DeployTaskDetailID: detail.ID,
				LogType:            logType,
				Content:            message,
				Timestamp:          detail.UT,
				Step:               detail.CurrentStep,
				StepIndex:          detail.CurrentStepIndex,
				CreateBy:           0, // 系统更新
				UpdateBy:           0, // 系统更新
			}

			err = tx.Create(deployLog).Error
			if err != nil {
				tx.Rollback()
				return err
			}
		}
	}

	// 更新主任务状态计数
	var completedCount, failedCount int64
	tx.Model(&model.DeployTaskDetail{}).
		Where("task_id = ? AND status = ? AND is_deleted = 0", taskID, model.DeployTaskDetailStatusCompleted).
		Count(&completedCount)

	tx.Model(&model.DeployTaskDetail{}).
		Where("task_id = ? AND status = ? AND is_deleted = 0", taskID, model.DeployTaskDetailStatusFailed).
		Count(&failedCount)

	// 更新主任务统计
	err = tx.Model(&model.DeployTask{}).
		Where("id = ?", taskID).
		Updates(map[string]interface{}{
			"task_success": completedCount,
			"task_failed":  failedCount,
		}).Error

	if err != nil {
		tx.Rollback()
		return err
	}

	return tx.Commit().Error
}

// GetLatestDeployTaskDetailsByTaskIDAndGroupIDs 根据 taskID 和多个 groupID 获取每个 groupID 的最新 DeployTaskDetail 记录
func (r *deployTaskRepository) GetLatestDeployTaskDetailsByTaskIDAndGroupIDs(ctx context.Context, taskID int64, groupIDs []int64) (map[int64]*model.DeployTaskDetail, error) {
	if len(groupIDs) == 0 {
		return map[int64]*model.DeployTaskDetail{}, nil
	}

	var details []model.DeployTaskDetail
	err := r.DB.WithContext(ctx).
		Where("task_id = ? AND group_id IN ? AND is_deleted = 0", taskID, groupIDs).
		Order("group_id ASC, id DESC").
		Find(&details).Error
	if err != nil {
		return nil, err
	}

	result := make(map[int64]*model.DeployTaskDetail)
	for _, detail := range details {
		// 只保留每个 group_id 的第一条（即最新的）
		if _, exists := result[detail.GroupID]; !exists {
			copyDetail := detail // 防止引用覆盖
			result[detail.GroupID] = &copyDetail
		}
	}
	return result, nil
}

// GetLatestDeployTaskDetailsByGroupIDs 根据多个 group_id 获取每个 group_id 的最新 DeployTaskDetail 记录
func (r *deployTaskRepository) GetLatestDeployTaskDetailsByGroupIDs(ctx context.Context, groupIDs []int64) (map[int64]*model.DeployTaskDetail, error) {
	if len(groupIDs) == 0 {
		return map[int64]*model.DeployTaskDetail{}, nil
	}

	// SELECT t1.*
	// FROM t_deploy_task_detail t1
	// INNER JOIN (
	// 	SELECT group_id, MAX(id) AS max_id
	// 	FROM t_deploy_task_detail
	// 	WHERE group_id IN (1,2,3,4) AND is_deleted = 0
	// 	GROUP BY group_id
	// ) t2 ON t1.group_id = t2.group_id AND t1.id = t2.max_id

	var details []model.DeployTaskDetail
	// 子查询获取每个 group_id 的最大 id
	subQuery := r.DB.Model(&model.DeployTaskDetail{}).
		Select("group_id, MAX(id) as max_id").
		Where("group_id IN ? AND is_deleted = 0", groupIDs).
		Group("group_id")

	err := r.DB.WithContext(ctx).
		Joins("JOIN (?) t2 ON t_deploy_task_detail.group_id = t2.group_id AND t_deploy_task_detail.id = t2.max_id", subQuery).
		Find(&details).Error
	if err != nil {
		return nil, err
	}

	result := make(map[int64]*model.DeployTaskDetail)
	for _, detail := range details {
		copyDetail := detail
		result[detail.GroupID] = &copyDetail
	}
	return result, nil
}
