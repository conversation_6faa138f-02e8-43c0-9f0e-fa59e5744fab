package repository

import (
	"ci-gateway/internal/model"
)

type EnvironmentVariableRepository interface {
	GetEnvironmentVariablesByAppAndEnvID(appID, envID int64) ([]*model.EnvironmentVariable, error)
	CreateEnvironmentVariable(variable *model.EnvironmentVariable) error
	UpdateEnvironmentVariable(variable *model.EnvironmentVariable) error
}

type environmentVariableRepository struct {
	*Repository
}

func NewEnvironmentVariableRepository(repository *Repository) EnvironmentVariableRepository {
	return &environmentVariableRepository{
		Repository: repository,
	}
}

func (r *environmentVariableRepository) GetEnvironmentVariablesByAppAndEnvID(appID, envID int64) ([]*model.EnvironmentVariable, error) {
	var variables []*model.EnvironmentVariable
	err := r.DB.Where("app_id = ? AND env_id = ? AND status = 1", appID, envID).Find(&variables).Error
	if err != nil {
		return nil, err
	}
	return variables, nil
}

func (r *environmentVariableRepository) CreateEnvironmentVariable(variable *model.EnvironmentVariable) error {
	return r.DB.Create(variable).Error
}

func (r *environmentVariableRepository) UpdateEnvironmentVariable(variable *model.EnvironmentVariable) error {
	return r.DB.Save(variable).Error
}
