package repository

import (
	"time"

	"github.com/spf13/viper"
	"go.uber.org/zap"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

// DatabaseManager 管理多个数据源
type DatabaseManager struct {
	deployDB *gorm.DB
	cmdbDB   *gorm.DB
	logger   *zap.Logger
}

// NewDatabaseManager 创建数据库管理器
func NewDatabaseManager(conf *viper.Viper, logger *zap.Logger) (*DatabaseManager, error) {
	deployDB, err := createDatabase(conf, logger, "data.mysql.deploy")
	if err != nil {
		return nil, err
	}

	cmdbDB, err := createDatabase(conf, logger, "data.mysql.cmdb")
	if err != nil {
		return nil, err
	}

	return &DatabaseManager{
		deployDB: deployDB,
		cmdbDB:   cmdbDB,
		logger:   logger,
	}, nil
}

// GetDeployDB 获取deploy数据库连接
func (dm *DatabaseManager) GetDeployDB() *gorm.DB {
	return dm.deployDB
}

// GetCmdbDB 获取cmdb数据库连接
func (dm *DatabaseManager) GetCmdbDB() *gorm.DB {
	return dm.cmdbDB
}

// createDatabase 创建数据库连接
func createDatabase(conf *viper.Viper, logger *zap.Logger, configKey string) (*gorm.DB, error) {
	dsn := conf.GetString(configKey)
	if dsn == "" {
		logger.Fatal("数据库配置不能为空", zap.String("configKey", configKey))
	}

	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{})
	if err != nil {
		logger.Error("连接数据库失败", zap.String("configKey", configKey), zap.Error(err))
		return nil, err
	}

	// 设置连接池参数
	sqlDB, err := db.DB()
	if err != nil {
		logger.Error("获取底层数据库连接失败", zap.Error(err))
		return nil, err
	}

	// 设置连接池配置
	maxIdleConns := conf.GetInt("mysql.max_idle_conns")
	if maxIdleConns == 0 {
		maxIdleConns = 10
	}
	sqlDB.SetMaxIdleConns(maxIdleConns)

	maxOpenConns := conf.GetInt("mysql.max_open_conns")
	if maxOpenConns == 0 {
		maxOpenConns = 100
	}
	sqlDB.SetMaxOpenConns(maxOpenConns)

	connMaxLifetime := conf.GetDuration("mysql.conn_max_lifetime")
	if connMaxLifetime == 0 {
		connMaxLifetime = time.Hour
	}
	sqlDB.SetConnMaxLifetime(connMaxLifetime)

	logger.Info("成功连接数据库", zap.String("configKey", configKey))
	return db, nil
}

type Repository struct {
	*gorm.DB
	cmdbDB *gorm.DB
	logger *zap.Logger
}

func NewRepository(logger *zap.Logger, dbManager *DatabaseManager) *Repository {
	return &Repository{
		DB:     dbManager.GetDeployDB(),
		cmdbDB: dbManager.GetCmdbDB(),
		logger: logger,
	}
}

// GetDeployDB 获取deploy数据库连接
func (r *Repository) GetDeployDB() *gorm.DB {
	return r.DB
}

// GetCmdbDB 获取cmdb数据库连接
func (r *Repository) GetCmdbDB() *gorm.DB {
	return r.cmdbDB
}

// 兼容旧版本，保留原有方法
func NewDb(conf *viper.Viper, l *zap.Logger) *gorm.DB {
	dsn := conf.GetString("data.mysql.deploy")
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{})
	if err != nil {
		return nil
	}

	// Set connection pool settings
	sqlDB, err := db.DB()
	if err != nil {
		return nil
	}

	sqlDB.SetMaxIdleConns(conf.GetInt("mysql.max_idle_conns"))
	sqlDB.SetMaxOpenConns(conf.GetInt("mysql.max_open_conns"))
	sqlDB.SetConnMaxLifetime(conf.GetDuration("mysql.conn_max_lifetime"))

	l.Info("成功连接数据库")
	return db
}

// ExecuteInTransaction executes the given function within a database transaction
func (r *Repository) ExecuteInTransaction(txFunc func(*gorm.DB) error) error {
	return r.DB.Transaction(func(tx *gorm.DB) error {
		return txFunc(tx)
	})
}

// ExecuteInDeployTransaction 在deploy数据库中执行事务
func (r *Repository) ExecuteInDeployTransaction(txFunc func(*gorm.DB) error) error {
	return r.DB.Transaction(func(tx *gorm.DB) error {
		return txFunc(tx)
	})
}

// ExecuteInCmdbTransaction 在cmdb数据库中执行事务
func (r *Repository) ExecuteInCmdbTransaction(txFunc func(*gorm.DB) error) error {
	return r.cmdbDB.Transaction(func(tx *gorm.DB) error {
		return txFunc(tx)
	})
}
