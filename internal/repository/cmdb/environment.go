package cmdb

import (
	"ci-gateway/internal/model/cmdb"
	"ci-gateway/internal/repository"

	"gorm.io/gorm"
)

type EnvironmentRepository interface {
	GetEnvironmentByID(id int64) (*cmdb.Environment, error)
	GetEnvironmentByCode(code string) (*cmdb.Environment, error)
	CreateEnvironment(env *cmdb.Environment) error
	UpdateEnvironment(env *cmdb.Environment) error
	DeleteEnvironment(id int64) error
	ListEnvironments() ([]*cmdb.Environment, error)
}

type environmentRepository struct {
	*repository.Repository
}

func NewEnvironmentRepository(repository *repository.Repository) EnvironmentRepository {
	return &environmentRepository{
		Repository: repository,
	}
}

func (r *environmentRepository) GetEnvironmentByID(id int64) (*cmdb.Environment, error) {
	var env cmdb.Environment
	err := r.GetCmdbDB().Where("id = ? AND status = 1", id).First(&env).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &env, nil
}

func (r *environmentRepository) GetEnvironmentByCode(code string) (*cmdb.Environment, error) {
	var env cmdb.Environment
	err := r.GetCmdbDB().Where("code = ? AND status = 1", code).First(&env).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &env, nil
}

func (r *environmentRepository) CreateEnvironment(env *cmdb.Environment) error {
	return r.GetCmdbDB().Create(env).Error
}

func (r *environmentRepository) UpdateEnvironment(env *cmdb.Environment) error {
	return r.GetCmdbDB().Save(env).Error
}

func (r *environmentRepository) DeleteEnvironment(id int64) error {
	return r.GetCmdbDB().Model(&cmdb.Environment{}).Where("id = ?", id).Update("status", 0).Error
}

func (r *environmentRepository) ListEnvironments() ([]*cmdb.Environment, error) {
	var envs []*cmdb.Environment
	err := r.GetCmdbDB().Where("status = 1").Find(&envs).Error
	return envs, err
}
