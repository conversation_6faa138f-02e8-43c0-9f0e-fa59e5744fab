package cmdb

import (
	"ci-gateway/internal/model/cmdb"
	"ci-gateway/internal/repository"

	"gorm.io/gorm"
)

type ApplicationRepository interface {
	GetApplicationByCode(code string) (*cmdb.Application, error)
	GetApplicationByID(id int64) (*cmdb.Application, error)
	GetApplicationBySrvTreeID(srvTreeID int64) (*cmdb.Application, error)
}

type applicationRepository struct {
	*repository.Repository
}

func NewApplicationRepository(repository *repository.Repository) ApplicationRepository {
	return &applicationRepository{
		Repository: repository,
	}
}

func (r *applicationRepository) GetApplicationByCode(code string) (*cmdb.Application, error) {
	var app cmdb.Application
	err := r.GetCmdbDB().Where("code = ? AND status = 1", code).First(&app).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &app, nil
}

func (r *applicationRepository) GetApplicationByID(id int64) (*cmdb.Application, error) {
	var app cmdb.Application
	err := r.GetCmdbDB().Where("id = ? AND status = 1", id).First(&app).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &app, nil
}

func (r *applicationRepository) GetApplicationBySrvTreeID(srvTreeID int64) (*cmdb.Application, error) {
	var app cmdb.Application
	err := r.GetCmdbDB().Where("srvtree_id = ? AND status = 1", srvTreeID).First(&app).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}

	// 查询应用类型信息
	if app.AppTypeID > 0 {
		var appType cmdb.AppType
		err := r.GetCmdbDB().Where("id = ? AND status = 1", app.AppTypeID).First(&appType).Error
		if err == nil {
			app.AppType = &appType
		} else if err != gorm.ErrRecordNotFound {
			return nil, err
		}
	}

	return &app, nil
}
