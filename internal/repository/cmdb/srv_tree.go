package cmdb

import (
	"ci-gateway/internal/model/cmdb"
	"ci-gateway/internal/repository"

	"gorm.io/gorm"
)

type SrvTreeRepository interface {
	GetSrvTreeByID(id int64) (*cmdb.SrvTree, error)
	GetSrvTreeByCode(code string) (*cmdb.SrvTree, error)
	GetSrvTreeByNodeCode(nodeCode string) (*cmdb.SrvTree, error)
	GetSrvTreesByParentID(parentID int64) ([]*cmdb.SrvTree, error)
	GetSrvTreesByType(treeType string) ([]*cmdb.SrvTree, error)
	GetSrvTreesByReferID(referID int64) ([]*cmdb.SrvTree, error)
	CreateSrvTree(tree *cmdb.SrvTree) error
	UpdateSrvTree(tree *cmdb.SrvTree) error
	DeleteSrvTree(id int64) error
	ListSrvTrees(limit, offset int) ([]*cmdb.SrvTree, error)
}

type srvTreeRepository struct {
	*repository.Repository
}

func NewSrvTreeRepository(repository *repository.Repository) SrvTreeRepository {
	return &srvTreeRepository{
		Repository: repository,
	}
}

func (r *srvTreeRepository) GetSrvTreeByID(id int64) (*cmdb.SrvTree, error) {
	var tree cmdb.SrvTree
	err := r.GetCmdbDB().Where("id = ? AND status = 1", id).First(&tree).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &tree, nil
}

func (r *srvTreeRepository) GetSrvTreeByCode(code string) (*cmdb.SrvTree, error) {
	var tree cmdb.SrvTree
	err := r.GetCmdbDB().Where("code = ? AND status = 1", code).First(&tree).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &tree, nil
}

func (r *srvTreeRepository) GetSrvTreeByNodeCode(nodeCode string) (*cmdb.SrvTree, error) {
	var tree cmdb.SrvTree
	err := r.GetCmdbDB().Where("node_code = ? AND status = 1", nodeCode).First(&tree).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &tree, nil
}

func (r *srvTreeRepository) GetSrvTreesByParentID(parentID int64) ([]*cmdb.SrvTree, error) {
	var trees []*cmdb.SrvTree
	err := r.GetCmdbDB().Where("parent_id = ? AND status = 1", parentID).Find(&trees).Error
	return trees, err
}

func (r *srvTreeRepository) GetSrvTreesByType(treeType string) ([]*cmdb.SrvTree, error) {
	var trees []*cmdb.SrvTree
	err := r.GetCmdbDB().Where("type = ? AND status = 1", treeType).Find(&trees).Error
	return trees, err
}

func (r *srvTreeRepository) GetSrvTreesByReferID(referID int64) ([]*cmdb.SrvTree, error) {
	var trees []*cmdb.SrvTree
	err := r.GetCmdbDB().Where("refer_id = ? AND status = 1", referID).Find(&trees).Error
	return trees, err
}

func (r *srvTreeRepository) CreateSrvTree(tree *cmdb.SrvTree) error {
	return r.GetCmdbDB().Create(tree).Error
}

func (r *srvTreeRepository) UpdateSrvTree(tree *cmdb.SrvTree) error {
	return r.GetCmdbDB().Save(tree).Error
}

func (r *srvTreeRepository) DeleteSrvTree(id int64) error {
	return r.GetCmdbDB().Model(&cmdb.SrvTree{}).Where("id = ?", id).Update("status", 0).Error
}

func (r *srvTreeRepository) ListSrvTrees(limit, offset int) ([]*cmdb.SrvTree, error) {
	var trees []*cmdb.SrvTree
	err := r.GetCmdbDB().Where("status = 1").Limit(limit).Offset(offset).Find(&trees).Error
	return trees, err
}
