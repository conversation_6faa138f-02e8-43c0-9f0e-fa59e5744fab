package repository

import (
	"context"
	"errors"

	"ci-gateway/internal/model"

	"gorm.io/gorm"
)

// ContainerRepository 容器配置仓库接口
type ContainerRepository interface {
	// 查询操作
	GetContainerByGroupID(ctx context.Context, groupID int64) (*model.Container, error)
	GetContainerByAppAndEnv(ctx context.Context, appID, envID int64) (*model.Container, error)
	GetContainersByAppAndEnv(ctx context.Context, appID, envID int64) ([]*model.Container, error)

	// 写入操作
	CreateContainer(ctx context.Context, container *model.Container) error
	UpdateContainer(ctx context.Context, container *model.Container) error
	DeleteContainer(ctx context.Context, appID, envID int64) error
}

type containerRepository struct {
	*Repository
}

// NewContainerRepository 创建容器配置仓库
func NewContainerRepository(repository *Repository) ContainerRepository {
	return &containerRepository{
		Repository: repository,
	}
}

// GetContainersByGroupID 通过分组ID获取容器配置列表
func (r *containerRepository) GetContainerByGroupID(ctx context.Context, groupID int64) (*model.Container, error) {
	var container model.Container

	err := r.DB.WithContext(ctx).
		Where("status = 1 AND (group_id = ? OR group_id = 0)", groupID).
		Order("group_id DESC").
		First(&container).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}

	return &container, nil
}

// GetContainerByAppAndEnv 根据应用ID和环境ID获取容器配置（应用级别配置，group_id=0）
func (r *containerRepository) GetContainerByAppAndEnv(ctx context.Context, appID, envID int64) (*model.Container, error) {
	var container model.Container

	err := r.DB.WithContext(ctx).
		Where("app_id = ? AND env_id = ? AND group_id = 0 AND status = 1 AND is_deleted = 0", appID, envID).
		First(&container).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}

	return &container, nil
}

// GetContainersByAppAndEnv 根据应用ID和环境ID获取所有容器配置列表
func (r *containerRepository) GetContainersByAppAndEnv(ctx context.Context, appID, envID int64) ([]*model.Container, error) {
	var containers []*model.Container

	err := r.DB.WithContext(ctx).
		Where("app_id = ? AND env_id = ? AND status = 1 AND is_deleted = 0", appID, envID).
		Order("group_id ASC").
		Find(&containers).Error

	if err != nil {
		return nil, err
	}

	return containers, nil
}

// CreateContainer 创建容器配置
func (r *containerRepository) CreateContainer(ctx context.Context, container *model.Container) error {
	return r.DB.WithContext(ctx).Create(container).Error
}

// UpdateContainer 更新容器配置
func (r *containerRepository) UpdateContainer(ctx context.Context, container *model.Container) error {
	return r.DB.WithContext(ctx).
		Where("app_id = ? AND env_id = ? AND group_id = ? AND is_deleted = 0", container.AppID, container.EnvID, container.GroupID).
		Updates(container).Error
}

// DeleteContainer 删除容器配置（软删除）
func (r *containerRepository) DeleteContainer(ctx context.Context, appID, envID int64) error {
	return r.DB.WithContext(ctx).
		Model(&model.Container{}).
		Where("app_id = ? AND env_id = ? AND group_id = 0 AND is_deleted = 0", appID, envID).
		Update("is_deleted", 1).Error
}
