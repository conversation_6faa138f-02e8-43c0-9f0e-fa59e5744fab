package server

import (
	"strings"

	"ci-gateway/internal/handler"
	"ci-gateway/internal/middleware"
	"ci-gateway/internal/repository"
	pkghttp "ci-gateway/pkg/http"

	"github.com/gin-contrib/sessions"
	"github.com/gin-contrib/sessions/cookie"
	"github.com/gin-gonic/contrib/static"
	"github.com/gin-gonic/gin"
	"github.com/spf13/viper"
	"go.uber.org/zap"
)

func NewServerHTTP(
	logger *zap.Logger,
	config *viper.Viper,
	healthHandler *handler.HealthHandler,
	srvTreeHandler *handler.Srv<PERSON>reeHandler,
	imageHandler *handler.ImageHandler,
	appHandler *handler.AppHandler,
	configHandler *handler.ConfigHandler,
	deployTaskHandler *handler.DeployTaskHandler,
	groupHandler *handler.GroupHandler,
	environmentVariableHandler *handler.EnvironmentVariableHandler,
	probeHandler *handler.<PERSON>be<PERSON><PERSON><PERSON>,
	gitlabHandler *handler.Git<PERSON>ab<PERSON><PERSON><PERSON>,
	buildConfigHandler *handler.Build<PERSON>onfig<PERSON><PERSON><PERSON>,
	podHandler *handler.PodHandler,
	containerHandler *handler.ContainerHandler,
	userRepo repository.UserRepository,
) *pkghttp.Server {
	gin.SetMode(gin.ReleaseMode)

	engine := gin.Default()

	r := pkghttp.NewServer(
		engine,
		logger,
		pkghttp.WithServerHost(config.GetString("server.http.addr")),
		pkghttp.WithServerPort(config.GetInt("server.http.port")),
	)

	// 健康检查保持GET方式（不需要详细日志）
	r.Engine.GET("/health/startupProbe", healthHandler.StartupProbe)
	r.Engine.GET("/health/readiness", healthHandler.ReadinessProbe)
	r.Engine.GET("/health/liveness", healthHandler.LivenessProbe)

	store := cookie.NewStore([]byte("secret"))
	r.Engine.Use(sessions.Sessions("session", store))

	api := r.Engine.Group("/api")

	// 为Argo Workflow回调提供一个不需要认证的端点
	api.POST("/callback/deploytask", deployTaskHandler.UpdateTaskDetailStatus)
	// 获取部署配置的回调端点（不需要认证）
	api.GET("/callback/deployconfig", deployTaskHandler.GetDeployConfig)

	// 为API添加日志中间件
	api.Use(middleware.RequestResponseLoggingMiddleware(logger))
	api.Use(sessions.Sessions("session", store))
	{
		// 应用认证中间件
		authenticated := api.Group("/")
		authenticated.Use(middleware.AuthMiddleware(logger, config, userRepo))
		{
			image := authenticated.Group("/images")
			{
				image.GET("/list", imageHandler.ListImages)
				image.DELETE("/del/:id", imageHandler.DelImages)
				// image.PUT("/update/:id", imageHandler.UpdateImages)
			}

			// 获取服务树
			authenticated.POST("/srvTree/tree", srvTreeHandler.GetSrvTree)

			// 根据环境ID和服务树节点ID获取应用及其分组信息
			authenticated.POST("/srvTree/appInfo", appHandler.GetAppInfoByNodeID)

			// 根据应用ID和环境ID获取配置文件列表
			authenticated.POST("/config/getFileList", configHandler.GetConfigFilesByAppAndEnvID)

			// 部署任务相关路由
			authenticated.POST("/deploytask/create", deployTaskHandler.CreateDeployTask)
			authenticated.GET("/deploytask/list", deployTaskHandler.GetDeployTasks)
			authenticated.GET("/deploytask/query", deployTaskHandler.QueryDeployTasks)
			authenticated.GET("/deploytask/getDetails", deployTaskHandler.GetDeployTaskDetail)

			// 根据配置文件ID获取配置文件内容
			authenticated.POST("/configfile/getContent", configHandler.GetConfigContentByFileID)

			// 创建配置文件
			authenticated.POST("/configfile/create", configHandler.CreateConfigFile)

			authenticated.GET("/group/list", groupHandler.ListGroups)
			authenticated.POST("/group/create", groupHandler.CreateGroup)
			authenticated.POST("/group/delete", groupHandler.DeleteGroup)

			// 运行时设置相关路由
			authenticated.GET("/runtime/probes", probeHandler.GetProbesByAppAndEnv)

			// 容器配置相关路由
			container := authenticated.Group("/container")
			{
				container.GET("/config", containerHandler.GetContainerConfig)
				container.GET("/config/list", containerHandler.GetContainerConfigList)
				container.POST("/config", containerHandler.CreateContainerConfig)
				container.PUT("/config", containerHandler.UpdateContainerConfig)
				container.DELETE("/config", containerHandler.DeleteContainerConfig)
				container.POST("/config/validate", containerHandler.ValidateContainerConfig)
				container.POST("/config/preview", containerHandler.PreviewContainerYAML)
			}

			// 构建配置相关路由
			authenticated.GET("/build/templates", buildConfigHandler.GetBuildTemplates)
			authenticated.GET("/build/templates/:id", buildConfigHandler.GetBuildTemplateByID)
			authenticated.GET("/build/templates/default", buildConfigHandler.GetDefaultTemplate)
			authenticated.GET("/build/config", buildConfigHandler.GetProjectBuildConfig)
			authenticated.POST("/build/config/create", buildConfigHandler.CreateProjectBuildConfig)
			authenticated.POST("/build/config/update", buildConfigHandler.UpdateProjectBuildConfig)
			authenticated.POST("/build/config/delete", buildConfigHandler.DeleteProjectBuildConfig)
			authenticated.POST("/build/config/preview", buildConfigHandler.PreviewBuildConfig)

			authenticated.GET("/environment_variable/list", environmentVariableHandler.ListEnvironmentVariables)
			authenticated.POST("/environment_variable/create", environmentVariableHandler.CreateEnvironmentVariable)
			authenticated.POST("/environment_variable/update", environmentVariableHandler.UpdateEnvironmentVariable)

			// GitLab相关路由
			authenticated.GET("/gitlab/semvers", gitlabHandler.GetSemvers)

			// Pod相关路由
			pods := authenticated.Group("/pods")
			{
				// 基础功能
				pods.GET("/list", podHandler.ListPods)
				pods.GET("/metrics", podHandler.GetPodMetrics)

				// 重启功能（统一接口）
				pods.POST("/restart", podHandler.RestartPods)

				// 终端相关
				pods.POST("/terminal/start", podHandler.StartTerminal)
				pods.POST("/terminal/stop/:namespace/:pod_name", podHandler.StopTerminal)
				pods.POST("/terminal/resize", podHandler.ResizeTerminal)

				// Java相关
				pods.POST("/java/heap-dump", podHandler.DumpJavaHeap)
				pods.GET("/java/processes/:namespace/:pod_name", podHandler.ListJavaProcesses)

				// Arthas相关
				pods.POST("/arthas/start", podHandler.StartArthas)
				pods.POST("/arthas/stop/:namespace/:pod_name", podHandler.StopArthas)
				pods.GET("/arthas/status/:namespace/:pod_name", podHandler.GetArthasStatus)
			}
		}
	}

	r.Engine.Use(static.Serve("/", static.LocalFile("./webapp/dist", true)))

	// 处理前端路由
	r.Engine.NoRoute(func(c *gin.Context) {
		if !strings.HasPrefix(c.Request.URL.Path, "/api") {
			c.File("./webapp/dist/index.html")
		} else {
			c.Status(404)
		}
	})

	return r
}
