package handler

import (
	"net/http"

	"ci-gateway/internal/service"

	"github.com/gin-gonic/gin"
)

// ProbeHandler 探针处理器
type ProbeHandler struct {
	*Handler
	probeService service.ProbeService
}

// NewProbeHandler 创建探针处理器
func NewProbeHandler(
	handler *Handler,
	probeService service.ProbeService,
) *ProbeHandler {
	return &ProbeHandler{
		Handler:      handler,
		probeService: probeService,
	}
}

// GetProbeRequest 获取探针配置请求
type GetProbeRequest struct {
	AppID int64 `form:"app_id" binding:"required"`
	EnvID int64 `form:"env_id" binding:"required"`
}

// GetProbesByAppAndEnv 根据应用ID和环境ID获取探针配置
func (h *ProbeHandler) GetProbesByAppAndEnv(c *gin.Context) {
	var req GetProbeRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		h.handleError(c, http.StatusBadRequest, "参数错误", err)
		return
	}

	probes, err := h.probeService.GetProbesByAppAndEnv(c, req.AppID, req.EnvID)
	if err != nil {
		h.handleError(c, http.StatusInternalServerError, "获取探针配置失败", err)
		return
	}

	h.handleSuccess(c, probes)
}
