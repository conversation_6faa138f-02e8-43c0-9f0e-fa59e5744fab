package handler

import (
	"ci-gateway/internal/dto"
	"ci-gateway/internal/model"
	"ci-gateway/internal/service"
	pkghttp "ci-gateway/pkg/http"
	"net/http"

	"github.com/gin-contrib/sessions"
	"github.com/gin-gonic/gin"
)

// EnvironmentVariableHandler 环境变量处理器
type EnvironmentVariableHandler struct {
	*Handler
	envService service.EnvironmentVariableService
}

// NewEnvironmentVariableHandler 创建环境变量处理器
func NewEnvironmentVariableHandler(envService service.EnvironmentVariableService, handler *Handler) *EnvironmentVariableHandler {
	return &EnvironmentVariableHandler{
		Handler:    handler,
		envService: envService,
	}
}

// ListEnvs 获取环境列表
func (h *EnvironmentVariableHandler) ListEnvironmentVariables(c *gin.Context) {
	var req = struct {
		AppID int64 `form:"app_id"`
		EnvID int64 `form:"env_id"`
	}{}
	if err := c.Should<PERSON>ind<PERSON>uery(&req); err != nil {
		pkghttp.HandleError(c, http.StatusBadRequest, "参数错误", nil)
		return
	}

	environmentVariables, _ := h.envService.GetEnvironmentVariablesByAppAndEnvID(req.AppID, req.EnvID)

	pkghttp.HandleSuccess(c, environmentVariables)
}

func (h *EnvironmentVariableHandler) CreateEnvironmentVariable(c *gin.Context) {
	var req dto.CreateEnvironmentVariableRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		pkghttp.HandleError(c, http.StatusBadRequest, "参数错误", nil)
		return
	}

	session := sessions.Default(c)
	user := session.Get("user").(*model.User)

	environmentVariable, err := h.envService.CreateEnvironmentVariable(req, user.ID)
	if err != nil {
		pkghttp.HandleError(c, http.StatusInternalServerError, "创建环境变量失败", nil)
		return
	}

	pkghttp.HandleSuccess(c, environmentVariable)
}

func (h *EnvironmentVariableHandler) UpdateEnvironmentVariable(c *gin.Context) {
	var req dto.UpdateEnvironmentVariableRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		pkghttp.HandleError(c, http.StatusBadRequest, "参数错误", nil)
		return
	}

	session := sessions.Default(c)
	user := session.Get("user").(*model.User)

	environmentVariable, err := h.envService.UpdateEnvironmentVariable(req, user.ID)
	if err != nil {
		pkghttp.HandleError(c, http.StatusInternalServerError, "更新环境变量失败", nil)
		return
	}

	pkghttp.HandleSuccess(c, environmentVariable)
}
