package handler

import (
	"context"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"ci-gateway/internal/dto"
	"ci-gateway/internal/service"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type PodHandler struct {
	*Handler
	podService service.PodService
}

func NewPodHandler(handler *Handler, podService service.PodService) *PodHandler {
	return &PodHandler{
		Handler:    handler,
		podService: podService,
	}
}

// ListPods 获取Pod列表
func (h *PodHandler) ListPods(c *gin.Context) {
	namespace := c.Query("namespace")
	if namespace == "" {
		h.handleError(c, http.StatusBadRequest, "namespace参数是必需的", fmt.Errorf("missing namespace"))
		return
	}

	appID, _ := strconv.ParseInt(c.Query("app_id"), 10, 64)
	envID, _ := strconv.ParseInt(c.Query("env_id"), 10, 64)

	req := &service.ListPodsRequest{
		Namespace: namespace,
		AppID:     appID,
		EnvID:     envID,
	}

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	result, err := h.podService.ListPods(ctx, req)
	if err != nil {
		h.logger.Error("获取Pod列表失败", zap.Error(err))
		h.handleError(c, http.StatusInternalServerError, "获取Pod列表失败", err)
		return
	}

	h.handleSuccess(c, result)
}

// GetPodMetrics 获取Pod指标
func (h *PodHandler) GetPodMetrics(c *gin.Context) {
	namespace := c.Query("namespace")
	podName := c.Query("pod_name")

	if namespace == "" || podName == "" {
		h.handleError(c, http.StatusBadRequest, "namespace和pod_name参数是必需的", fmt.Errorf("missing required parameters"))
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	metrics, err := h.podService.GetPodMetrics(ctx, namespace, podName)
	if err != nil {
		h.logger.Error("获取Pod指标失败", zap.Error(err))
		h.handleError(c, http.StatusInternalServerError, "获取Pod指标失败", err)
		return
	}

	h.handleSuccess(c, metrics)
}

// RestartPods 重启Pod（统一接口）
func (h *PodHandler) RestartPods(c *gin.Context) {
	var req dto.PodRestartRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.handleError(c, http.StatusBadRequest, "请求参数错误", err)
		return
	}

	h.logger.Info("收到重启Pod请求",
		zap.String("namespace", req.Namespace),
		zap.Int("podCount", len(req.PodNames)),
		zap.String("strategy", string(req.Strategy)))

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Minute)
	defer cancel()

	result, err := h.podService.RestartPods(ctx, &req)
	if err != nil {
		h.logger.Error("重启Pod失败", zap.Error(err))
		h.handleError(c, http.StatusInternalServerError, "重启Pod失败", err)
		return
	}

	h.logger.Info("Pod重启完成",
		zap.Int("totalPods", result.TotalPods),
		zap.Int("successfulPods", result.SuccessfulPods),
		zap.Int("failedPods", len(result.FailedPods)))

	h.handleSuccess(c, result)
}

// StartTerminal 启动终端会话
func (h *PodHandler) StartTerminal(c *gin.Context) {
	var req dto.PodTerminalStartRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.handleError(c, http.StatusBadRequest, "请求参数错误", err)
		return
	}

	// 设置默认值
	if req.ContainerName == "" {
		req.ContainerName = "app"
	}
	if req.Shell == "" {
		req.Shell = "/bin/bash"
	}
	if req.Cols == 0 {
		req.Cols = 80
	}
	if req.Rows == 0 {
		req.Rows = 24
	}
	if req.Timeout == 0 {
		req.Timeout = 3600
	}

	// 转换为service层请求
	serviceReq := &service.StartTerminalRequest{
		Namespace:     req.Namespace,
		PodName:       req.PodName,
		ContainerName: req.ContainerName,
		Shell:         req.Shell,
		Cols:          req.Cols,
		Rows:          req.Rows,
		Timeout:       req.Timeout,
	}

	ctx, cancel := context.WithTimeout(context.Background(), 1*time.Minute)
	defer cancel()

	result, err := h.podService.StartTerminal(ctx, serviceReq)
	if err != nil {
		h.logger.Error("启动终端失败", zap.Error(err))
		h.handleError(c, http.StatusInternalServerError, "启动终端失败", err)
		return
	}

	h.handleSuccess(c, result)
}

// StopTerminal 停止终端会话
func (h *PodHandler) StopTerminal(c *gin.Context) {
	namespace := c.Param("namespace")
	podName := c.Param("pod_name")
	sessionID := c.Query("session_id")

	if namespace == "" || podName == "" {
		h.handleError(c, http.StatusBadRequest, "namespace和pod_name参数是必需的", fmt.Errorf("missing required parameters"))
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	err := h.podService.StopTerminal(ctx, namespace, podName, sessionID)
	if err != nil {
		h.logger.Error("停止终端失败", zap.Error(err))
		h.handleError(c, http.StatusInternalServerError, "停止终端失败", err)
		return
	}

	h.handleSuccess(c, gin.H{"message": "终端会话已停止"})
}

// ResizeTerminal 调整终端大小
func (h *PodHandler) ResizeTerminal(c *gin.Context) {
	var req dto.PodTerminalResizeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.handleError(c, http.StatusBadRequest, "请求参数错误", err)
		return
	}

	// 转换为service层请求
	serviceReq := &service.ResizeTerminalRequest{
		Namespace: req.Namespace,
		PodName:   req.PodName,
		SessionID: req.SessionID,
		Cols:      req.Cols,
		Rows:      req.Rows,
	}

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	err := h.podService.ResizeTerminal(ctx, serviceReq)
	if err != nil {
		h.logger.Error("调整终端大小失败", zap.Error(err))
		h.handleError(c, http.StatusInternalServerError, "调整终端大小失败", err)
		return
	}

	h.handleSuccess(c, gin.H{"message": "终端大小已调整"})
}

// DumpJavaHeap 执行Java堆内存Dump
func (h *PodHandler) DumpJavaHeap(c *gin.Context) {
	var req dto.PodHeapDumpRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.handleError(c, http.StatusBadRequest, "请求参数错误", err)
		return
	}

	// 设置默认值
	if req.ContainerName == "" {
		req.ContainerName = "app"
	}
	if req.DumpPath == "" {
		req.DumpPath = fmt.Sprintf("/tmp/heap-dump-%s-%d.hprof", req.PodName, time.Now().Unix())
	}

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Minute)
	defer cancel()

	result, err := h.podService.DumpJavaHeap(ctx, req.Namespace, req.PodName, req.ContainerName, req.DumpPath)
	if err != nil {
		h.logger.Error("执行堆内存Dump失败", zap.Error(err))
		h.handleError(c, http.StatusInternalServerError, "执行堆内存Dump失败", err)
		return
	}

	h.handleSuccess(c, result)
}

// StartArthas 启动Arthas
func (h *PodHandler) StartArthas(c *gin.Context) {
	var req dto.PodArthasRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.handleError(c, http.StatusBadRequest, "请求参数错误", err)
		return
	}

	// 设置默认值
	if req.ContainerName == "" {
		req.ContainerName = "app"
	}
	if req.Port == 0 {
		req.Port = 3658
	}

	// 转换为service层请求
	serviceReq := &service.StartArthasRequest{
		Namespace:     req.Namespace,
		PodName:       req.PodName,
		ContainerName: req.ContainerName,
		HttpPort:      req.Port,
		TelnetPort:    req.Port,
	}

	ctx, cancel := context.WithTimeout(context.Background(), 2*time.Minute)
	defer cancel()

	result, err := h.podService.StartArthas(ctx, serviceReq)
	if err != nil {
		h.logger.Error("启动Arthas失败", zap.Error(err))
		h.handleError(c, http.StatusInternalServerError, "启动Arthas失败", err)
		return
	}

	h.handleSuccess(c, result)
}

// StopArthas 停止Arthas
func (h *PodHandler) StopArthas(c *gin.Context) {
	namespace := c.Param("namespace")
	podName := c.Param("pod_name")

	if namespace == "" || podName == "" {
		h.handleError(c, http.StatusBadRequest, "namespace和pod_name参数是必需的", fmt.Errorf("missing required parameters"))
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), 1*time.Minute)
	defer cancel()

	err := h.podService.StopArthas(ctx, namespace, podName)
	if err != nil {
		h.logger.Error("停止Arthas失败", zap.Error(err))
		h.handleError(c, http.StatusInternalServerError, "停止Arthas失败", err)
		return
	}

	h.handleSuccess(c, gin.H{"message": "Arthas已停止"})
}

// GetArthasStatus 获取Arthas状态
func (h *PodHandler) GetArthasStatus(c *gin.Context) {
	namespace := c.Param("namespace")
	podName := c.Param("pod_name")

	if namespace == "" || podName == "" {
		h.handleError(c, http.StatusBadRequest, "namespace和pod_name参数是必需的", fmt.Errorf("missing required parameters"))
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	status, err := h.podService.GetArthasStatus(ctx, namespace, podName)
	if err != nil {
		h.logger.Error("获取Arthas状态失败", zap.Error(err))
		h.handleError(c, http.StatusInternalServerError, "获取Arthas状态失败", err)
		return
	}

	h.handleSuccess(c, status)
}

// ListJavaProcesses 列出Java进程
func (h *PodHandler) ListJavaProcesses(c *gin.Context) {
	namespace := c.Param("namespace")
	podName := c.Param("pod_name")
	containerName := c.Query("container_name")

	if namespace == "" || podName == "" {
		h.handleError(c, http.StatusBadRequest, "namespace和pod_name参数是必需的", fmt.Errorf("missing required parameters"))
		return
	}

	if containerName == "" {
		containerName = "app"
	}

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	processes, err := h.podService.ListJavaProcesses(ctx, namespace, podName, containerName)
	if err != nil {
		h.logger.Error("获取Java进程列表失败", zap.Error(err))
		h.handleError(c, http.StatusInternalServerError, "获取Java进程列表失败", err)
		return
	}

	h.handleSuccess(c, processes)
}
