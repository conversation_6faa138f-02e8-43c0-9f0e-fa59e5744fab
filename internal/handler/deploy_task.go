package handler

import (
	"net/http"
	"strconv"

	"ci-gateway/internal/dto"
	"ci-gateway/internal/model"
	"ci-gateway/internal/service"

	"github.com/gin-contrib/sessions"
	"github.com/gin-gonic/gin"
)

// DeployHistoryHandler 部署历史处理器
type DeployTaskHandler struct {
	*Handler
	deployTaskService service.DeployTaskService
}

// NewDeployHistoryHandler 创建部署历史处理器实例
func NewDeployTaskHandler(handler *Handler, deployTaskService service.DeployTaskService) *DeployTaskHandler {
	return &DeployTaskHandler{
		Handler:           handler,
		deployTaskService: deployTaskService,
	}
}

// CreateDeployTask 创建部署任务
func (h *DeployTaskHandler) CreateDeployTask(c *gin.Context) {
	var req dto.CreateDeployTaskRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		h.handleError(c, http.StatusBadRequest, "参数绑定失败", err)
		return
	}

	session := sessions.Default(c)
	user := session.Get("user").(*model.User)

	deployTask, err := h.deployTaskService.CreateDeployTask(c.Request.Context(), &req, user.ID)
	if err != nil {
		h.handleError(c, http.StatusInternalServerError, "创建部署任务失败", err)
		return
	}

	h.handleSuccess(c, deployTask)
}

// GetDeployTasks 获取部署任务列表
func (h *DeployTaskHandler) GetDeployTasks(c *gin.Context) {
	appIDStr := c.Query("app_id")
	envIDStr := c.Query("env_id")

	appID, err := strconv.ParseInt(appIDStr, 10, 64)
	if err != nil {
		h.handleError(c, http.StatusBadRequest, "应用ID格式错误", err)
		return
	}

	envID, err := strconv.ParseInt(envIDStr, 10, 64)
	if err != nil {
		h.handleError(c, http.StatusBadRequest, "环境ID格式错误", err)
		return
	}

	deployTasks, err := h.deployTaskService.GetDeployTasksByAppAndEnvID(c.Request.Context(), appID, envID)
	if err != nil {
		h.handleError(c, http.StatusInternalServerError, "获取部署任务列表失败", err)
		return
	}

	h.handleSuccess(c, deployTasks)
}

// QueryDeployTasks 分页查询部署任务列表
func (h *DeployTaskHandler) QueryDeployTasks(c *gin.Context) {
	var req dto.QueryDeployTasksRequest

	// 绑定查询参数
	if err := c.ShouldBindQuery(&req); err != nil {
		h.handleError(c, http.StatusBadRequest, "参数绑定失败", err)
		return
	}

	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	// 调用服务方法查询
	response, err := h.deployTaskService.QueryDeployTasks(c.Request.Context(), &req)
	if err != nil {
		h.handleError(c, http.StatusInternalServerError, "查询部署任务列表失败", err)
		return
	}

	h.handleSuccess(c, response)
}

// GetDeployTaskDetail 获取部署任务详情
func (h *DeployTaskHandler) GetDeployTaskDetail(c *gin.Context) {
	taskIDStr := c.Param("taskId")

	taskID, err := strconv.ParseInt(taskIDStr, 10, 64)
	if err != nil {
		h.handleError(c, http.StatusBadRequest, "任务ID格式错误", err)
		return
	}

	deployTask, err := h.deployTaskService.GetDeployTaskByID(c.Request.Context(), taskID)
	if err != nil {
		h.handleError(c, http.StatusInternalServerError, "获取部署任务详情失败", err)
		return
	}

	if deployTask == nil {
		h.handleError(c, http.StatusNotFound, "部署任务不存在", nil)
		return
	}

	h.handleSuccess(c, deployTask)
}

// UpdateTaskDetailStatus 更新部署任务详情状态
func (h *DeployTaskHandler) UpdateTaskDetailStatus(c *gin.Context) {
	var req struct {
		TaskID  int64  `json:"task_id" binding:"required"`
		GroupID int64  `json:"group_id" binding:"required"`
		Status  int    `json:"status" binding:"required"`
		Message string `json:"message"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		h.handleError(c, http.StatusBadRequest, "参数绑定失败", err)
		return
	}

	// 调用服务更新TaskDetail状态
	err := h.deployTaskService.UpdateTaskDetailStatus(c.Request.Context(), req.TaskID, req.GroupID, req.Status, req.Message)
	if err != nil {
		h.handleError(c, http.StatusInternalServerError, "更新任务详情状态失败", err)
		return
	}

	h.handleSuccess(c, gin.H{"success": true})
}

// GetDeployConfig 获取部署配置
// 根据 task_id 和 group_id 获取 t_deploy_task_detail 数据
func (h *DeployTaskHandler) GetDeployConfig(c *gin.Context) {
	var req struct {
		TaskID  int64 `form:"task_id" binding:"required"`
		GroupID int64 `form:"group_id" binding:"required"`
	}

	if err := c.ShouldBindQuery(&req); err != nil {
		h.handleError(c, http.StatusBadRequest, "参数绑定失败", err)
		return
	}

	// 调用服务获取部署配置
	deployTaskDetail, err := h.deployTaskService.GetDeployTaskDetailByTaskIDAndGroupID(c.Request.Context(), req.TaskID, req.GroupID)
	if err != nil {
		h.handleError(c, http.StatusInternalServerError, "获取部署配置失败", err)
		return
	}

	if deployTaskDetail == nil {
		h.handleError(c, http.StatusNotFound, "未找到对应的部署配置", nil)
		return
	}

	h.handleSuccess(c, deployTaskDetail)
}
