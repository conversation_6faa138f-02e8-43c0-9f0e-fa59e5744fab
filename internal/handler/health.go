package handler

import (
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
)

type HealthHandler struct {
	*Handler
	startTime time.Time
}

func NewHealthHandler(handler *Handler) *HealthHandler {
	return &HealthHandler{
		Handler:   handler,
		startTime: time.Now(),
	}
}

// Health handles health check requests
func (h *HealthHandler) StartupProbe(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status":    "ok",
		"uptime":    time.Since(h.startTime).String(),
		"timestamp": time.Now().Unix(),
	})
}

// LivenessProbe handles kubernetes liveness probe
func (h *HealthHandler) LivenessProbe(c *gin.Context) {
	c.JSO<PERSON>(http.StatusOK, gin.H{
		"status": "ok",
	})
}

// ReadinessProbe handles kubernetes readiness probe
func (h *HealthHandler) ReadinessProbe(c *gin.Context) {
	c.<PERSON><PERSON>(http.StatusOK, gin.H{
		"status": "ok",
	})
}
