package handler

import (
	"net/http"
	"strconv"

	"ci-gateway/internal/dto"
	"ci-gateway/internal/service"
	pkghttp "ci-gateway/pkg/http"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// GroupHandler 部署分组处理器
type GroupHandler struct {
	groupService service.GroupService
	logger       *zap.Logger
}

// NewGroupHandler 创建部署分组处理器
func NewGroupHandler(groupService service.GroupService, logger *zap.Logger) *GroupHandler {
	return &GroupHandler{
		groupService: groupService,
		logger:       logger,
	}
}

// ListGroups 获取部署分组列表
func (h *GroupHandler) ListGroups(c *gin.Context) {
	var req dto.ListGroupsRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		pkghttp.HandleError(c, http.StatusBadRequest, "参数错误", gin.H{"details": err.Error()})
		return
	}

	result, err := h.groupService.ListGroupsDeployInfoByAppIDAndEnvID(c, req.AppID, req.EnvID, req.Code, req.Name)
	if err != nil {
		pkghttp.HandleError(c, http.StatusInternalServerError, "获取分组列表失败", gin.H{"details": err.Error()})
		return
	}

	pkghttp.HandleSuccess(c, result)
}

// GetGroupByID 获取部署分组详情
func (h *GroupHandler) GetGroupByID(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		pkghttp.HandleError(c, http.StatusBadRequest, "ID参数错误", gin.H{"details": err.Error()})
		return
	}

	group, err := h.groupService.GetGroupByID(id)
	if err != nil {
		pkghttp.HandleError(c, http.StatusInternalServerError, "获取分组详情失败", gin.H{"details": err.Error()})
		return
	}

	if group == nil {
		pkghttp.HandleError(c, http.StatusNotFound, "分组不存在", nil)
		return
	}

	pkghttp.HandleSuccess(c, group)
}

// GetGroupsByAppAndEnvID 获取应用环境的所有分组
func (h *GroupHandler) GetGroupsByAppAndEnvID(c *gin.Context) {
	appIDStr := c.Query("app_id")
	envIDStr := c.Query("env_id")

	if appIDStr == "" || envIDStr == "" {
		pkghttp.HandleError(c, http.StatusBadRequest, "请提供应用ID和环境ID", nil)
		return
	}

	appID, err := strconv.ParseInt(appIDStr, 10, 64)
	if err != nil {
		pkghttp.HandleError(c, http.StatusBadRequest, "应用ID参数错误", gin.H{"details": err.Error()})
		return
	}

	envID, err := strconv.ParseInt(envIDStr, 10, 64)
	if err != nil {
		pkghttp.HandleError(c, http.StatusBadRequest, "环境ID参数错误", gin.H{"details": err.Error()})
		return
	}

	groups, err := h.groupService.GetGroupsByAppIDAndEnvID(appID, envID)
	if err != nil {
		pkghttp.HandleError(c, http.StatusInternalServerError, "获取分组列表失败", gin.H{"details": err.Error()})
		return
	}

	pkghttp.HandleSuccess(c, groups)
}

// CreateGroup 创建部署分组
func (h *GroupHandler) CreateGroup(c *gin.Context) {
	var req dto.CreateGroupRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		pkghttp.HandleError(c, http.StatusBadRequest, "参数错误", gin.H{"details": err.Error()})
		return
	}

	group, err := h.groupService.CreateGroup(c, &req)
	if err != nil {
		pkghttp.HandleError(c, http.StatusInternalServerError, "创建分组失败", gin.H{"details": err.Error()})
		return
	}

	pkghttp.HandleSuccess(c, group)
}

// DeleteGroup 删除部署分组
func (h *GroupHandler) DeleteGroup(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		pkghttp.HandleError(c, http.StatusBadRequest, "ID参数错误", gin.H{"details": err.Error()})
		return
	}

	if err := h.groupService.DeleteGroup(c, id); err != nil {
		pkghttp.HandleError(c, http.StatusInternalServerError, "删除分组失败", gin.H{"details": err.Error()})
		return
	}

	pkghttp.HandleSuccess(c, gin.H{"message": "删除成功"})
}

// SetDefaultGroup 设置默认分组
func (h *GroupHandler) SetDefaultGroup(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		pkghttp.HandleError(c, http.StatusBadRequest, "ID参数错误", gin.H{"details": err.Error()})
		return
	}

	if err := h.groupService.SetDefaultGroup(c, id); err != nil {
		pkghttp.HandleError(c, http.StatusInternalServerError, "设置默认分组失败", gin.H{"details": err.Error()})
		return
	}

	pkghttp.HandleSuccess(c, gin.H{"message": "设置默认分组成功"})
}
