package handler

import (
	"ci-gateway/pkg/http"

	"github.com/gin-gonic/gin"
	"github.com/spf13/viper"
	"go.uber.org/zap"
)

type Handler struct {
	logger *zap.Logger
	config *viper.Viper
}

func NewHandler(logger *zap.Logger, config *viper.Viper) *Handler {
	return &Handler{
		logger: logger,
		config: config,
	}
}

func (h *Handler) handleError(c *gin.Context, code int, message string, err error) {
	details := gin.H{}
	if err != nil {
		details["details"] = err.Error()
	}
	http.HandleError(c, code, message, details)
}

func (h *Handler) handleSuccess(c *gin.Context, data interface{}) {
	http.HandleSuccess(c, data)
}

func (h *Handler) getConfig() *viper.Viper {
	return h.config
}
