package handler

import (
	"net/http"

	"ci-gateway/internal/model"
	"ci-gateway/internal/service"

	"github.com/gin-contrib/sessions"
	"github.com/gin-gonic/gin"
)

// ContainerHandler 容器配置处理器
type ContainerHandler struct {
	*Handler
	containerService service.ContainerService
}

// NewContainerHandler 创建容器配置处理器
func NewContainerHandler(
	handler *Handler,
	containerService service.ContainerService,
) *ContainerHandler {
	return &ContainerHandler{
		Handler:          handler,
		containerService: containerService,
	}
}

// GetContainerConfigRequest 获取容器配置请求
type GetContainerConfigRequest struct {
	AppID int64 `form:"app_id" binding:"required"`
	EnvID int64 `form:"env_id" binding:"required"`
}

// CreateContainerConfigRequest 创建容器配置请求
type CreateContainerConfigRequest struct {
	AppID         int64                `json:"app_id" binding:"required"`
	EnvID         int64                `json:"env_id" binding:"required"`
	GroupID       int64                `json:"group_id"`
	BaseImage     string               `json:"base_image" binding:"required"`
	Replicas      int                  `json:"replicas" binding:"required,min=1,max=10"`
	Namespace     string               `json:"namespace" binding:"required"`
	Ports         []model.PortConfig   `json:"ports" binding:"required,dive"`
	Resources     model.ResourceConfig `json:"resources" binding:"required"`
	Environment   map[string]string    `json:"environment"`
	Strategy      string               `json:"strategy" binding:"required,oneof=RollingUpdate Recreate"`
	RolloutConfig model.RolloutConfig  `json:"rollout_config" binding:"required"`
	Status        int8                 `json:"status"`
}

// UpdateContainerConfigRequest 更新容器配置请求
type UpdateContainerConfigRequest struct {
	AppID         int64                `json:"app_id" binding:"required"`
	EnvID         int64                `json:"env_id" binding:"required"`
	GroupID       int64                `json:"group_id"`
	BaseImage     string               `json:"base_image" binding:"required"`
	Replicas      int                  `json:"replicas" binding:"required,min=1,max=10"`
	Namespace     string               `json:"namespace" binding:"required"`
	Ports         []model.PortConfig   `json:"ports" binding:"required,dive"`
	Resources     model.ResourceConfig `json:"resources" binding:"required"`
	Environment   map[string]string    `json:"environment"`
	Strategy      string               `json:"strategy" binding:"required,oneof=RollingUpdate Recreate"`
	RolloutConfig model.RolloutConfig  `json:"rollout_config" binding:"required"`
	Status        int8                 `json:"status"`
}

// DeleteContainerConfigRequest 删除容器配置请求
type DeleteContainerConfigRequest struct {
	AppID int64 `form:"app_id" binding:"required"`
	EnvID int64 `form:"env_id" binding:"required"`
}

// ValidateContainerConfigRequest 验证容器配置请求
type ValidateContainerConfigRequest struct {
	AppID         int64                `json:"app_id" binding:"required"`
	EnvID         int64                `json:"env_id" binding:"required"`
	GroupID       int64                `json:"group_id"`
	BaseImage     string               `json:"base_image" binding:"required"`
	Replicas      int                  `json:"replicas" binding:"required,min=1,max=10"`
	Namespace     string               `json:"namespace" binding:"required"`
	Ports         []model.PortConfig   `json:"ports" binding:"required,dive"`
	Resources     model.ResourceConfig `json:"resources" binding:"required"`
	Environment   map[string]string    `json:"environment"`
	Strategy      string               `json:"strategy" binding:"required,oneof=RollingUpdate Recreate"`
	RolloutConfig model.RolloutConfig  `json:"rollout_config" binding:"required"`
}

// ValidateContainerResponse 验证容器配置响应
type ValidateContainerResponse struct {
	Valid    bool     `json:"valid"`
	Errors   []string `json:"errors"`
	Warnings []string `json:"warnings"`
}

// PreviewYAMLResponse 预览YAML响应
type PreviewYAMLResponse struct {
	DeploymentYAML string `json:"deployment_yaml"`
	ServiceYAML    string `json:"service_yaml"`
}

// GetContainerConfig 根据应用ID和环境ID获取容器配置
func (h *ContainerHandler) GetContainerConfig(c *gin.Context) {
	var req GetContainerConfigRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		h.handleError(c, http.StatusBadRequest, "参数错误", err)
		return
	}

	container, err := h.containerService.GetContainerByAppAndEnv(c, req.AppID, req.EnvID)
	if err != nil {
		h.handleError(c, http.StatusInternalServerError, "获取容器配置失败", err)
		return
	}

	if container == nil {
		h.handleError(c, http.StatusNotFound, "容器配置不存在", nil)
		return
	}

	h.handleSuccess(c, container)
}

// GetContainerConfigList 根据应用ID和环境ID获取容器配置列表
func (h *ContainerHandler) GetContainerConfigList(c *gin.Context) {
	var req GetContainerConfigRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		h.handleError(c, http.StatusBadRequest, "参数错误", err)
		return
	}

	containers, err := h.containerService.GetContainersByAppAndEnv(c, req.AppID, req.EnvID)
	if err != nil {
		h.handleError(c, http.StatusInternalServerError, "获取容器配置列表失败", err)
		return
	}

	h.handleSuccess(c, containers)
}

// CreateContainerConfig 创建容器配置
func (h *ContainerHandler) CreateContainerConfig(c *gin.Context) {
	var req CreateContainerConfigRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.handleError(c, http.StatusBadRequest, "参数错误", err)
		return
	}

	// 获取用户信息
	session := sessions.Default(c)
	var userID int64 = 0
	if user := session.Get("user"); user != nil {
		userID = user.(*model.User).ID
	}

	// 转换为模型
	container := &model.Container{
		AppID:         req.AppID,
		EnvID:         req.EnvID,
		GroupID:       req.GroupID,
		BaseImage:     req.BaseImage,
		Replicas:      req.Replicas,
		Namespace:     req.Namespace,
		Ports:         model.ContainerPortsJSON(req.Ports),
		Resources:     model.ContainerResourcesJSON(req.Resources),
		Environment:   model.ContainerEnvironmentJSON(req.Environment),
		Strategy:      req.Strategy,
		RolloutConfig: model.ContainerRolloutJSON(req.RolloutConfig),
		Status:        1, // 默认启用
		CreateBy:      userID,
		UpdateBy:      userID,
	}

	createdContainer, err := h.containerService.CreateContainer(c, container)
	if err != nil {
		h.handleError(c, http.StatusInternalServerError, "创建容器配置失败", err)
		return
	}

	h.handleSuccess(c, createdContainer)
}

// UpdateContainerConfig 更新容器配置
func (h *ContainerHandler) UpdateContainerConfig(c *gin.Context) {
	var req UpdateContainerConfigRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.handleError(c, http.StatusBadRequest, "参数错误", err)
		return
	}

	// 获取用户信息
	session := sessions.Default(c)
	var userID int64 = 0
	if user := session.Get("user"); user != nil {
		userID = user.(*model.User).ID
	}

	// 转换为模型
	container := &model.Container{
		AppID:         req.AppID,
		EnvID:         req.EnvID,
		GroupID:       req.GroupID,
		BaseImage:     req.BaseImage,
		Replicas:      req.Replicas,
		Namespace:     req.Namespace,
		Ports:         model.ContainerPortsJSON(req.Ports),
		Resources:     model.ContainerResourcesJSON(req.Resources),
		Environment:   model.ContainerEnvironmentJSON(req.Environment),
		Strategy:      req.Strategy,
		RolloutConfig: model.ContainerRolloutJSON(req.RolloutConfig),
		Status:        1, // 保持启用
		UpdateBy:      userID,
	}

	updatedContainer, err := h.containerService.UpdateContainer(c, container)
	if err != nil {
		h.handleError(c, http.StatusInternalServerError, "更新容器配置失败", err)
		return
	}

	h.handleSuccess(c, updatedContainer)
}

// DeleteContainerConfig 删除容器配置
func (h *ContainerHandler) DeleteContainerConfig(c *gin.Context) {
	var req DeleteContainerConfigRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		h.handleError(c, http.StatusBadRequest, "参数错误", err)
		return
	}

	err := h.containerService.DeleteContainer(c, req.AppID, req.EnvID)
	if err != nil {
		h.handleError(c, http.StatusInternalServerError, "删除容器配置失败", err)
		return
	}

	h.handleSuccess(c, gin.H{"message": "删除成功"})
}

// ValidateContainerConfig 验证容器配置
func (h *ContainerHandler) ValidateContainerConfig(c *gin.Context) {
	var req ValidateContainerConfigRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.handleError(c, http.StatusBadRequest, "参数错误", err)
		return
	}

	// 转换为模型
	container := &model.Container{
		AppID:         req.AppID,
		EnvID:         req.EnvID,
		GroupID:       req.GroupID,
		BaseImage:     req.BaseImage,
		Replicas:      req.Replicas,
		Namespace:     req.Namespace,
		Ports:         model.ContainerPortsJSON(req.Ports),
		Resources:     model.ContainerResourcesJSON(req.Resources),
		Environment:   model.ContainerEnvironmentJSON(req.Environment),
		Strategy:      req.Strategy,
		RolloutConfig: model.ContainerRolloutJSON(req.RolloutConfig),
	}

	valid, errors, warnings, err := h.containerService.ValidateContainer(c, container)
	if err != nil {
		h.handleError(c, http.StatusInternalServerError, "验证容器配置失败", err)
		return
	}

	response := ValidateContainerResponse{
		Valid:    valid,
		Errors:   errors,
		Warnings: warnings,
	}

	h.handleSuccess(c, response)
}

// PreviewContainerYAML 预览容器配置YAML
func (h *ContainerHandler) PreviewContainerYAML(c *gin.Context) {
	var req ValidateContainerConfigRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.handleError(c, http.StatusBadRequest, "参数错误", err)
		return
	}

	// 转换为模型
	container := &model.Container{
		AppID:         req.AppID,
		EnvID:         req.EnvID,
		GroupID:       req.GroupID,
		BaseImage:     req.BaseImage,
		Replicas:      req.Replicas,
		Namespace:     req.Namespace,
		Ports:         model.ContainerPortsJSON(req.Ports),
		Resources:     model.ContainerResourcesJSON(req.Resources),
		Environment:   model.ContainerEnvironmentJSON(req.Environment),
		Strategy:      req.Strategy,
		RolloutConfig: model.ContainerRolloutJSON(req.RolloutConfig),
	}

	yamlMap, err := h.containerService.PreviewYAML(c, container)
	if err != nil {
		h.handleError(c, http.StatusInternalServerError, "预览YAML失败", err)
		return
	}

	response := PreviewYAMLResponse{
		DeploymentYAML: yamlMap["deployment_yaml"],
		ServiceYAML:    yamlMap["service_yaml"],
	}

	h.handleSuccess(c, response)
}
