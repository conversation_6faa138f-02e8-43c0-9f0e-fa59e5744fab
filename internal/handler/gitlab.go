package handler

import (
	"net/http"

	"ci-gateway/internal/dto"
	gitlabService "ci-gateway/internal/service/gitlab"

	"github.com/gin-gonic/gin"
)

// GitLabHandler GitLab处理器
type GitLabHandler struct {
	*Handler
	gitlabService gitlabService.GitLabService
}

// NewGitLabHandler 创建GitLab处理器
func NewGitLabHandler(handler *Handler, gitlabService gitlabService.GitLabService) *GitLabHandler {
	return &GitLabHandler{
		Handler:       handler,
		gitlabService: gitlabService,
	}
}

// GetSemvers 获取版本列表
func (h *GitLabHandler) GetSemvers(c *gin.Context) {
	var req dto.GetSemversRequest

	if err := c.ShouldBindQuery(&req); err != nil {
		h.handleError(c, http.StatusBadRequest, "请求参数错误", err)
		return
	}

	if req.IsBranch == 1 {
		branches, err := h.gitlabService.GetBranches(req.ProjectID, req.Search, req.Page, req.PerPage)
		if err != nil {
			h.handleError(c, http.StatusInternalServerError, "获取分支失败", err)
			return
		}
		h.handleSuccess(c, branches)
	} else {
		tags, err := h.gitlabService.GetTags(req.ProjectID, req.Search, req.Page, req.PerPage)
		if err != nil {
			h.handleError(c, http.StatusInternalServerError, "获取标签失败", err)
			return
		}
		h.handleSuccess(c, tags)
	}

}
