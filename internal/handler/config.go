package handler

import (
	"net/http"

	"ci-gateway/internal/dto"
	"ci-gateway/internal/model"
	"ci-gateway/internal/service"

	"github.com/gin-contrib/sessions"
	"github.com/gin-gonic/gin"
)

type ConfigHandler struct {
	*Handler
	configFileService service.ConfigFileService
}

func NewConfigHandler(handler *Handler, configFileService service.ConfigFileService) *ConfigHandler {
	return &ConfigHandler{
		Handler:           handler,
		configFileService: configFileService,
	}
}

// GetConfigFilesByAppAndEnvID 根据应用ID和环境ID获取配置文件列表
func (h *ConfigHandler) GetConfigFilesByAppAndEnvID(c *gin.Context) {
	var req struct {
		AppID int64 `json:"appId" binding:"required"`
		EnvID int64 `json:"envId" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		h.handleError(c, http.StatusBadRequest, "参数绑定失败", err)
		return
	}

	configFiles, err := h.configFileService.GetConfigFilesByAppAndEnvID(req.AppID, req.EnvID)
	if err != nil {
		h.handleError(c, http.StatusInternalServerError, "获取配置文件列表失败", err)
		return
	}

	h.handleSuccess(c, configFiles)
}

func (h *ConfigHandler) GetConfigContentByFileID(c *gin.Context) {
	var req struct {
		FileID int64 `json:"fileId" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		h.handleError(c, http.StatusBadRequest, "参数绑定失败", err)
		return
	}

	configContent, err := h.configFileService.GetConfigContentByFileID(req.FileID)
	if err != nil {
		h.handleError(c, http.StatusInternalServerError, "获取配置文件内容失败", err)
		return
	}

	h.handleSuccess(c, configContent)
}

func (h *ConfigHandler) CreateConfigFile(c *gin.Context) {
	var req dto.CreateConfigFileRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.handleError(c, http.StatusBadRequest, "参数绑定失败", err)
		return
	}
	session := sessions.Default(c)
	user := session.Get("user").(*model.User)
	configFile, err := h.configFileService.CreateConfigFile(req, user.ID)
	if err != nil {
		h.handleError(c, http.StatusInternalServerError, "创建配置文件失败", err)
		return
	}

	h.handleSuccess(c, configFile)
}
