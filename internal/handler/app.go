package handler

import (
	"net/http"

	"ci-gateway/internal/dto"
	"ci-gateway/internal/service"
	"ci-gateway/internal/service/cmdb"
	pkghttp "ci-gateway/pkg/http"

	"github.com/gin-gonic/gin"
)

type AppHandler struct {
	*Handler
	applicationService cmdb.ApplicationService
	appSettingsService service.AppSettingsService
	groupService       service.GroupService
	configFileService  service.ConfigFileService
}

func NewAppHandler(applicationService cmdb.ApplicationService, appSettingsService service.AppSettingsService, groupService service.GroupService, environmentVariableService service.EnvironmentVariableService, configFileService service.ConfigFileService, handler *Handler) *AppHandler {
	return &AppHandler{
		Handler:            handler,
		applicationService: applicationService,
		appSettingsService: appSettingsService,
		groupService:       groupService,
		configFileService:  configFileService,
	}
}

// GetAppInfoByNodeID 从指定服务树节点ID获取应用信息
func (h *AppHandler) GetAppInfoByNodeID(c *gin.Context) {
	var req dto.GetAppInfoByNodeIDRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		pkghttp.HandleError(c, http.StatusBadRequest, "参数错误", nil)
		return
	}

	app, err := h.applicationService.GetApplicationByEnvAndSrvTree(c.Request.Context(), req.EnvID, req.NodeID)
	if err != nil {
		pkghttp.HandleError(c, http.StatusInternalServerError, "获取应用信息失败", gin.H{"details": err.Error()})
		return
	}

	appSettings, _ := h.appSettingsService.GetAppSettingsByAppID(app.Application.ID)
	app.AppSettings = appSettings

	configFiles, _ := h.configFileService.GetConfigFilesByAppAndEnvID(app.Application.ID, req.EnvID)
	app.ConfigFiles = configFiles

	pkghttp.HandleSuccess(c, app)
}
