package handler

import (
	"net/http"
	"strconv"

	"ci-gateway/internal/dto"
	"ci-gateway/internal/model"
	"ci-gateway/internal/service"

	"github.com/gin-contrib/sessions"
	"github.com/gin-gonic/gin"
)

// BuildConfigHandler 构建配置处理器
type BuildConfigHandler struct {
	*Handler
	buildConfigService service.BuildConfigService
}

// NewBuildConfigHandler 创建构建配置处理器
func NewBuildConfigHandler(
	handler *Handler,
	buildConfigService service.BuildConfigService,
) *BuildConfigHandler {
	return &BuildConfigHandler{
		Handler:            handler,
		buildConfigService: buildConfigService,
	}
}

// GetBuildTemplates 获取构建模板列表
// @Summary 获取构建模板列表
// @Description 获取所有可用的构建模板或指定语言的模板
// @Tags BuildConfig
// @Accept json
// @Produce json
// @Param language query string false "编程语言"
// @Success 200 {object} Response{data=[]dto.BuildTemplateResponse}
// @Router /api/build/templates [get]
func (h *BuildConfigHandler) GetBuildTemplates(c *gin.Context) {
	language := c.Query("language")

	templates, err := h.buildConfigService.GetBuildTemplates(c.Request.Context(), language)
	if err != nil {
		h.handleError(c, http.StatusInternalServerError, "获取构建模板失败", err)
		return
	}

	h.handleSuccess(c, templates)
}

// GetBuildTemplateByID 根据ID获取构建模板
// @Summary 根据ID获取构建模板
// @Description 获取指定ID的构建模板详情
// @Tags BuildConfig
// @Accept json
// @Produce json
// @Param id path int true "模板ID"
// @Success 200 {object} Response{data=dto.BuildTemplateResponse}
// @Router /api/build/templates/{id} [get]
func (h *BuildConfigHandler) GetBuildTemplateByID(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		h.handleError(c, http.StatusBadRequest, "无效的模板ID", err)
		return
	}

	template, err := h.buildConfigService.GetBuildTemplateByID(c.Request.Context(), id)
	if err != nil {
		h.handleError(c, http.StatusInternalServerError, "获取构建模板失败", err)
		return
	}

	h.handleSuccess(c, template)
}

// GetDefaultTemplate 获取指定语言的默认模板
// @Summary 获取默认构建模板
// @Description 获取指定语言的默认构建模板
// @Tags BuildConfig
// @Accept json
// @Produce json
// @Param language query string true "编程语言"
// @Success 200 {object} Response{data=dto.BuildTemplateResponse}
// @Router /api/build/templates/default [get]
func (h *BuildConfigHandler) GetDefaultTemplate(c *gin.Context) {
	language := c.Query("language")
	if language == "" {
		h.handleError(c, http.StatusBadRequest, "语言参数不能为空", nil)
		return
	}

	template, err := h.buildConfigService.GetDefaultTemplate(c.Request.Context(), language)
	if err != nil {
		h.handleError(c, http.StatusInternalServerError, "获取默认模板失败", err)
		return
	}

	h.handleSuccess(c, template)
}

// GetProjectBuildConfig 获取项目构建配置
// @Summary 获取项目构建配置
// @Description 根据应用ID和环境ID获取项目的构建配置
// @Tags BuildConfig
// @Accept json
// @Produce json
// @Param app_id query int true "应用ID"
// @Param env_id query int true "环境ID"
// @Success 200 {object} Response{data=dto.ProjectBuildConfigResponse}
// @Router /api/build/config [get]
func (h *BuildConfigHandler) GetProjectBuildConfig(c *gin.Context) {
	appIDStr := c.Query("app_id")
	envIDStr := c.Query("env_id")

	if appIDStr == "" || envIDStr == "" {
		h.handleError(c, http.StatusBadRequest, "app_id和env_id参数不能为空", nil)
		return
	}

	appID, err := strconv.ParseInt(appIDStr, 10, 64)
	if err != nil {
		h.handleError(c, http.StatusBadRequest, "无效的应用ID", err)
		return
	}

	envID, err := strconv.ParseInt(envIDStr, 10, 64)
	if err != nil {
		h.handleError(c, http.StatusBadRequest, "无效的环境ID", err)
		return
	}

	config, err := h.buildConfigService.GetProjectBuildConfig(c.Request.Context(), appID, envID)
	if err != nil {
		h.handleError(c, http.StatusInternalServerError, "获取项目构建配置失败", err)
		return
	}

	h.handleSuccess(c, config)
}

// CreateProjectBuildConfig 创建项目构建配置
// @Summary 创建项目构建配置
// @Description 为指定的应用和环境创建构建配置
// @Tags BuildConfig
// @Accept json
// @Produce json
// @Param request body dto.CreateProjectBuildConfigRequest true "创建请求参数"
// @Success 200 {object} Response{data=dto.ProjectBuildConfigResponse}
// @Router /api/build/config/create [post]
func (h *BuildConfigHandler) CreateProjectBuildConfig(c *gin.Context) {
	var req dto.CreateProjectBuildConfigRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.handleError(c, http.StatusBadRequest, "参数绑定失败", err)
		return
	}

	// 获取当前用户信息
	session := sessions.Default(c)
	user := session.Get("user").(*model.User)

	config, err := h.buildConfigService.CreateProjectBuildConfig(c.Request.Context(), &req, user.ID)
	if err != nil {
		h.handleError(c, http.StatusInternalServerError, "创建项目构建配置失败", err)
		return
	}

	h.handleSuccess(c, config)
}

// UpdateProjectBuildConfig 更新项目构建配置
// @Summary 更新项目构建配置
// @Description 更新指定应用和环境的构建配置
// @Tags BuildConfig
// @Accept json
// @Produce json
// @Param request body dto.UpdateProjectBuildConfigWithAppEnvRequest true "更新请求参数"
// @Success 200 {object} Response{data=dto.ProjectBuildConfigResponse}
// @Router /api/build/config/update [post]
func (h *BuildConfigHandler) UpdateProjectBuildConfig(c *gin.Context) {
	var req dto.UpdateProjectBuildConfigWithAppEnvRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.handleError(c, http.StatusBadRequest, "参数绑定失败", err)
		return
	}

	// 获取当前用户信息
	session := sessions.Default(c)
	user := session.Get("user").(*model.User)

	config, err := h.buildConfigService.UpdateProjectBuildConfig(c.Request.Context(), req.AppID, req.EnvID, &req.UpdateProjectBuildConfigRequest, user.ID)
	if err != nil {
		h.handleError(c, http.StatusInternalServerError, "更新项目构建配置失败", err)
		return
	}

	h.handleSuccess(c, config)
}

// DeleteProjectBuildConfig 删除项目构建配置
// @Summary 删除项目构建配置
// @Description 删除指定应用和环境的构建配置
// @Tags BuildConfig
// @Accept json
// @Produce json
// @Param request body dto.ProjectBuildConfigRequest true "删除请求参数"
// @Success 200 {object} Response{data=bool}
// @Router /api/build/config/delete [post]
func (h *BuildConfigHandler) DeleteProjectBuildConfig(c *gin.Context) {
	var req dto.ProjectBuildConfigRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.handleError(c, http.StatusBadRequest, "参数绑定失败", err)
		return
	}

	err := h.buildConfigService.DeleteProjectBuildConfig(c.Request.Context(), req.AppID, req.EnvID)
	if err != nil {
		h.handleError(c, http.StatusInternalServerError, "删除项目构建配置失败", err)
		return
	}

	h.handleSuccess(c, true)
}

// PreviewBuildConfig 预览构建配置
// @Summary 预览构建配置
// @Description 根据配置参数预览生成的构建命令、Dockerfile等
// @Tags BuildConfig
// @Accept json
// @Produce json
// @Param request body dto.BuildConfigPreviewRequest true "预览请求参数"
// @Success 200 {object} Response{data=dto.BuildConfigPreviewResponse}
// @Router /api/build/config/preview [post]
func (h *BuildConfigHandler) PreviewBuildConfig(c *gin.Context) {
	var req dto.BuildConfigPreviewRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.handleError(c, http.StatusBadRequest, "参数绑定失败", err)
		return
	}

	preview, err := h.buildConfigService.PreviewBuildConfig(c.Request.Context(), &req)
	if err != nil {
		h.handleError(c, http.StatusInternalServerError, "生成构建配置预览失败", err)
		return
	}

	h.handleSuccess(c, preview)
}
