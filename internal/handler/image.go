package handler

import (
	"ci-gateway/internal/service"
	pkghttp "ci-gateway/pkg/http"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

type ImageHandler struct {
	*Handler
	imageService service.ImageService
}

func NewImageHandler(handler *Handler, imageService service.ImageService) *ImageHandler {
	return &ImageHandler{
		Handler:      handler,
		imageService: imageService,
	}
}

func (h *ImageHandler) ListImages(c *gin.Context) {

	name := c.<PERSON><PERSON>ult<PERSON>("name", "")

	imageType := c.DefaultQuery("type", "")

	statusInt, _ := strconv.Atoi(c.<PERSON>ult<PERSON>("status", "0"))
	status := int8(statusInt)

	current, _ := strconv.Atoi(c.<PERSON>("current", "0"))
	pageSize, _ := strconv.Atoi(c.<PERSON>("pageSize", "20"))
	offset := (current - 1) * pageSize

	images, total, err := h.imageService.GetImageList(c.Request.Context(), offset, pageSize, name, status, imageType)

	if err != nil {
		pkghttp.HandleError(c, 500, "获取镜像列表失败", gin.H{"details": err.Error()})
		return
	}

	pkghttp.HandleSuccess(c, gin.H{
		"tasks": images,
		"total": total,
	})
}

// func (h *ImageHandler) UploadImage(c *gin.Context) {
// 	if err := c.Request.ParseMultipartForm(200 << 20); err != nil {
// 		resp.HandleError(c, 400, "解析表单数据失败", gin.H{"details": err.Error()})
// 		return
// 	}

// 	file, header, err := c.Request.FormFile("file")
// 	if err != nil {
// 		resp.HandleError(c, 400, "获取文件失败", gin.H{"details": err.Error()})
// 		return
// 	}
// 	defer file.Close()

// 	// 创建一个缓冲区来存储文件内容
// 	buffer := &bytes.Buffer{}
// 	_, err = io.Copy(buffer, file)
// 	if err != nil {
// 		resp.HandleError(c, 500, "读取文件内容失败", gin.H{"details": err.Error()})
// 		return
// 	}

// 	uuid := uuid.New().String()
// 	// 上传文件到MinIO
// 	fileExt := filepath.Ext(header.Filename)
// 	objectName := "redis-image-" + uuid + fileExt
// 	_, err = h.imageService.UploadToMinIO(c.Request.Context(), buffer, objectName, header.Size)
// 	if err != nil {
// 		resp.HandleError(c, 500, "上传文件到MinIO失败", gin.H{"details": err.Error()})
// 		return
// 	}

// 	resp.HandleSuccess(c, gin.H{
// 		"message": "文件已成功上传到MinIO",
// 		"object":  objectName,
// 		"size":    header.Size,
// 	})
// }

// func (h *ImageHandler) Create(c *gin.Context) {
// 	var createParam dto.CreateParam
// 	if err := c.ShouldBindJSON(&createParam); err != nil {
// 		resp.HandleError(c, http.StatusBadRequest, "无效的请求数据", gin.H{"details": err.Error()})
// 		return
// 	}

// 	id, err := h.imageService.Create(c.Request.Context(), createParam)
// 	if err != nil {
// 		resp.HandleError(c, http.StatusInternalServerError, "创建 Redis 镜像失败", gin.H{"details": err.Error()})
// 		return
// 	}

// 	resp.HandleSuccess(c, gin.H{"id": id})
// }

// func (h *ImageHandler) Test(c *gin.Context) {
// 	var param struct {
// 		ID int64 `json:"id"`
// 	}
// 	if err := c.ShouldBindJSON(&param); err != nil {
// 		resp.HandleError(c, http.StatusBadRequest, "无效的请求数据", gin.H{"details": err.Error()})
// 		return
// 	}

// 	err := h.imageService.Test(c.Request.Context(), param.ID)
// 	if err != nil {
// 		resp.HandleError(c, 500, "测试Redis镜像失败", gin.H{"details": err.Error()})
// 		return
// 	}

// 	resp.HandleSuccess(c, gin.H{"message": "Redis镜像测试已开始"})
// }

// http://localhost:1024/api/image/del/11

func (h *ImageHandler) DelImages(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		pkghttp.HandleError(c, http.StatusBadRequest, "无效的ID", gin.H{"details": err.Error()})
		return
	}

	err = h.imageService.Delete(c.Request.Context(), id)
	if err != nil {
		pkghttp.HandleError(c, http.StatusInternalServerError, "删除镜像失败", gin.H{"details": err.Error()})
		return
	}

	pkghttp.HandleSuccess(c, gin.H{"message": "镜像删除成功"})
}

// func (h *ImageHandler) ChangeStatus(c *gin.Context) {
// 	var param struct {
// 		ID     int64             `json:"id"`
// 		Status model.ImageStatus `json:"status"`
// 	}
// 	if err := c.ShouldBindJSON(&param); err != nil {
// 		resp.HandleError(c, 400, "无效的请求数据", gin.H{"details": err.Error()})
// 		return
// 	}
// 	err := h.imageService.ChangeStatus(c, param.ID, param.Status)
// 	if err != nil {
// 		resp.HandleError(c, 500, "修改Redis镜像状态失败", gin.H{"details": err.Error()})
// 		return
// 	}
// }

// func (h *ImageHandler) UpdateImages(c *gin.Context) {
// 	var param dto.UpdateImageParam
// 	if err := c.ShouldBindJSON(&param); err != nil {
// 		resp.HandleError(c, 400, "无效的请求数据", gin.H{"details": err.Error()})
// 		return
// 	}

// 	err := h.imageService.Update(c, param)
// 	if err != nil {
// 		resp.HandleError(c, 500, "更新Redis镜像失败", gin.H{"details": err.Error()})
// 		return
// 	}

// 	resp.HandleSuccess(c, gin.H{"message": "Redis镜像更新成功"})
// }

// func (h *ImageHandler) ListVersions(c *gin.Context) {
// 	versions, err := h.imageService.ListVersions(c.Request.Context())
// 	if err != nil {
// 		resp.HandleError(c, http.StatusInternalServerError, "获取版本列表失败", gin.H{"details": err.Error()})
// 		return
// 	}

// 	resp.HandleSuccess(c, versions)
// }

// func (h *ImageHandler) ListVersionsAndCPUs(c *gin.Context) {
// 	versionsAndCPUs, err := h.imageService.ListVersionsAndCPUs(c.Request.Context())
// 	if err != nil {
// 		resp.HandleError(c, http.StatusInternalServerError, "获取版本和CPU列表失败", gin.H{"details": err.Error()})
// 		return
// 	}

// 	resp.HandleSuccess(c, versionsAndCPUs)
// }
