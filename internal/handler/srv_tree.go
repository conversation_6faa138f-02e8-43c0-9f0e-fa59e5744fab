package handler

import (
	"net/http"

	"ci-gateway/internal/model"
	"ci-gateway/internal/service/cmdb"
	pkghttp "ci-gateway/pkg/http"

	"github.com/gin-contrib/sessions"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type SrvTreeHandler struct {
	srvTreeService cmdb.SrvTreeService
	logger         *zap.Logger
}

func NewSrvTreeHandler(srvTreeService cmdb.SrvTreeService, applicationService cmdb.ApplicationService, logger *zap.Logger) *SrvTreeHandler {
	return &SrvTreeHandler{
		srvTreeService: srvTreeService,
		logger:         logger,
	}
}

func (h *SrvTreeHandler) GetSrvTree(c *gin.Context) {
	session := sessions.Default(c)
	user := session.Get("user").(*model.User)
	trees, err := h.srvTreeService.GetUserSrvTree(c.Request.Context(), user.Username)
	if err != nil {
		pkghttp.HandleError(c, http.StatusInternalServerError, "查询服务树失败", gin.H{"details": err.Error()})
		return
	}

	pkghttp.HandleSuccess(c, trees)
}
