package model

import (
	"time"

	"gorm.io/gorm"
)

const (
	VariableTypeNormal = 0 // 普通变量
	VariableTypeScript = 1 // 可编程变量
)

// EnvironmentVariable 环境变量表 - 用于存储应用部署时的环境变量配置
type EnvironmentVariable struct {
	ID           int64  `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	AppID        int64  `gorm:"column:app_id;type:bigint;not null;index" json:"app_id"`
	EnvID        int64  `gorm:"column:env_id;type:bigint;not null;index" json:"env_id"`
	KeyName      string `gorm:"column:key_name;type:varchar(255);not null" json:"key_name"`
	Value        string `gorm:"column:value;type:text;not null" json:"value"`
	VariableType int8   `gorm:"column:variable_type;type:tinyint;not null;default:0" json:"variable_type"`
	Dependencies string `gorm:"column:dependencies;type:text" json:"dependencies"`
	Description  string `gorm:"column:description;type:varchar(500)" json:"description"`
	CT           int64  `gorm:"column:c_t;not null" json:"c_t"`
	CreateBy     int64  `gorm:"column:create_by;type:bigint;not null" json:"create_by"`
	UT           int64  `gorm:"column:u_t;not null" json:"u_t"`
	UpdateBy     int64  `gorm:"column:update_by;type:bigint;not null" json:"update_by"`
	Status       int8   `gorm:"column:status;type:tinyint;not null;default:1" json:"status"`
	IsDeleted    int8   `gorm:"column:is_deleted;type:tinyint;not null;default:0" json:"is_deleted"`
}

func (EnvironmentVariable) TableName() string {
	return "t_env_variable"
}

// BeforeCreate will set create_time and update_time before creating
func (ev *EnvironmentVariable) BeforeCreate(tx *gorm.DB) error {
	now := time.Now()
	ev.CT = now.Unix()
	ev.UT = now.Unix()
	return nil
}

// BeforeUpdate will set update_time before updating
func (ev *EnvironmentVariable) BeforeUpdate(tx *gorm.DB) error {
	ev.UT = time.Now().Unix()
	return nil
}
