package model

import (
	"time"

	"gorm.io/gorm"
)

// ConfigFile 配置文件表 - 用于存储应用的配置文件信息
type ConfigFile struct {
	ID             int64             `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	AppID          int64             `gorm:"column:app_id;type:bigint;not null;index" json:"app_id"`
	EnvID          int64             `gorm:"column:env_id;type:bigint;not null;index" json:"env_id"`
	GroupID        int64             `gorm:"column:group_id;type:bigint;index" json:"group_id"`
	Name           string            `gorm:"column:name;type:varchar(255);not null" json:"name"`
	Path           string            `gorm:"column:path;type:varchar(500);not null" json:"path"`
	Format         string            `gorm:"column:format;type:varchar(50);not null;default:'text'" json:"format"`
	Description    string            `gorm:"column:description;type:varchar(500)" json:"description"`
	CurrentVersion int64             `gorm:"column:current_version;type:bigint;not null;default:1" json:"current_version"`
	CT             int64             `gorm:"column:c_t;not null" json:"c_t"`
	CreateBy       int64             `gorm:"column:create_by;type:bigint;not null" json:"create_by"`
	UT             int64             `gorm:"column:u_t;not null" json:"u_t"`
	UpdateBy       int64             `gorm:"column:update_by;type:bigint;not null" json:"update_by"`
	Status         int8              `gorm:"column:status;type:tinyint;not null;default:1" json:"status"`
	IsDeleted      int8              `gorm:"column:is_deleted;type:tinyint;not null;default:0" json:"is_deleted"`
	Content        ConfigFileContent `gorm:"foreignKey:FileID;references:ID;constraint:OnDelete:CASCADE" json:"content_payload"`
}

func (ConfigFile) TableName() string {
	return "t_config_file"
}

// BeforeCreate will set c_t and u_t before creating
func (cf *ConfigFile) BeforeCreate(tx *gorm.DB) error {
	now := time.Now()
	cf.CT = now.Unix()
	cf.UT = now.Unix()
	return nil
}

// BeforeUpdate will set u_t before updating
func (cf *ConfigFile) BeforeUpdate(tx *gorm.DB) error {
	cf.UT = time.Now().Unix()
	return nil
}

// AfterCreate 创建ConfigFile后设置ConfigFileContent的时间和关联关系
func (cf *ConfigFile) AfterCreate(tx *gorm.DB) error {
	if cf.Content.FileID == 0 {
		cf.Content.FileID = cf.ID
	}
	return nil
}

type ConfigFileContent struct {
	ID        int64  `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	FileID    int64  `gorm:"column:file_id;type:bigint;not null;index" json:"file_id"`
	Content   string `gorm:"column:content;type:longtext" json:"content"`
	Version   int64  `gorm:"column:version;type:bigint;not null;default:1" json:"version"`
	MD5       string `gorm:"column:md5;type:varchar(32)" json:"md5"`
	Size      int64  `gorm:"column:size;type:int;not null;default:0" json:"size"`
	CT        int64  `gorm:"column:c_t;not null" json:"c_t"`
	CreateBy  int64  `gorm:"column:create_by;type:bigint;not null" json:"create_by"`
	Remark    string `gorm:"column:remark;type:varchar(500)" json:"remark"`
	UT        int64  `gorm:"column:u_t;not null" json:"u_t"`
	UpdateBy  int64  `gorm:"column:update_by;type:bigint;not null" json:"update_by"`
	Status    int8   `gorm:"column:status;type:tinyint;not null;default:1" json:"status"`
	IsDeleted int8   `gorm:"column:is_deleted;type:tinyint;not null;default:0" json:"is_deleted"`
}

func (ConfigFileContent) TableName() string {
	return "t_config_file_content"
}

func (cfc *ConfigFileContent) BeforeCreate(tx *gorm.DB) error {
	now := time.Now()
	cfc.CT = now.Unix()
	cfc.UT = now.Unix()
	return nil
}
