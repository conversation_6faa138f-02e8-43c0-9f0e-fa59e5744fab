package model

import (
	"encoding/json"
	"time"

	"gorm.io/gorm"
)

// Container 容器配置表
type Container struct {
	ID            int64                    `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	AppID         int64                    `gorm:"column:app_id;type:bigint;not null;index:idx_app_env_group" json:"app_id"`       // 应用ID
	EnvID         int64                    `gorm:"column:env_id;type:bigint;not null;index:idx_app_env_group" json:"env_id"`       // 环境ID
	GroupID       int64                    `gorm:"column:group_id;type:bigint;not null;index:idx_app_env_group" json:"group_id"`   // 分组ID
	BaseImage     string                   `gorm:"column:base_image;type:varchar(500);not null" json:"base_image"`                 // 基础镜像
	Replicas      int                      `gorm:"column:replicas;type:int;not null;default:1" json:"replicas"`                    // 副本数量
	Namespace     string                   `gorm:"column:namespace;type:varchar(100);not null;default:'default'" json:"namespace"` // K8S命名空间
	Ports         ContainerPortsJSON       `gorm:"column:ports;type:json" json:"ports"`                                            // 端口配置，JSON格式存储
	Resources     ContainerResourcesJSON   `gorm:"column:resources;type:json;not null" json:"resources"`                           // 资源配置，JSON格式存储
	Environment   ContainerEnvironmentJSON `gorm:"column:environment;type:json" json:"environment"`                                // 环境变量配置，JSON格式存储
	Strategy      string                   `gorm:"column:strategy;type:varchar(50);not null;default:'blueGreen'" json:"strategy"`  // 部署策略：blueGreen/canary/rolling
	RolloutConfig ContainerRolloutJSON     `gorm:"column:rollout_config;type:json;not null" json:"rollout_config"`                 // 发布配置，JSON格式存储
	Status        int8                     `gorm:"column:status;type:tinyint;not null;default:1" json:"status"`                    // 状态：1-启用 0-禁用

	// 审计字段
	CT        int64 `gorm:"column:c_t;not null" json:"c_t"`
	CreateBy  int64 `gorm:"column:create_by;type:bigint;not null" json:"create_by"`
	UT        int64 `gorm:"column:u_t;not null" json:"u_t"`
	UpdateBy  int64 `gorm:"column:update_by;type:bigint;not null" json:"update_by"`
	IsDeleted int8  `gorm:"column:is_deleted;type:tinyint;not null;default:0;index:idx_deleted" json:"is_deleted"` // 记录状态：0-正常 1-删除
}

// 定义 JSON 字段类型
type ContainerPortsJSON []PortConfig
type ContainerResourcesJSON ResourceConfig
type ContainerEnvironmentJSON map[string]string
type ContainerRolloutJSON RolloutConfig

type PortConfig struct {
	Name          string `json:"name" validate:"required"`
	ContainerPort int    `json:"container_port" validate:"required,min=1,max=65535"`
	ServicePort   int    `json:"service_port" validate:"required,min=1,max=65535"`
	Protocol      string `json:"protocol" validate:"required,oneof=TCP UDP"`
}

type ResourceConfig struct {
	Requests ResourceSpec `json:"requests" validate:"required"`
	Limits   ResourceSpec `json:"limits" validate:"required"`
}

type ResourceSpec struct {
	CPU    string `json:"cpu" validate:"required"`
	Memory string `json:"memory" validate:"required"`
}

type RolloutConfig struct {
	MaxSurge             string `json:"max_surge" validate:"required"`
	MaxUnavailable       string `json:"max_unavailable" validate:"required"`
	RevisionHistoryLimit int    `json:"revision_history_limit" validate:"min=1"`
	TimeoutSeconds       int    `json:"timeout_seconds" validate:"min=1"`
}

// 实现 driver.Valuer 接口，用于写入数据库
func (c ContainerPortsJSON) Value() (interface{}, error) {
	if len(c) == 0 {
		return nil, nil
	}
	return json.Marshal(c)
}

func (c ContainerResourcesJSON) Value() (interface{}, error) {
	return json.Marshal(c)
}

func (c ContainerEnvironmentJSON) Value() (interface{}, error) {
	if len(c) == 0 {
		return nil, nil
	}
	return json.Marshal(c)
}

func (c ContainerRolloutJSON) Value() (interface{}, error) {
	return json.Marshal(c)
}

// 实现 sql.Scanner 接口，用于从数据库读取
func (c *ContainerPortsJSON) Scan(value interface{}) error {
	if value == nil {
		*c = ContainerPortsJSON{}
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return nil
	}

	return json.Unmarshal(bytes, c)
}

func (c *ContainerResourcesJSON) Scan(value interface{}) error {
	if value == nil {
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return nil
	}

	return json.Unmarshal(bytes, c)
}

func (c *ContainerEnvironmentJSON) Scan(value interface{}) error {
	if value == nil {
		*c = ContainerEnvironmentJSON{}
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return nil
	}

	return json.Unmarshal(bytes, c)
}

func (c *ContainerRolloutJSON) Scan(value interface{}) error {
	if value == nil {
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return nil
	}

	return json.Unmarshal(bytes, c)
}

func (Container) TableName() string {
	return "t_container"
}

// BeforeCreate will set create_time and update_time before creating
func (c *Container) BeforeCreate(tx *gorm.DB) error {
	now := time.Now()
	c.CT = now.Unix()
	c.UT = now.Unix()
	return nil
}

// BeforeUpdate will set update_time before updating
func (c *Container) BeforeUpdate(tx *gorm.DB) error {
	c.UT = time.Now().Unix()
	return nil
}
