package model

import (
	"time"

	"gorm.io/gorm"
)

// ImageStatus represents the status of a Redis image
type ImageStatus int

const (
	// ImageStatusInited indicates that the Redis image has been initialized
	ImageStatusInited ImageStatus = iota + 1
	// ImageStatusTesting indicates that the Redis image is currently being tested
	ImageStatusTesting
	// ImageStatusSuccess indicates that the Redis image has passed testing successfully
	ImageStatusSuccess
	// ImageStatusFailed indicates that the Redis image failed testing or is otherwise unusable
	ImageStatusFailed
	// ImageStatusActive indicates that the Redis image is active and ready for use
	ImageStatusActive
)

type Image struct {
	ID          int64  `json:"id" gorm:"primaryKey"`
	Name        string `json:"name" gorm:"uniqueIndex;not null"`
	ImageName   string `json:"image_name" gorm:"not null"`
	McImageName string `json:"mc_image_name" gorm:"not null"`
	Type        string `json:"type" gorm:"not null`
	Description string `json:"description" gorm:"not null`
	Size        int64  `json:"size" gorm:"not null;default:0"`
	Status      int8   `json:"status" gorm:"not null"`
	Ct          int64  `json:"c_t" gorm:"column:c_t"`
	CreateBy    string `json:"create_by" gorm:"not null"`
	Ut          int64  `json:"u_t" gorm:"column:u_t"`
	UpdateBy    string `json:"update_by" gorm:"not null"`
	IsDeleted   int8   `json:"is_deleted" gorm:"column:is_deleted"`
}

func (Image) TableName() string {
	return "t_image"
}

// BeforeCreate will set CT and UT before creating
func (a *Image) BeforeCreate(tx *gorm.DB) error {
	now := time.Now().Unix()
	a.Ct = now
	a.Ut = now
	return nil
}

// BeforeUpdate will set UT before updating
func (a *Image) BeforeUpdate(tx *gorm.DB) error {
	a.Ut = time.Now().Unix()
	return nil
}
