package model

// WorkflowStatus 工作流状态类型
type WorkflowStatus string

const (
	// 工作流状态常量
	WorkflowStatusPending       WorkflowStatus = "PENDING"
	WorkflowStatusRunning       WorkflowStatus = "RUNNING"
	WorkflowStatusCompleted     WorkflowStatus = "COMPLETED"
	WorkflowStatusFailed        WorkflowStatus = "FAILED"
	WorkflowStatusTriggerFailed WorkflowStatus = "TRIGGER_FAILED"
	WorkflowStatusCancelled     WorkflowStatus = "CANCELLED"
	WorkflowStatusTimedOut      WorkflowStatus = "TIMED_OUT"
)

// Description 获取工作流状态的可读描述
func (s WorkflowStatus) Description() string {
	descriptions := map[WorkflowStatus]string{
		WorkflowStatusPending:       "等待执行",
		WorkflowStatusRunning:       "执行中",
		WorkflowStatusCompleted:     "已完成",
		WorkflowStatusFailed:        "执行失败",
		WorkflowStatusTriggerFailed: "触发失败",
		WorkflowStatusCancelled:     "已取消",
		WorkflowStatusTimedOut:      "已超时",
	}

	if desc, ok := descriptions[s]; ok {
		return desc
	}
	return "未知状态"
}

// IsTerminal 判断工作流是否为终态
func (s WorkflowStatus) IsTerminal() bool {
	terminalStatuses := map[WorkflowStatus]bool{
		WorkflowStatusCompleted:     true,
		WorkflowStatusFailed:        true,
		WorkflowStatusTriggerFailed: true,
		WorkflowStatusCancelled:     true,
		WorkflowStatusTimedOut:      true,
	}

	return terminalStatuses[s]
}

// ToDeployTaskStatus 根据工作流状态获取对应的部署任务状态
func (s WorkflowStatus) ToDeployTaskStatus() int {
	statusMapping := map[WorkflowStatus]int{
		WorkflowStatusPending:       DeployTaskStatusPending,
		WorkflowStatusRunning:       DeployTaskStatusRunning,
		WorkflowStatusCompleted:     DeployTaskStatusCompleted,
		WorkflowStatusFailed:        DeployTaskStatusFailed,
		WorkflowStatusTriggerFailed: DeployTaskStatusFailed,
		WorkflowStatusCancelled:     DeployTaskStatusFailed,
		WorkflowStatusTimedOut:      DeployTaskStatusFailed,
	}

	if status, ok := statusMapping[s]; ok {
		return status
	}
	return DeployTaskStatusFailed
}

// UpdateTaskStatus 更新任务状态
func UpdateTaskStatusByWorkflow(task *DeployTask, workflowStatus WorkflowStatus) {
	task.Status = workflowStatus.ToDeployTaskStatus()
	task.WorkflowStatus = string(workflowStatus)
}
