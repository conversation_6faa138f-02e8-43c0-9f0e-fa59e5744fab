package model

import (
	"encoding/json"
	"fmt"
)

// ConfigSnapshot 部署配置快照 - 用于 Argo Workflows
type ConfigSnapshot struct {
	Version   string         `json:"version" validate:"required"`
	Timestamp int64          `json:"timestamp" validate:"required"`
	Build     *BuildConfig   `json:"build" validate:"required"`
	Container *Container     `json:"container" validate:"required"`
	Probe     *Probe         `json:"probe,omitempty"`
	Labels    LabelConfig    `json:"labels" validate:"required"`
	Metadata  MetadataConfig `json:"metadata" validate:"required"`
}

// LabelConfig Kubernetes 标签配置
type LabelConfig struct {
	AppCode       string `json:"appCode" validate:"required"`       // 应用代码，如：gis-api-service
	AppID         string `json:"appId" validate:"required"`         // 应用ID，如：23123
	AppGroup      string `json:"appGroup" validate:"required"`      // 应用分组，如：k8s-env
	AppLang       string `json:"appLang" validate:"required"`       // 应用语言，如：JAVA
	AppLevel      string `json:"appLevel" validate:"required"`      // 应用级别，如：1
	AppVersion    string `json:"appVersion" validate:"required"`    // 应用版本，如：BUILD_DEPLOY-954521
	DeployID      string `json:"deployId" validate:"required"`      // 部署ID，如：954521
	EnvironmentID string `json:"environmentId" validate:"required"` // 环境ID，如：2
	OWT           string `json:"owt" validate:"required"`           // 组织架构-团队，如：owt.meicai-middleground
	PDL           string `json:"pdl" validate:"required"`           // 组织架构-产品线，如：pdl.gis-platform
	Role          string `json:"role" validate:"required"`          // 角色，如：active
	SG            string `json:"sg" validate:"required"`            // 服务组，如：sg.gis
	SRV           string `json:"srv" validate:"required"`           // 服务名，如：srv.gis-api-service
}

// MetadataConfig 元数据配置
type MetadataConfig struct {
	AppID    int64  `json:"app_id" validate:"required"`
	EnvID    int64  `json:"env_id" validate:"required"`
	GroupID  int64  `json:"group_id" validate:"required"`
	CommitID string `json:"commit_id" validate:"required"`
	Semver   string `json:"semver" validate:"required"`
	TaskID   int64  `json:"task_id" validate:"required"`
}

// ToJSON 序列化为 JSON 字符串
func (cs *ConfigSnapshot) ToJSON() string {
	fmt.Println("cs11111111111111111", cs)
	data, err := json.Marshal(cs)
	fmt.Println("err11111111111111111", err)
	fmt.Println("data11111111111111111", string(data))
	if err != nil {
		return ""
	}
	return string(data)
}

// FromJSON 从 JSON 字符串反序列化
func (cs *ConfigSnapshot) FromJSON(jsonStr string) error {
	return json.Unmarshal([]byte(jsonStr), cs)
}
