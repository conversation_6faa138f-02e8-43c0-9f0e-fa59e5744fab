package model

import (
	"time"

	"gorm.io/gorm"
)

const (
	DeployTaskDetailStatusPending   = 1
	DeployTaskDetailStatusRunning   = 2
	DeployTaskDetailStatusPaused    = 3
	DeployTaskDetailStatusWaiting   = 4
	DeployTaskDetailStatusCompleted = 5
	DeployTaskDetailStatusConfirmed = 6
	DeployTaskDetailStatusCancelled = 7
	DeployTaskDetailStatusFailed    = 8

	DeployTaskStatusPending         = 1
	DeployTaskStatusRunning         = 2
	DeployTaskStatusCompleted       = 3
	DeployTaskStatusFailed          = 4
	DeployTaskStatusPartiallyFailed = 5
)

// DeployTask 部署任务主表 - 用于存储应用部署任务
type DeployTask struct {
	ID          int64  `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	Description string `gorm:"column:description;type:varchar(1000)" json:"description"`
	AppID       int64  `gorm:"column:app_id;type:bigint;not null;index" json:"app_id"`
	EnvID       int64  `gorm:"column:env_id;type:bigint;not null;index" json:"env_id"`
	Status      int    `gorm:"column:status;type:int;not null;index;default:1" json:"status"`
	TaskCount   int    `gorm:"column:task_count;type:int;not null;default:0" json:"task_count"`     // 任务数量
	TaskSuccess int    `gorm:"column:task_success;type:int;not null;default:0" json:"task_success"` // 成功任务数量
	TaskFailed  int    `gorm:"column:task_failed;type:int;not null;default:0" json:"task_failed"`   // 失败任务数量
	Semver      string `gorm:"column:semver;type:varchar(100);not null" json:"semver"`              // 版本号
	CommitID    string `gorm:"column:commit_id;type:varchar(100);not null" json:"commit_id"`        // 提交ID
	IsBranch    int8   `gorm:"column:is_branch;type:tinyint;not null;default:0" json:"is_branch"`   // 是否分支
	CreateBy    int64  `gorm:"column:create_by;type:bigint;not null" json:"create_by"`              // 创建人
	UpdateBy    int64  `gorm:"column:update_by;type:bigint;not null" json:"update_by"`              // 最后更新人
	CT          int64  `gorm:"column:c_t;not null" json:"c_t"`
	UT          int64  `gorm:"column:u_t;not null" json:"u_t"`
	IsDeleted   int8   `gorm:"column:is_deleted;type:tinyint;not null;default:0" json:"is_deleted"` // 记录状态：0-正常 1-删除

	// Argo Workflow相关字段
	WorkflowName      string `gorm:"column:workflow_name;type:varchar(255)" json:"workflow_name"`
	WorkflowStatus    string `gorm:"column:workflow_status;type:varchar(50)" json:"workflow_status"`
	WorkflowStartTime int64  `gorm:"column:workflow_start_time;type:bigint" json:"workflow_start_time"`
	WorkflowEndTime   int64  `gorm:"column:workflow_end_time;type:bigint" json:"workflow_end_time"`

	// 关联关系
	TaskDetails []*DeployTaskDetail `gorm:"foreignKey:TaskID;references:ID" json:"task_details,omitempty"`
}

func (DeployTask) TableName() string {
	return "t_deploy_task"
}

// BeforeCreate will set create_time and update_time before creating
func (dt *DeployTask) BeforeCreate(tx *gorm.DB) error {
	now := time.Now()
	dt.CT = now.Unix()
	dt.UT = now.Unix()
	return nil
}

// BeforeUpdate will set update_time before updating
func (dt *DeployTask) BeforeUpdate(tx *gorm.DB) error {
	dt.UT = time.Now().Unix()
	return nil
}

// DeployTaskDetail 部署任务详情表 - 用于存储应用部署任务详情
type DeployTaskDetail struct {
	ID                int64  `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	AppID             int64  `gorm:"column:app_id;type:bigint;not null;index" json:"app_id"`
	EnvID             int64  `gorm:"column:env_id;type:bigint;not null;index" json:"env_id"`
	GroupID           int64  `gorm:"column:group_id;type:bigint;index" json:"group_id"`
	TaskID            int64  `gorm:"column:task_id;type:bigint;not null;index" json:"task_id"`
	Semver            string `gorm:"column:semver;type:varchar(100);not null" json:"semver"`
	CommitID          string `gorm:"column:commit_id;type:varchar(100);not null" json:"commit_id"`
	IsBranch          int8   `gorm:"column:is_branch;type:tinyint;not null;default:0" json:"is_branch"`          // 是否分支
	Status            int    `gorm:"column:status;type:int;not null" json:"status"`                              // 排队中、进行中、暂停中、等待确认、已完成、已确认、已取消、失败、部分失败
	StartTime         int64  `gorm:"column:start_time;type:bigint" json:"start_time"`                            // 开始时间
	EndTime           int64  `gorm:"column:end_time;type:bigint" json:"end_time"`                                // 结束时间
	Duration          int64  `gorm:"column:duration;type:bigint" json:"duration"`                                // 持续时间(秒)
	CurrentStep       string `gorm:"column:current_step;type:varchar(200)" json:"current_step"`                  // 当前步骤
	TotalSteps        int    `gorm:"column:total_steps;type:int;default:0" json:"total_steps"`                   // 总步骤数
	CurrentStepIndex  int    `gorm:"column:current_step_index;type:int;default:0" json:"current_step_index"`     // 当前步骤索引
	Progress          int    `gorm:"column:progress;type:int;default:0" json:"progress"`                         // 进度百分比
	EstimatedEndTime  int64  `gorm:"column:estimated_end_time;type:bigint" json:"estimated_end_time"`            // 预计结束时间
	NeedsConfirmation int8   `gorm:"column:needs_confirmation;type:tinyint;default:0" json:"needs_confirmation"` // 是否需要确认
	ConfigSnapshot    string `gorm:"column:config_snapshot;type:json" json:"config_snapshot"`                    // 部署配置快照
	DeployType        string `gorm:"column:deploy_type;type:varchar(50);not null" json:"deploy_type"`            // 部署、编译部署、回滚部署、批量部署
	CT                int64  `gorm:"column:c_t;not null" json:"c_t"`
	UT                int64  `gorm:"column:u_t;not null" json:"u_t"`
	IsDeleted         int8   `gorm:"column:is_deleted;type:tinyint;not null;default:0" json:"is_deleted"` // 记录状态：0-正常 1-删除

	// 关联关系
	Task DeployTask  `gorm:"foreignKey:TaskID;references:ID" json:"task,omitempty"`
	Logs []DeployLog `gorm:"foreignKey:DeployTaskDetailID;references:ID" json:"logs,omitempty"`
}

func (DeployTaskDetail) TableName() string {
	return "t_deploy_task_detail"
}

// BeforeCreate will set create_time and update_time before creating
func (dh *DeployTaskDetail) BeforeCreate(tx *gorm.DB) error {
	now := time.Now()
	dh.CT = now.Unix()
	dh.UT = now.Unix()
	return nil
}

// BeforeUpdate will set update_time before updating
func (dh *DeployTaskDetail) BeforeUpdate(tx *gorm.DB) error {
	dh.UT = time.Now().Unix()
	return nil
}

// DeployLog 部署日志表 - 用于存储部署过程中的日志信息
type DeployLog struct {
	ID                 int64  `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	DeployTaskDetailID int64  `gorm:"column:deploy_task_detail_id;type:bigint;not null;index" json:"deploy_task_detail_id"`
	LogType            string `gorm:"column:log_type;type:varchar(50);not null" json:"log_type"` // info、warning、error、success
	Content            string `gorm:"column:content;type:text;not null" json:"content"`
	Timestamp          int64  `gorm:"column:timestamp;type:bigint;not null" json:"timestamp"`
	Step               string `gorm:"column:step;type:varchar(200)" json:"step"`    // 所属步骤
	StepIndex          int    `gorm:"column:step_index;type:int" json:"step_index"` // 步骤索引
	CT                 int64  `gorm:"column:c_t;not null" json:"c_t"`
	CreateBy           int64  `gorm:"column:create_by;type:bigint;not null" json:"create_by"`
	UT                 int64  `gorm:"column:u_t;not null" json:"u_t"`
	UpdateBy           int64  `gorm:"column:update_by;type:bigint;not null" json:"update_by"`
	IsDeleted          int8   `gorm:"column:is_deleted;type:tinyint;not null;default:0" json:"is_deleted"`

	// 关联关系
	TaskDetail DeployTaskDetail `gorm:"foreignKey:DeployTaskDetailID;references:ID" json:"task_detail,omitempty"`
}

func (DeployLog) TableName() string {
	return "t_deploy_log"
}

// BeforeCreate will set create_time and update_time before creating
func (dl *DeployLog) BeforeCreate(tx *gorm.DB) error {
	now := time.Now()
	dl.CT = now.Unix()
	dl.UT = now.Unix()
	return nil
}

// BeforeUpdate will set update_time before updating
func (dl *DeployLog) BeforeUpdate(tx *gorm.DB) error {
	dl.UT = time.Now().Unix()
	return nil
}
