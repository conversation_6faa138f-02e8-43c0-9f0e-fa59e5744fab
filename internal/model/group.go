package model

import (
	"time"

	"gorm.io/gorm"
)

// Group 部署分组表 - 用于管理应用的部署分组信息
type Group struct {
	ID          int64  `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	Code        string `gorm:"column:code;type:varchar(50);not null;default:'';index:idx_code" json:"code"`  // 分组编码
	Name        string `gorm:"column:name;type:varchar(100);not null;default:''" json:"name"`                // 分组名称
	Description string `gorm:"column:description;type:varchar(500)" json:"description"`                      // 分组描述
	AppID       int64  `gorm:"column:app_id;type:bigint;not null;default:0;index:idx_app_env" json:"app_id"` // 应用ID
	EnvID       int64  `gorm:"column:env_id;type:bigint;not null;default:0;index:idx_app_env" json:"env_id"` // 环境ID

	// 审计字段
	CT       int64 `gorm:"column:c_t;not null;default:0;index:idx_create_time" json:"c_t"`   // 创建时间
	CreateBy int64 `gorm:"column:create_by;type:bigint;not null;default:0" json:"create_by"` // 创建人ID
	UT       int64 `gorm:"column:u_t;not null;default:0" json:"u_t"`                         // 更新时间
	UpdateBy int64 `gorm:"column:update_by;type:bigint;not null;default:0" json:"update_by"` // 更新人ID
	// Status    int8  `gorm:"column:status;type:tinyint;not null;default:1" json:"status"`                              // 状态 0:禁用 1:启用
	IsDeleted int8 `gorm:"column:is_deleted;type:tinyint;not null;default:0;index:idx_is_deleted" json:"is_deleted"` // 记录状态：0-正常 1-删除
}

func (Group) TableName() string {
	return "t_group"
}

// BeforeCreate will set create_time and update_time before creating
func (g *Group) BeforeCreate(tx *gorm.DB) error {
	now := time.Now()
	g.CT = now.Unix()
	g.UT = now.Unix()
	return nil
}

// BeforeUpdate will set update_time before updating
func (g *Group) BeforeUpdate(tx *gorm.DB) error {
	g.UT = time.Now().Unix()
	return nil
}
