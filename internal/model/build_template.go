package model

import (
	"time"

	"gorm.io/gorm"
)

// BuildTemplate 构建模板表 - 定义各语言的构建流程模板
type BuildTemplate struct {
	ID                   int64  `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	Name                 string `gorm:"column:name;type:varchar(100);not null" json:"name"`
	Language             string `gorm:"column:language;type:varchar(50);not null;index" json:"language"`
	TemplateType         string `gorm:"column:template_type;type:varchar(50);not null;index" json:"template_type"`
	DisplayName          string `gorm:"column:display_name;type:varchar(200);not null" json:"display_name"`
	Description          string `gorm:"column:description;type:text" json:"description"`
	Icon                 string `gorm:"column:icon;type:varchar(100)" json:"icon"`
	WorkflowTemplateName string `gorm:"column:workflow_template_name;type:varchar(255);not null" json:"workflow_template_name"`
	DefaultParams        string `gorm:"column:default_params;type:json" json:"default_params"`
	ParamSchema          string `gorm:"column:param_schema;type:json" json:"param_schema"`
	IsDefault            int8   `gorm:"column:is_default;type:tinyint;default:0" json:"is_default"`
	SortOrder            int    `gorm:"column:sort_order;type:int;default:100" json:"sort_order"`
	CT                   int64  `gorm:"column:c_t;not null" json:"c_t"`
	CreateBy             int64  `gorm:"column:create_by;type:bigint;not null" json:"create_by"`
	UT                   int64  `gorm:"column:u_t;not null" json:"u_t"`
	UpdateBy             int64  `gorm:"column:update_by;type:bigint;not null" json:"update_by"`
	Status               int8   `gorm:"column:status;type:tinyint;not null;default:1" json:"status"`
	IsDeleted            int8   `gorm:"column:is_deleted;type:tinyint;not null;default:0" json:"is_deleted"`
}

func (BuildTemplate) TableName() string {
	return "t_build_template"
}

// BeforeCreate will set c_t and u_t before creating
func (bt *BuildTemplate) BeforeCreate(tx *gorm.DB) error {
	now := time.Now()
	bt.CT = now.Unix()
	bt.UT = now.Unix()
	return nil
}

// BeforeUpdate will set u_t before updating
func (bt *BuildTemplate) BeforeUpdate(tx *gorm.DB) error {
	bt.UT = time.Now().Unix()
	return nil
}

// BuildConfig 项目构建配置表 - 存储每个项目的构建配置
type BuildConfig struct {
	ID                int64  `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	AppID             int64  `gorm:"column:app_id;type:bigint;not null;index" json:"app_id"`
	EnvID             int64  `gorm:"column:env_id;type:bigint;not null;index" json:"env_id"`
	GroupID           int64  `gorm:"column:group_id;type:bigint;not null;index" json:"group_id"`
	TemplateID        int64  `gorm:"column:template_id;type:bigint;not null" json:"template_id"`
	Language          string `gorm:"column:language;type:varchar(50);not null;index" json:"language"`
	TemplateType      string `gorm:"column:template_type;type:varchar(50);not null" json:"template_type"`
	BuildParams       string `gorm:"column:build_params;type:json" json:"build_params"`
	RuntimeParams     string `gorm:"column:runtime_params;type:json" json:"runtime_params"`
	CustomBuildScript string `gorm:"column:custom_build_script;type:text" json:"custom_build_script"`
	PreBuildScript    string `gorm:"column:pre_build_script;type:text" json:"pre_build_script"`
	PostBuildScript   string `gorm:"column:post_build_script;type:text" json:"post_build_script"`
	CT                int64  `gorm:"column:c_t;not null" json:"c_t"`
	CreateBy          int64  `gorm:"column:create_by;type:bigint;not null" json:"create_by"`
	UT                int64  `gorm:"column:u_t;not null" json:"u_t"`
	UpdateBy          int64  `gorm:"column:update_by;type:bigint;not null" json:"update_by"`
	Status            int8   `gorm:"column:status;type:tinyint;not null;default:1" json:"status"`
	IsDeleted         int8   `gorm:"column:is_deleted;type:tinyint;not null;default:0" json:"is_deleted"`
}

func (BuildConfig) TableName() string {
	return "t_build_config"
}

// BeforeCreate will set c_t and u_t before creating
func (bc *BuildConfig) BeforeCreate(tx *gorm.DB) error {
	now := time.Now()
	bc.CT = now.Unix()
	bc.UT = now.Unix()
	return nil
}

// BeforeUpdate will set u_t before updating
func (bc *BuildConfig) BeforeUpdate(tx *gorm.DB) error {
	bc.UT = time.Now().Unix()
	return nil
}
