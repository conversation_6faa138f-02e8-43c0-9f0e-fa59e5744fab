package cmdb

import (
	"time"

	"gorm.io/gorm"
)

// Environment CMDB环境信息 - 对应数据库中的environment表
type Environment struct {
	ID         int64     `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	Code       string    `gorm:"column:code;type:varchar(20);uniqueIndex;not null" json:"code"`
	Name       string    `gorm:"column:name;type:varchar(20);not null" json:"name"`
	CreateTime time.Time `gorm:"column:create_time;not null" json:"create_time"`
	CreateBy   int64     `gorm:"column:create_by;type:bigint;default:0" json:"create_by"`
	UpdateTime time.Time `gorm:"column:update_time;not null" json:"update_time"`
	UpdateBy   int64     `gorm:"column:update_by;type:bigint;not null" json:"update_by"`
	Status     int8      `gorm:"column:status;type:tinyint;default:1" json:"status"`
}

func (Environment) TableName() string {
	return "environment"
}

// BeforeCreate will set create_time and update_time before creating
func (e *Environment) BeforeCreate(tx *gorm.DB) error {
	now := time.Now()
	e.CreateTime = now
	e.UpdateTime = now
	return nil
}

// BeforeUpdate will set update_time before updating
func (e *Environment) BeforeUpdate(tx *gorm.DB) error {
	e.UpdateTime = time.Now()
	return nil
}
