package cmdb

import (
	"time"

	"gorm.io/gorm"
)

// Application CMDB应用信息 - 对应数据库中的application表
type Application struct {
	ID         int64     `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	Code       string    `gorm:"column:code;type:varchar(50);uniqueIndex;not null" json:"code"`
	Name       string    `gorm:"column:name;type:varchar(50);not null" json:"name"`
	Language   string    `gorm:"column:language;type:varchar(10);not null" json:"language"`
	Basic      int8      `gorm:"column:basic;type:tinyint;default:0" json:"basic"`
	Sequence   int       `gorm:"column:sequence;type:int;default:1" json:"sequence"`
	AppTypeID  int64     `gorm:"column:app_type_id;type:bigint;default:0" json:"app_type_id"`
	AppType    *AppType  `gorm:"-" json:"app_type,omitempty"` // 应用类型信息
	Level      int       `gorm:"column:level;type:int;default:3" json:"level"`
	Corp       string    `gorm:"column:corp;type:varchar(250);default:''" json:"corp"`
	Owt        string    `gorm:"column:owt;type:varchar(250);default:''" json:"owt"`
	PDL        string    `gorm:"column:pdl;type:varchar(250);default:''" json:"pdl"`
	SG         string    `gorm:"column:sg;type:varchar(250);default:''" json:"sg"`
	Srv        string    `gorm:"column:srv;type:varchar(250);default:''" json:"srv"`
	USN        string    `gorm:"column:usn;type:varchar(250);default:''" json:"usn"`
	CreateTime time.Time `gorm:"column:create_time;not null" json:"create_time"`
	CreateBy   int64     `gorm:"column:create_by;type:bigint;default:0" json:"create_by"`
	UpdateTime time.Time `gorm:"column:update_time;not null" json:"update_time"`
	UpdateBy   int64     `gorm:"column:update_by;type:bigint;default:0" json:"update_by"`
	Status     int8      `gorm:"column:status;type:tinyint;default:1" json:"status"`
	SrvTreeID  int64     `gorm:"column:srvtree_id;type:bigint;default:0" json:"srvtree_id"`
}

func (Application) TableName() string {
	return "application"
}

// BeforeCreate will set create_time and update_time before creating
func (a *Application) BeforeCreate(tx *gorm.DB) error {
	now := time.Now()
	a.CreateTime = now
	a.UpdateTime = now
	return nil
}

// BeforeUpdate will set update_time before updating
func (a *Application) BeforeUpdate(tx *gorm.DB) error {
	a.UpdateTime = time.Now()
	return nil
}

type AppType struct {
	ID         int64     `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	Code       string    `gorm:"column:code;type:varchar(50);uniqueIndex;not null" json:"code"`
	Name       string    `gorm:"column:name;type:varchar(50);not null" json:"name"`
	CreateTime time.Time `gorm:"column:create_time;not null" json:"create_time"`
	CreateBy   int64     `gorm:"column:create_by;type:bigint;default:0" json:"create_by"`
	UpdateTime time.Time `gorm:"column:update_time;not null" json:"update_time"`
	UpdateBy   int64     `gorm:"column:update_by;type:bigint;default:0" json:"update_by"`
	Status     int8      `gorm:"column:status;type:tinyint;default:1" json:"status"`
}

func (AppType) TableName() string {
	return "app_type"
}

func (a *AppType) BeforeCreate(tx *gorm.DB) error {
	now := time.Now()
	a.CreateTime = now
	a.UpdateTime = now
	return nil
}

func (a *AppType) BeforeUpdate(tx *gorm.DB) error {
	a.UpdateTime = time.Now()
	return nil
}
