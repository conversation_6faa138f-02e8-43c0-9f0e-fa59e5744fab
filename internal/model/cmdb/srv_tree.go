package cmdb

import (
	"time"

	"gorm.io/gorm"
)

// SrvTree 服务树信息 - 对应数据库中的srv_tree表
type SrvTree struct {
	ID         int64     `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	NodeCode   string    `gorm:"column:node_code;type:varchar(50);not null" json:"node_code"`
	Name       string    `gorm:"column:name;type:varchar(50);not null" json:"name"`
	ParentID   int64     `gorm:"column:parent_id;type:bigint;not null" json:"parent_id"`
	Type       string    `gorm:"column:type;type:varchar(50);not null" json:"type"`
	ReferID    int64     `gorm:"column:refer_id;type:bigint;not null" json:"refer_id"`
	Code       string    `gorm:"column:code;type:varchar(50);not null" json:"code"`
	CreateTime time.Time `gorm:"column:create_time;not null" json:"create_time"`
	CreateBy   int64     `gorm:"column:create_by;type:bigint;default:0" json:"create_by"`
	UpdateTime time.Time `gorm:"column:update_time;not null" json:"update_time"`
	UpdateBy   int64     `gorm:"column:update_by;type:bigint;not null" json:"update_by"`
	Status     int8      `gorm:"column:status;type:tinyint;default:1" json:"status"`
}

func (SrvTree) TableName() string {
	return "srv_tree"
}

// BeforeCreate will set create_time and update_time before creating
func (s *SrvTree) BeforeCreate(tx *gorm.DB) error {
	now := time.Now()
	s.CreateTime = now
	s.UpdateTime = now
	return nil
}

// BeforeUpdate will set update_time before updating
func (s *SrvTree) BeforeUpdate(tx *gorm.DB) error {
	s.UpdateTime = time.Now()
	return nil
}
