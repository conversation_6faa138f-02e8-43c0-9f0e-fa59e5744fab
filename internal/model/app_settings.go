package model

import (
	"time"

	"gorm.io/gorm"
)

// AppSettings 应用基础配置 - 对应数据库中的t_app_settings表
type AppSettings struct {
	ID                 int64  `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	AppID              int64  `gorm:"column:app_id;type:bigint;uniqueIndex;not null" json:"app_id"`
	CodeRepository     string `gorm:"column:code_repository;type:varchar(500);not null" json:"code_repository"`
	BuildPackageName   string `gorm:"column:build_package_name;type:varchar(500);not null" json:"build_package_name"`
	DeployPackageName  string `gorm:"column:deploy_package_name;type:varchar(500);not null" json:"deploy_package_name"`
	StartCommand       string `gorm:"column:start_command;type:varchar(500)" json:"start_command"`
	StopCommand        string `gorm:"column:stop_command;type:varchar(500)" json:"stop_command"`
	HealthCheckCommand string `gorm:"column:health_check_command;type:varchar(500);not null" json:"health_check_command"`
	HealthCheck        int8   `gorm:"column:health_check;type:tinyint;not null" json:"health_check"`
	RequestPath        string `gorm:"column:request_path;type:varchar(100);not null" json:"request_path"`
	RequestPort        int    `gorm:"column:request_port;type:int;not null" json:"request_port"`
	ResponseStatus     int    `gorm:"column:response_status;type:int;not null" json:"response_status"`
	MaxRetries         int    `gorm:"column:max_retries;type:int;not null" json:"max_retries"`
	RequestTimeout     int    `gorm:"column:request_timeout;type:int;not null" json:"request_timeout"`
	Rate               int    `gorm:"column:rate;type:int;not null" json:"rate"`
	DeployPath         string `gorm:"column:deploy_path;type:varchar(500);not null" json:"deploy_path"`
	LogPath            string `gorm:"column:log_path;type:varchar(500);not null" json:"log_path"`
	BaseImage          string `gorm:"column:base_image;type:varchar(200);not null" json:"base_image"`
	Probe              string `gorm:"column:probe;type:varchar(2000);not null" json:"probe"`
	RouteType          string `gorm:"column:route_type;type:varchar(50);not null" json:"route_type"`
	Strategy           string `gorm:"column:strategy;type:varchar(4000);not null" json:"strategy"`
	CT                 int64  `gorm:"column:c_t;not null" json:"c_t"`
	CreateBy           int64  `gorm:"column:create_by;type:bigint;not null" json:"create_by"`
	UT                 int64  `gorm:"column:u_t;not null" json:"u_t"`
	UpdateBy           int64  `gorm:"column:update_by;type:bigint;not null" json:"update_by"`
	Status             int8   `gorm:"column:status;type:tinyint;not null;default:1" json:"status"`
	IsDeleted          int8   `gorm:"column:is_deleted;type:tinyint;not null;default:0" json:"is_deleted"`
}

func (AppSettings) TableName() string {
	return "t_app_settings"
}

// BeforeCreate will set create_time and update_time before creating
func (bs *AppSettings) BeforeCreate(tx *gorm.DB) error {
	now := time.Now()
	bs.CT = now.Unix()
	bs.UT = now.Unix()
	return nil
}

// BeforeUpdate will set update_time before updating
func (bs *AppSettings) BeforeUpdate(tx *gorm.DB) error {
	bs.UT = time.Now().Unix()
	return nil
}
