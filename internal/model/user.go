package model

import (
	"time"

	"gorm.io/gorm"
)

const (
	RoleUser  = 1
	RoleDBA   = 2
	RoleAdmin = 3
)

type User struct {
	ID         int64  `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	Username   string `gorm:"column:username;type:varchar(255);uniqueIndex;not null" json:"username"`
	Dispname   string `gorm:"column:dispname;type:varchar(255);not null" json:"dispname"`
	Phone      string `gorm:"column:phone;type:varchar(20);not null" json:"phone"`
	Email      string `gorm:"column:email;type:varchar(255);uniqueIndex" json:"email"`
	Role       int    `gorm:"column:role;type:int;" json:"role"`
	CT         int64  `gorm:"column:c_t;not null" json:"ct"`
	UT         int64  `gorm:"column:u_t;not null" json:"ut"`
	Status     int    `gorm:"column:status;type:tinyint;default:1" json:"status"`
	CurrentApp string `gorm:"column:current_app;type:varchar(255);not null" json:"current_app"`
	Token      string `gorm:"-" json:"token"`
}

func (User) TableName() string {
	return "t_user"
}

// BeforeCreate will set CT and UT before creating
func (a *User) BeforeCreate(tx *gorm.DB) error {
	now := time.Now().Unix()
	a.CT = now
	a.UT = now
	return nil
}

// BeforeUpdate will set UT before updating
func (a *User) BeforeUpdate(tx *gorm.DB) error {
	a.UT = time.Now().Unix()
	return nil
}
