package model

import (
	"encoding/json"
)

// Probe 探针配置
type Probe struct {
	ID                  int64           `gorm:"column:id;primaryKey" json:"id"`
	AppID               int64           `gorm:"column:app_id" json:"app_id"`
	EnvID               int64           `gorm:"column:env_id" json:"env_id"`
	GroupID             int64           `gorm:"column:group_id" json:"group_id"`
	ProbeType           int8            `gorm:"column:probe_type" json:"probe_type"`                       // 探针类型: 1(启动探针), 2(就绪探针), 3(存活探针)
	ProbeMethod         int8            `gorm:"column:probe_method" json:"probe_method"`                   // 探针方法: 1(http), 2(tcp), 3(exec)
	InitialDelaySeconds int             `gorm:"column:initial_delay_seconds" json:"initial_delay_seconds"` // 初始延迟秒数
	TimeoutSeconds      int             `gorm:"column:timeout_seconds" json:"timeout_seconds"`             // 超时秒数
	PeriodSeconds       int             `gorm:"column:period_seconds" json:"period_seconds"`               // 检测周期秒数
	SuccessThreshold    int             `gorm:"column:success_threshold" json:"success_threshold"`         // 成功阈值
	FailureThreshold    int             `gorm:"column:failure_threshold" json:"failure_threshold"`         // 失败阈值
	HTTPPath            string          `gorm:"column:http_path" json:"http_path"`                         // HTTP探针路径
	HTTPPort            int             `gorm:"column:http_port" json:"http_port"`                         // HTTP探针端口
	HTTPScheme          string          `gorm:"column:http_scheme" json:"http_scheme"`                     // HTTP协议(HTTP/HTTPS)
	HTTPHeaders         json.RawMessage `gorm:"column:http_headers" json:"http_headers"`                   // HTTP请求头(JSON格式)
	TCPPort             int             `gorm:"column:tcp_port" json:"tcp_port"`                           // TCP探针端口
	ExecCommand         string          `gorm:"column:exec_command" json:"exec_command"`                   // 执行命令探针
	CreatedAt           int64           `gorm:"column:c_t" json:"created_at"`                              // 创建时间
	CreatedBy           int64           `gorm:"column:create_by" json:"created_by"`                        // 创建人ID
	UpdatedAt           int64           `gorm:"column:u_t" json:"updated_at"`                              // 更新时间
	UpdatedBy           int64           `gorm:"column:update_by" json:"updated_by"`                        // 更新人ID
	Status              int8            `gorm:"column:status" json:"status"`                               // 状态 0:禁用 1:启用
	IsDeleted           int8            `gorm:"column:is_deleted" json:"is_deleted"`                       // 是否删除 0:否 1:是
}

// TableName 表名
func (r *Probe) TableName() string {
	return "t_probe"
}

// 探针类型常量
const (
	ProbeTypeStartup   int8 = 1 // 启动探针
	ProbeTypeReadiness int8 = 2 // 就绪探针
	ProbeTypeLiveness  int8 = 3 // 存活探针
)

// 探针方法常量
const (
	ProbeMethodHTTP int8 = 1 // HTTP探针
	ProbeMethodTCP  int8 = 2 // TCP探针
	ProbeMethodExec int8 = 3 // 执行命令探针
)
