package middleware

import (
	"ci-gateway/internal/model"
	"ci-gateway/internal/repository"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/gin-contrib/sessions"
	"github.com/gin-gonic/gin"
	"github.com/spf13/viper"
	"go.uber.org/zap"
)

func AuthMiddleware(logger *zap.Logger, config *viper.Viper, userRepo repository.UserRepository) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 检查是否是开发环境
		if config.GetString("env") == "dev" {
			logger.Info("使用开发环境配置，自动登录为maqiang01用户")

			// 在开发环境中，自动使用maqiang01用户
			user, err := userRepo.GetByUsername("maqiang01")
			if err != nil || user == nil {
				// 如果用户不存在，创建一个新用户
				logger.Info("maqiang01用户不存在，创建新用户")
				user = &model.User{
					Username: "maqiang01",
					Dispname: "开发者账号",
					Email:    "<EMAIL>",
					Phone:    "13800138000",   // 添加一个默认电话号码
					Role:     model.RoleAdmin, // 使用常量表示管理员角色
					Status:   1,               // 正常状态
				}
				if err := userRepo.Create(user); err != nil {
					logger.Error("创建开发用户失败", zap.Error(err))
					c.JSON(http.StatusInternalServerError, gin.H{"error": "创建开发用户失败"})
					c.Abort()
					return
				}
				logger.Info("成功创建maqiang01用户")
			} else {
				logger.Info("使用已存在的maqiang01用户", zap.String("dispname", user.Dispname), zap.Int("role", user.Role))
				// 确保用户有管理员权限
				if user.Role != model.RoleAdmin {
					user.Role = model.RoleAdmin
					if err := userRepo.Update(user); err != nil {
						logger.Warn("更新用户角色失败", zap.Error(err))
					} else {
						logger.Info("已更新用户为管理员角色")
					}
				}
			}

			// 存储用户信息到会话
			session := sessions.Default(c)
			session.Set("user", user)
			session.Save()

			c.Set("username", user.Username)
			c.Next()
			return
		}

		// 非开发环境，使用正常的认证流程
		var token string
		if strings.HasPrefix(c.Request.Header.Get("Upgrade"), "websocket") {
			token = c.Query("token")
		} else {
			token, _ = c.Cookie(config.GetString("sso.token"))
		}

		if token == "" {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
			c.Abort()
			return
		}

		user := validateAndGetUser(c, token, config, userRepo)
		if user == nil {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
			c.Abort()
			return
		}

		// Store user information in session
		session := sessions.Default(c)
		session.Set("user", user)
		session.Save()

		c.Set("username", user.Username)
		c.Next()
	}
}

func validateAndGetUser(c *gin.Context, token string, config *viper.Viper, userRepo repository.UserRepository) *model.User {
	userInfo, err := getUserInfo(c, token, config)
	if err != nil || userInfo == "" {
		return nil
	}

	var userResp UserResponse
	if err := json.Unmarshal([]byte(userInfo), &userResp); err != nil {
		return nil
	}

	username := strings.Split(userResp.Data.Email, "@")[0]
	user, err := userRepo.GetByUsername(username)
	if err != nil {
		return nil
	}

	if user == nil {
		role := 1
		if username == "maqiang01" {
			role = 3
		}
		user = &model.User{
			Username: username,
			Dispname: userResp.Data.Name,
			Phone:    userResp.Data.Phone,
			Email:    userResp.Data.Email,
			Role:     role,
		}
		if err := userRepo.Create(user); err != nil {
			fmt.Println("create user failed", err)
			return nil
		}
	} else if username == "maqiang01" && user.Role != 3 {
		user.Role = 3
		if err := userRepo.Update(user); err != nil {
			return nil
		}
	}

	user.Token = token

	return user
}

func getUserInfo(c *gin.Context, token string, config *viper.Viper) (string, error) {
	ssoCookieUserInfo := config.GetString("sso.principal")
	userInfo, err := c.Cookie(ssoCookieUserInfo)
	if err == nil && userInfo != "" {
		return url.QueryUnescape(userInfo)
	}

	return fetchAndSetUserInfo(c, token, config)
}

func fetchAndSetUserInfo(c *gin.Context, token string, config *viper.Viper) (string, error) {
	ssoCookieUserInfo := config.GetString("sso.principal")
	ssoSystemKey := config.GetString("sso.system_key")
	ssoAddr := config.GetString("sso.sso_addr")

	tokenForm := url.Values{
		"real_ip":    {c.ClientIP()},
		"user_token": {token},
		"system_key": {ssoSystemKey},
	}

	client := &http.Client{Timeout: 5 * time.Second}
	resp, err := client.PostForm(ssoAddr+"/user/tokeninfo", tokenForm)
	if err != nil {
		return "", fmt.Errorf("failed to request SSO URL: %v", err)
	}
	defer resp.Body.Close()

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read SSO response: %v", err)
	}

	if len(body) == 0 {
		return "", fmt.Errorf("SSO response is empty")
	}

	var authUser AuthUser
	if err := json.Unmarshal(body, &authUser); err != nil {
		return "", fmt.Errorf("failed to unmarshal SSO response: %v", err)
	}

	if !strings.Contains(authUser.Data.Email, "@") {
		return "", fmt.Errorf("invalid email in SSO response: %s", authUser.Data.Email)
	}

	jsonStr, err := json.Marshal(authUser)
	if err != nil {
		return "", fmt.Errorf("failed to marshal auth user: %v", err)
	}

	userInfo := url.QueryEscape(string(jsonStr))
	c.SetCookie(ssoCookieUserInfo, userInfo, 0, "/", "", false, true)
	return string(jsonStr), nil
}

type UserResponse struct {
	Data UserData `json:"data"`
}

type UserData struct {
	ID    string `json:"id"`
	Name  string `json:"name"`
	Email string `json:"email"`
	Phone string `json:"phone"`
}

type AuthUser struct {
	Data UserData `json:"data"`
}
