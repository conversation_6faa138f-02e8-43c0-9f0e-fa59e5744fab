package middleware

import (
	"bytes"
	"encoding/json"
	"io"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// responseWriter 包装gin.ResponseWriter以捕获响应数据
type responseWriter struct {
	gin.ResponseWriter
	body *bytes.Buffer
}

func (w *responseWriter) Write(b []byte) (int, error) {
	w.body.Write(b)
	return w.ResponseWriter.Write(b)
}

// LoggingMiddleware 日志中间件
func LoggingMiddleware(logger *zap.Logger) gin.HandlerFunc {
	return gin.LoggerWithFormatter(func(param gin.LogFormatterParams) string {
		// 这个函数实际上不会被调用，因为我们用自定义的实现
		return ""
	})
}

// RequestResponseLoggingMiddleware 详细的请求响应日志中间件
func RequestResponseLoggingMiddleware(logger *zap.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		startTime := time.Now()

		// 读取请求体
		var requestBody []byte
		if c.Request.Body != nil {
			requestBody, _ = io.ReadAll(c.Request.Body)
			c.Request.Body = io.NopCloser(bytes.NewBuffer(requestBody))
		}

		// 包装响应写入器以捕获响应体
		blw := &responseWriter{
			ResponseWriter: c.Writer,
			body:           bytes.NewBufferString(""),
		}
		c.Writer = blw

		// 记录请求开始日志
		fields := []zap.Field{
			zap.String("method", c.Request.Method),
			zap.String("path", c.Request.URL.Path),
			zap.String("query", c.Request.URL.RawQuery),
			zap.String("ip", c.ClientIP()),
			zap.String("user_agent", c.Request.UserAgent()),
		}

		// 如果有请求体且不为空，记录请求体（JSON格式）
		if len(requestBody) > 0 {
			var requestJSON interface{}
			if err := json.Unmarshal(requestBody, &requestJSON); err == nil {
				fields = append(fields, zap.Any("request_body", requestJSON))
			} else {
				fields = append(fields, zap.String("request_body", string(requestBody)))
			}
		}

		logger.Info("HTTP请求开始", fields...)

		// 处理请求
		c.Next()

		// 计算处理时间
		duration := time.Since(startTime)

		// 记录响应日志
		responseFields := []zap.Field{
			zap.String("method", c.Request.Method),
			zap.String("path", c.Request.URL.Path),
			zap.Int("status", c.Writer.Status()),
			zap.Duration("duration", duration),
			zap.String("ip", c.ClientIP()),
		}

		// 记录响应体（如果是JSON格式）
		if blw.body.Len() > 0 {
			var responseJSON interface{}
			if err := json.Unmarshal(blw.body.Bytes(), &responseJSON); err == nil {
				responseFields = append(responseFields, zap.Any("response_body", responseJSON))
			}
		}

		// 如果有错误，记录错误信息
		if len(c.Errors) > 0 {
			responseFields = append(responseFields, zap.Any("errors", c.Errors.Errors()))
		}

		// 根据状态码确定日志级别
		if c.Writer.Status() >= 500 {
			logger.Error("HTTP请求完成", responseFields...)
		} else if c.Writer.Status() >= 400 {
			logger.Warn("HTTP请求完成", responseFields...)
		} else {
			logger.Info("HTTP请求完成", responseFields...)
		}
	}
}
