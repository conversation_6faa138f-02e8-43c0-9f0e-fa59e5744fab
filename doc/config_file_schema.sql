-- 配置文件表结构
CREATE TABLE `t_config_file` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `app_id` BIGINT NOT NULL DEFAULT 0 COMMENT '应用ID',
  `env_id` BIGINT NOT NULL DEFAULT 0 COMMENT '环境ID',
  `group_id` BIGINT COMMENT '分组ID, 0表示全局',
  `name` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '配置文件名',
  `path` VARCHAR(500) NOT NULL DEFAULT '' COMMENT '配置文件路径',
  `format` VARCHAR(50) NOT NULL DEFAULT 'text' COMMENT '文件格式',
  `description` VARCHAR(500) COMMENT '描述',
  `c_t` BIGINT NOT NULL DEFAULT 0 COMMENT '创建时间',
  `create_by` BIGINT NOT NULL DEFAULT 0 COMMENT '创建人ID',
  `u_t` BIGINT NOT NULL DEFAULT 0 COMMENT '更新时间',
  `update_by` BIGINT NOT NULL DEFAULT 0 COMMENT '更新人ID',
  `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态 0:禁用 1:启用',
  `current_version` INT NOT NULL DEFAULT 1 COMMENT '当前使用的版本号',
  `is_deleted` TINYINT NOT NULL DEFAULT 0 COMMENT '是否删除 0:否 1:是',
  PRIMARY KEY (`id`),
  INDEX `idx_app_id` (`app_id`),
  INDEX `idx_env_id` (`env_id`),
  INDEX `idx_group_id` (`group_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='配置文件表 - 用于存储应用的配置文件信息'; 