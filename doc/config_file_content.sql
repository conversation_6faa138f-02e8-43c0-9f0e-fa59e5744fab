CREATE TABLE `t_config_file_content` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `file_id` BIGINT NOT NULL DEFAULT 0 COMMENT '关联的配置文件ID',
  `content` LONGTEXT NOT NULL DEFAULT '' COMMENT '配置文件内容',
  `version` BIGINT NOT NULL DEFAULT 1 COMMENT '内容版本号',
  `md5` VARCHAR(32) COMMENT '内容MD5校验值',
  `size` INT UNSIGNED DEFAULT 0 COMMENT '内容大小(字节)',
  `c_t` BIGINT NOT NULL DEFAULT 0 COMMENT '创建时间',
  `create_by` BIGINT NOT NULL DEFAULT 0 COMMENT '创建人ID',
  `remark` VARCHAR(500) COMMENT '版本说明',
  `u_t` BIGINT NOT NULL DEFAULT 0 COMMENT '更新时间',
  `update_by` BIGINT NOT NULL DEFAULT 0 COMMENT '更新人ID',
  `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态 1:正常 0:删除',
  `is_deleted` TINYINT NOT NULL DEFAULT 0 COMMENT '是否删除 0:否 1:是',
  PRIMARY KEY (`id`),
  INDEX `idx_file_id` (`file_id`),
  INDEX `idx_file_version` (`file_id`, `version` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='配置文件内容表 - 用于存储配置文件的内容及版本信息';

-- 用于查询最新版本内容的示例SQL
-- SELECT * FROM t_config_file_content WHERE file_id = ? AND status = 1 ORDER BY version DESC LIMIT 1;
