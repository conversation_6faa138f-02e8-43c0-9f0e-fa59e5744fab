-- 探针配置表
-- 用于存储应用运行时的探针配置参数（启动探针、就绪探针、存活探针）

CREATE TABLE `t_probe` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `app_id` BIGINT NOT NULL DEFAULT 0 COMMENT '应用ID',
  `env_id` BIGINT NOT NULL DEFAULT 0 COMMENT '环境ID',
  `group_id` BIGINT NOT NULL DEFAULT 0 COMMENT '分组ID',
  
  -- 探针类型
  `probe_type` TINYINT NOT NULL DEFAULT 2 COMMENT '探针类型: 1(启动探针), 2(就绪探针), 3(存活探针)',
  
  -- 通用配置
  `probe_method` TINYINT NOT NULL DEFAULT 1 COMMENT '探针方法: 1(http), 2(tcp), 3(exec)',
  `initial_delay_seconds` INT NOT NULL DEFAULT 0 COMMENT '初始延迟秒数',
  `timeout_seconds` INT NOT NULL DEFAULT 1 COMMENT '超时秒数',
  `period_seconds` INT NOT NULL DEFAULT 10 COMMENT '检测周期秒数',
  `success_threshold` INT NOT NULL DEFAULT 1 COMMENT '成功阈值',
  `failure_threshold` INT NOT NULL DEFAULT 3 COMMENT '失败阈值',
  
  -- HTTP探针配置
  `http_path` VARCHAR(200) DEFAULT '/health' COMMENT 'HTTP探针路径',
  `http_port` INT DEFAULT 8080 COMMENT 'HTTP探针端口',
  `http_scheme` VARCHAR(10) DEFAULT 'HTTP' COMMENT 'HTTP协议(HTTP/HTTPS)',
  `http_headers` JSON COMMENT 'HTTP请求头(JSON格式)',
  
  -- TCP探针配置
  `tcp_port` INT COMMENT 'TCP探针端口',
  
  -- 执行命令探针配置
  `exec_command` VARCHAR(500) COMMENT '执行命令',
  
  -- 审计字段
  `c_t` BIGINT NOT NULL DEFAULT 0 COMMENT '创建时间',
  `create_by` BIGINT NOT NULL DEFAULT 0 COMMENT '创建人ID',
  `u_t` BIGINT NOT NULL DEFAULT 0 COMMENT '更新时间',
  `update_by` BIGINT NOT NULL DEFAULT 0 COMMENT '更新人ID',
  `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态 0:禁用 1:启用',
  `is_deleted` TINYINT NOT NULL DEFAULT 0 COMMENT '是否删除 0:否 1:是',
  
  PRIMARY KEY (`id`),
  INDEX `idx_app_id` (`app_id`),
  INDEX `idx_env_id` (`env_id`),
  INDEX `idx_group_id` (`group_id`),
  INDEX `idx_probe_type` (`probe_type`),
  UNIQUE KEY `uk_app_env_type` (`app_id`, `env_id`, `probe_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='探针配置表 - 用于存储应用运行时的探针配置参数'; 