-- 环境变量表结构
CREATE TABLE `t_env_variable` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `app_id` BIGINT NOT NULL DEFAULT 0 COMMENT '应用ID',
  `env_id` BIGINT NOT NULL DEFAULT 0 COMMENT '环境ID',
  `key_name` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '环境变量名',
  `value` TEXT NOT NULL DEFAULT '' COMMENT '环境变量值',
  `description` VARCHAR(500) COMMENT '描述',
  `variable_type` TINYINT NOT NULL DEFAULT 0 COMMENT '变量类型 0:普通变量 1:可编程变量',
  `dependencies` TEXT COMMENT '依赖的其他环境变量ID列表',
  `c_t` BIGINT NOT NULL DEFAULT 0 COMMENT '创建时间',
  `create_by` BIGINT NOT NULL DEFAULT 0 COMMENT '创建人ID',
  `u_t` BIGINT NOT NULL DEFAULT 0 COMMENT '更新时间',
  `update_by` BIGINT NOT NULL DEFAULT 0 COMMENT '更新人ID',
  `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态 0:禁用 1:启用',
  `is_deleted` TINYINT NOT NULL DEFAULT 0 COMMENT '是否删除 0:否 1:是',
  PRIMARY KEY (`id`),
  INDEX `idx_app_id` (`app_id`),
  INDEX `idx_env_id` (`env_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='环境变量表 - 用于存储应用部署时的环境变量配置'; 