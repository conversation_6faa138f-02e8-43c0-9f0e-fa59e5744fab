-- 部署分组表结构
CREATE TABLE `t_group` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `code` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '分组编码',
  `name` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '分组名称',
  `description` VARCHAR(500) COMMENT '分组描述',
  `app_id` BIGINT NOT NULL DEFAULT 0 COMMENT '应用ID',
  `env_id` BIGINT NOT NULL DEFAULT 0 COMMENT '环境ID',
  
  -- 审计字段
  `c_t` BIGINT NOT NULL DEFAULT 0 COMMENT '创建时间',
  `create_by` BIGINT NOT NULL DEFAULT 0 COMMENT '创建人ID',
  `u_t` BIGINT NOT NULL DEFAULT 0 COMMENT '更新时间',
  `update_by` BIGINT NOT NULL DEFAULT 0 COMMENT '更新人ID',
  `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态 0:禁用 1:启用',
  `is_deleted` TINYINT NOT NULL DEFAULT 0 COMMENT '记录状态 0:正常 1:删除',
  
  PRIMARY KEY (`id`),
  INDEX `idx_code` (`code`),
  INDEX `idx_app_env` (`app_id`, `env_id`),
  INDEX `idx_app_id` (`app_id`),
  INDEX `idx_env_id` (`env_id`),
  INDEX `idx_create_time` (`c_t`),
  INDEX `idx_is_deleted` (`is_deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='部署分组表 - 用于管理应用的部署分组信息'; 


