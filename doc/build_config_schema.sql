-- 构建模板表 - 定义各语言的构建流程模板
CREATE TABLE `t_build_template` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '模板名称',
  `language` VARCHAR(50) NOT NULL DEFAULT 'java' COMMENT '编程语言：java|go|php|python|nodejs',
  `template_type` VARCHAR(50) NOT NULL DEFAULT 'smart-detect' COMMENT '模板类型：smart-detect|spring-boot-jar|spring-boot-war|gin-api|laravel|django|express',
  `display_name` VARCHAR(200) NOT NULL DEFAULT '' COMMENT '显示名称',
  `description` TEXT COMMENT '模板描述',
  `icon` VARCHAR(100) COMMENT '图标路径',
  `workflow_template_name` VARCHAR(255) NOT NULL DEFAULT '' COMMENT 'Argo Workflow模板名称',
  `default_params` JSON COMMENT '默认参数配置(JSON)',
  `param_schema` JSON COMMENT '参数配置模式(J<PERSON><PERSON> Schema)',
  `is_default` TINYINT DEFAULT 0 COMMENT '是否为该语言的默认模板',
  `sort_order` INT DEFAULT 100 COMMENT '排序顺序',
  `c_t` BIGINT NOT NULL DEFAULT 0 COMMENT '创建时间',
  `create_by` BIGINT NOT NULL DEFAULT 0 COMMENT '创建人ID',
  `u_t` BIGINT NOT NULL DEFAULT 0 COMMENT '更新时间',
  `update_by` BIGINT NOT NULL DEFAULT 0 COMMENT '更新人ID',
  `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态 0:禁用 1:启用',
  `is_deleted` TINYINT NOT NULL DEFAULT 0 COMMENT '是否删除 0:否 1:是',
  PRIMARY KEY (`id`),
  INDEX `idx_language` (`language`),
  INDEX `idx_template_type` (`template_type`),
  INDEX `idx_status` (`status`),
  INDEX `idx_is_default` (`is_default`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='构建模板表 - 定义各语言的构建流程模板';

-- 项目构建配置表 - 存储每个项目的构建配置
CREATE TABLE `t_build_config` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `app_id` BIGINT NOT NULL DEFAULT 0 COMMENT '应用ID',
  `env_id` BIGINT NOT NULL DEFAULT 0 COMMENT '环境ID',
  `group_id` BIGINT NOT NULL DEFAULT 0 COMMENT '分组ID',
  `template_id` BIGINT NOT NULL DEFAULT 0 COMMENT '构建模板ID',
  `language` VARCHAR(50) NOT NULL DEFAULT 'java' COMMENT '编程语言',
  `template_type` VARCHAR(50) NOT NULL DEFAULT 'smart-detect' COMMENT '模板类型',
  `build_params` JSON COMMENT '构建参数配置(JSON)',
  `runtime_params` JSON COMMENT '运行时参数配置(JSON)',
  `custom_build_script` TEXT COMMENT '自定义构建脚本',
  `pre_build_script` TEXT COMMENT '构建前脚本',
  `post_build_script` TEXT COMMENT '构建后脚本',
  `c_t` BIGINT NOT NULL DEFAULT 0 COMMENT '创建时间',
  `create_by` BIGINT NOT NULL DEFAULT 0 COMMENT '创建人ID',
  `u_t` BIGINT NOT NULL DEFAULT 0 COMMENT '更新时间',
  `update_by` BIGINT NOT NULL DEFAULT 0 COMMENT '更新人ID',
  `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态 0:禁用 1:启用',
  `is_deleted` TINYINT NOT NULL DEFAULT 0 COMMENT '是否删除 0:否 1:是',
  PRIMARY KEY (`id`),
  UNIQUE INDEX `uk_app_env` (`app_id`, `env_id`),
  INDEX `idx_template_id` (`template_id`),
  INDEX `idx_language` (`language`),
  INDEX `idx_status` (`status`),
  FOREIGN KEY (`template_id`) REFERENCES `t_build_template`(`id`) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='项目构建配置表 - 存储每个项目的构建配置';

-- 插入默认的Java构建模板
INSERT INTO `t_build_template` (
  `name`, `language`, `template_type`, `display_name`, `description`, `icon`,
  `workflow_template_name`, `default_params`, `param_schema`, `is_default`, `sort_order`,
  `c_t`, `create_by`, `u_t`, `update_by`, `status`
) VALUES 
(
  'java-smart-detect', 'java', 'smart-detect', 'Java智能检测', 
  '自动检测Java项目类型并选择最佳构建方式', 'java-icon.svg',
  'java-jib-build', 
  JSON_OBJECT(
    'java_version', '17',
    'build_tool', 'auto',
    'skip_tests', true,
    'parallel_build', true,
    'maven_profiles', '',
    'custom_build_command', ''
  ),
  JSON_OBJECT(
    'type', 'object',
    'properties', JSON_OBJECT(
      'java_version', JSON_OBJECT(
        'type', 'string',
        'title', 'Java版本',
        'enum', JSON_ARRAY('8', '11', '17', '21'),
        'default', '17',
        'description', '选择Java运行时版本'
      ),
      'build_tool', JSON_OBJECT(
        'type', 'string',
        'title', '构建工具',
        'enum', JSON_ARRAY('auto', 'maven', 'gradle'),
        'default', 'auto',
        'description', '构建工具，auto表示自动检测'
      ),
      'skip_tests', JSON_OBJECT(
        'type', 'boolean',
        'title', '跳过测试',
        'default', true,
        'description', '构建时是否跳过单元测试'
      ),
      'parallel_build', JSON_OBJECT(
        'type', 'boolean', 
        'title', '并行构建',
        'default', true,
        'description', '是否启用并行构建以提高速度'
      )
    )
  ),
  1, 1, UNIX_TIMESTAMP(), 1, UNIX_TIMESTAMP(), 1, 1
),
(
  'java-spring-boot-jar', 'java', 'spring-boot-jar', 'Spring Boot JAR', 
  '构建Spring Boot应用为可执行JAR包', 'spring-boot-icon.svg',
  'java-jib-build',
  JSON_OBJECT(
    'java_version', '17',
    'build_tool', 'maven',
    'skip_tests', true,
    'main_class', '',
    'jvm_memory', 'auto'
  ),
  JSON_OBJECT(
    'type', 'object',
    'properties', JSON_OBJECT(
      'java_version', JSON_OBJECT(
        'type', 'string',
        'title', 'Java版本',
        'enum', JSON_ARRAY('8', '11', '17', '21'),
        'default', '17'
      ),
      'main_class', JSON_OBJECT(
        'type', 'string',
        'title', '主类',
        'description', '应用主类，留空自动检测'
      ),
      'jvm_memory', JSON_OBJECT(
        'type', 'string',
        'title', '运行时内存',
        'enum', JSON_ARRAY('auto', '512m', '1g', '2g', '4g'),
        'default', 'auto'
      )
    )
  ),
  0, 2, UNIX_TIMESTAMP(), 1, UNIX_TIMESTAMP(), 1, 1
); 