-- 部署任务主表结构
CREATE TABLE `t_deploy_task` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `description` VARCHAR(1000) COMMENT '部署任务描述',
  `app_id` BIGINT NOT NULL COMMENT '应用ID',
  `env_id` BIGINT NOT NULL COMMENT '环境ID',
  `status` INT NOT NULL DEFAULT 1 COMMENT '整体部署状态 1:未部署 2:部署中 3:部署完成 4:部署失败 5:部分成功',
  `task_count` INT NOT NULL DEFAULT 0 COMMENT '任务数量',
  `task_success` INT NOT NULL DEFAULT 0 COMMENT '成功任务数量',
  `task_failed` INT NOT NULL DEFAULT 0 COMMENT '失败任务数量',
  `semver` VARCHAR(100) NOT NULL COMMENT '版本号',
  `commit_id` VARCHAR(100) NOT NULL COMMENT '提交ID',
  `is_branch` TINYINT NOT NULL DEFAULT 0 COMMENT '是否分支 0:否 1:是',
  `created_by` BIGINT NOT NULL COMMENT '创建人',
  `updated_by` BIGINT NOT NULL COMMENT '最后更新人',
  `workflow_name` VARCHAR(255) NULL COMMENT 'Argo Workflow名称',
  `workflow_status` VARCHAR(50) NULL COMMENT 'Workflow状态',
  `workflow_start_time` BIGINT NULL COMMENT 'Workflow开始时间',
  `workflow_end_time` BIGINT NULL COMMENT 'Workflow结束时间',
  `c_t` BIGINT NOT NULL COMMENT '创建时间',
  `u_t` BIGINT NOT NULL COMMENT '更新时间',
  `is_deleted` TINYINT NOT NULL DEFAULT 0 COMMENT '记录状态 0:正常 1:删除',
  PRIMARY KEY (`id`),
  INDEX `idx_app_id` (`app_id`),
  INDEX `idx_env_id` (`env_id`),
  INDEX `idx_status` (`status`),
  INDEX `idx_created_by` (`created_by`),
  INDEX `idx_create_time` (`c_t`),
  INDEX `idx_workflow_name` (`workflow_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='部署任务主表 - 用于管理部署任务';


-- 部署任务详情表（原部署历史表）
CREATE TABLE `t_deploy_task_detail` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `app_id` BIGINT NOT NULL COMMENT '应用ID',
  `env_id` BIGINT NOT NULL COMMENT '环境ID',
  `group_id` BIGINT COMMENT '分组ID',
  `is_branch` TINYINT NOT NULL DEFAULT 0 COMMENT '是否分支 0:否 1:是',
  `task_id` BIGINT NOT NULL COMMENT '部署任务ID',
  `semver` VARCHAR(100) NOT NULL COMMENT '版本号',
  `commit_id` VARCHAR(100) NOT NULL COMMENT '提交ID',
  `status` INT NOT NULL COMMENT '部署状态 1:排队中 2:进行中 3:暂停中 4:等待确认 5:已完成 6:已确认 7:已取消 8:失败',
  `start_time` BIGINT COMMENT '开始时间戳',
  `end_time` BIGINT COMMENT '结束时间戳',
  `duration` BIGINT COMMENT '持续时间(秒)',
  `current_step` VARCHAR(200) COMMENT '当前执行步骤',
  `total_steps` INT DEFAULT 0 COMMENT '总步骤数',
  `current_step_index` INT DEFAULT 0 COMMENT '当前步骤索引',
  `progress` INT DEFAULT 0 COMMENT '进度百分比 0-100',
  `estimated_end_time` BIGINT COMMENT '预计结束时间戳',
  `needs_confirmation` TINYINT DEFAULT 0 COMMENT '是否需要人工确认 0:否 1:是',
  `config_snapshot` JSON COMMENT '部署配置快照',
  `deploy_type` VARCHAR(50) NOT NULL COMMENT '部署类型 deploy:部署|rollback:回滚',
  `c_t` BIGINT NOT NULL COMMENT '创建时间',
  `u_t` BIGINT NOT NULL COMMENT '更新时间',
  `is_deleted` TINYINT NOT NULL DEFAULT 0 COMMENT '记录状态 0:正常 1:删除',
  PRIMARY KEY (`id`),
  INDEX `idx_app_id` (`app_id`),
  INDEX `idx_env_id` (`env_id`),
  INDEX `idx_group_id` (`group_id`),
  INDEX `idx_task_id` (`task_id`),
  INDEX `idx_deploy_type` (`deploy_type`),
  INDEX `idx_status` (`status`),
  INDEX `idx_start_time` (`start_time`),
  INDEX `idx_create_time` (`c_t`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='部署任务详情表 - 用于存储部署执行的具体记录';

-- 部署日志表结构
CREATE TABLE `t_deploy_task_log` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `deploy_task_detail_id` BIGINT NOT NULL COMMENT '部署任务详情ID',
  `log_type` VARCHAR(50) NOT NULL COMMENT '日志类型 info|warning|error|success',
  `content` TEXT NOT NULL COMMENT '日志内容',
  `timestamp` BIGINT NOT NULL COMMENT '日志时间戳',
  `step` VARCHAR(200) COMMENT '所属步骤名称',
  `step_index` INT COMMENT '步骤索引',
  `c_t` BIGINT NOT NULL COMMENT '创建时间',
  `create_by` BIGINT NOT NULL COMMENT '创建人ID',
  `u_t` BIGINT NOT NULL COMMENT '更新时间',
  `update_by` BIGINT NOT NULL COMMENT '更新人ID',
  `is_deleted` TINYINT NOT NULL DEFAULT 0 COMMENT '记录状态 0:正常 1:删除',
  PRIMARY KEY (`id`),
  INDEX `idx_deploy_task_detail_id` (`deploy_task_detail_id`),
  INDEX `idx_log_type` (`log_type`),
  INDEX `idx_timestamp` (`timestamp`),
  INDEX `idx_step_index` (`step_index`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='部署日志表 - 用于存储部署过程中的日志信息';