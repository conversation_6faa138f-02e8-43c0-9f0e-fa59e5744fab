#!/bin/bash

# 设置代理环境变量
export http_proxy=http://***********:1080
export https_proxy=http://***********:1080
export NO_PROXY=localhost,127.0.0.1,::1,***********/16,*************,0.0.0.0,.sprucetec.com,***********,yunshanmeicai.com,*.local,*********,**********, *********/12
export http_proxy=$HTTP_PROXY
export https_proxy=$HTTPS_PROXY
export no_proxy=$NO_PROXY

echo "已设置代理环境变量"

# 配置Docker代理
mkdir -p ~/.docker
cat > ~/.docker/config.json <<EOF
{
  "proxies": {
    "default": {
      "httpProxy": "$HTTP_PROXY",
      "httpsProxy": "$HTTPS_PROXY",
      "noProxy": "$NO_PROXY"
    }
  }
}
EOF

echo "已配置Docker代理"

# 设置系统文件描述符限制
echo "设置系统文件描述符限制..."

# 临时增加当前会话的文件描述符限制
ulimit -n 65535
echo "当前会话文件描述符限制已设置为: $(ulimit -n)"

# 永久增加系统文件描述符限制
cat << 'EOF' | sudo tee /etc/security/limits.d/95-nofiles.conf
* soft nofile 65535
* hard nofile 65535
root soft nofile 65535
root hard nofile 65535
EOF

echo "已添加文件描述符限制配置到系统"

# 设置系统inotify watches限制
echo "设置系统inotify watches限制..."

# 临时增加inotify limits
echo "临时增加inotify watches限制..."
sudo sysctl -w fs.inotify.max_user_watches=524288
sudo sysctl -w fs.inotify.max_user_instances=512
sudo sysctl -w fs.file-max=65536

echo "新的inotify watches限制："
cat /proc/sys/fs/inotify/max_user_watches
echo "新的inotify instances限制："
cat /proc/sys/fs/inotify/max_user_instances

# 永久增加inotify limits
echo "添加永久性inotify限制设置..."
cat << 'EOF' | sudo tee /etc/sysctl.d/99-inotify.conf
fs.inotify.max_user_watches = 524288
fs.inotify.max_user_instances = 512
fs.file-max = 65536
EOF

sudo sysctl -p /etc/sysctl.d/99-inotify.conf
echo "已添加永久性inotify限制配置"

# 配置Docker服务的限制
echo "配置Docker服务的文件描述符限制..."

# 确保目录存在
sudo mkdir -p /etc/systemd/system/docker.service.d/

# 创建配置文件
cat << 'EOF' | sudo tee /etc/systemd/system/docker.service.d/limits.conf
[Service]
LimitNOFILE=1048576
EOF

echo "重新加载systemd配置..."
sudo systemctl daemon-reload

# 询问是否重启Docker
read -p "是否重启Docker服务以应用设置? (y/n): " restart_docker
if [ "$restart_docker" = "y" ] || [ "$restart_docker" = "Y" ]; then
  echo "正在重启Docker服务..."
  sudo systemctl restart docker
fi

# 确保Harbor证书目录存在
echo "检查Harbor证书目录..."
HARBOR_CERTS_DIR="/data/www/harbor/certs"
if [ ! -d "$HARBOR_CERTS_DIR" ]; then
  echo "Harbor证书目录不存在，请先配置Harbor证书: $HARBOR_CERTS_DIR"
  echo "您可以从Harbor主机复制证书，或生成自签名证书"
  read -p "是否继续创建集群（不挂载Harbor证书）? (y/n): " continue_without_certs
  if [ "$continue_without_certs" != "y" ] && [ "$continue_without_certs" != "Y" ]; then
    echo "请先配置Harbor证书后再运行此脚本"
    exit 1
  fi
  MOUNT_HARBOR_CERTS=false
else
  echo "找到Harbor证书目录: $HARBOR_CERTS_DIR"
  MOUNT_HARBOR_CERTS=true
fi

# 创建带有增强文件描述符限制和Harbor支持的kind集群配置文件
if [ "$MOUNT_HARBOR_CERTS" = "true" ]; then
  # 包含Harbor证书的配置
  cat > kind-config.yaml <<EOF
kind: Cluster
apiVersion: kind.x-k8s.io/v1alpha4
name: multi-node-cluster
nodes:
- role: control-plane
  kubeadmConfigPatches:
  - |
    kind: InitConfiguration
    nodeRegistration:
      kubeletExtraArgs:
        system-reserved: memory=1Gi
  extraMounts:
  - hostPath: /etc/security/limits.d/95-nofiles.conf
    containerPath: /etc/security/limits.d/95-nofiles.conf
  - hostPath: /etc/sysctl.d/99-inotify.conf
    containerPath: /etc/sysctl.d/99-inotify.conf
  - hostPath: /data/www/harbor/certs
    containerPath: /etc/docker/certs.d/*************
  - hostPath: /data/www/harbor/certs/ca.crt
    containerPath: /usr/local/share/ca-certificates/harbor-ca.crt
- role: worker
  kubeadmConfigPatches:
  - |
    kind: JoinConfiguration
    nodeRegistration:
      kubeletExtraArgs:
        system-reserved: memory=1Gi
  extraMounts:
  - hostPath: /etc/security/limits.d/95-nofiles.conf
    containerPath: /etc/security/limits.d/95-nofiles.conf
  - hostPath: /etc/sysctl.d/99-inotify.conf
    containerPath: /etc/sysctl.d/99-inotify.conf
  - hostPath: /data/www/harbor/certs
    containerPath: /etc/docker/certs.d/*************
  - hostPath: /data/www/harbor/certs/ca.crt
    containerPath: /usr/local/share/ca-certificates/harbor-ca.crt
- role: worker
  kubeadmConfigPatches:
  - |
    kind: JoinConfiguration
    nodeRegistration:
      kubeletExtraArgs:
        system-reserved: memory=1Gi
  extraMounts:
  - hostPath: /etc/security/limits.d/95-nofiles.conf
    containerPath: /etc/security/limits.d/95-nofiles.conf
  - hostPath: /etc/sysctl.d/99-inotify.conf
    containerPath: /etc/sysctl.d/99-inotify.conf
  - hostPath: /data/www/harbor/certs
    containerPath: /etc/docker/certs.d/*************
  - hostPath: /data/www/harbor/certs/ca.crt
    containerPath: /usr/local/share/ca-certificates/harbor-ca.crt
- role: worker
  kubeadmConfigPatches:
  - |
    kind: JoinConfiguration
    nodeRegistration:
      kubeletExtraArgs:
        system-reserved: memory=1Gi
  extraMounts:
  - hostPath: /etc/security/limits.d/95-nofiles.conf
    containerPath: /etc/security/limits.d/95-nofiles.conf
  - hostPath: /etc/sysctl.d/99-inotify.conf
    containerPath: /etc/sysctl.d/99-inotify.conf
  - hostPath: /data/www/harbor/certs
    containerPath: /etc/docker/certs.d/*************
  - hostPath: /data/www/harbor/certs/ca.crt
    containerPath: /usr/local/share/ca-certificates/harbor-ca.crt
networking:
  podSubnet: "**********/16"
  serviceSubnet: "*********/12"
kubeadmConfigPatches:
- |
  kind: KubeletConfiguration
  evictionHard:
    memory.available: "200Mi"
    nodefs.available: "10%"
    nodefs.inodesFree: "5%"
  systemReserved:
    memory: "1Gi"
  maxPods: 110
  failSwapOn: false
  # 增加文件限制
  fileMaximum: 65536
  # 增加打开文件描述符限制
  maxOpenFiles: 65536
- |
  kind: KubeProxyConfiguration
  conntrack:
    maxPerCore: 0
    min: 131072
    tcpEstablishedTimeout: 24h0m0s
EOF
else
  # 不包含Harbor证书的配置
  cat > kind-config.yaml <<EOF
kind: Cluster
apiVersion: kind.x-k8s.io/v1alpha4
name: multi-node-cluster
nodes:
- role: control-plane
  kubeadmConfigPatches:
  - |
    kind: InitConfiguration
    nodeRegistration:
      kubeletExtraArgs:
        system-reserved: memory=1Gi
  extraMounts:
  - hostPath: /etc/security/limits.d/95-nofiles.conf
    containerPath: /etc/security/limits.d/95-nofiles.conf
  - hostPath: /etc/sysctl.d/99-inotify.conf
    containerPath: /etc/sysctl.d/99-inotify.conf
- role: worker
  kubeadmConfigPatches:
  - |
    kind: JoinConfiguration
    nodeRegistration:
      kubeletExtraArgs:
        system-reserved: memory=1Gi
  extraMounts:
  - hostPath: /etc/security/limits.d/95-nofiles.conf
    containerPath: /etc/security/limits.d/95-nofiles.conf
  - hostPath: /etc/sysctl.d/99-inotify.conf
    containerPath: /etc/sysctl.d/99-inotify.conf
- role: worker
  kubeadmConfigPatches:
  - |
    kind: JoinConfiguration
    nodeRegistration:
      kubeletExtraArgs:
        system-reserved: memory=1Gi
  extraMounts:
  - hostPath: /etc/security/limits.d/95-nofiles.conf
    containerPath: /etc/security/limits.d/95-nofiles.conf
  - hostPath: /etc/sysctl.d/99-inotify.conf
    containerPath: /etc/sysctl.d/99-inotify.conf
- role: worker
  kubeadmConfigPatches:
  - |
    kind: JoinConfiguration
    nodeRegistration:
      kubeletExtraArgs:
        system-reserved: memory=1Gi
  extraMounts:
  - hostPath: /etc/security/limits.d/95-nofiles.conf
    containerPath: /etc/security/limits.d/95-nofiles.conf
  - hostPath: /etc/sysctl.d/99-inotify.conf
    containerPath: /etc/sysctl.d/99-inotify.conf
networking:
  podSubnet: "**********/16"
  serviceSubnet: "*********/12"
kubeadmConfigPatches:
- |
  kind: KubeletConfiguration
  evictionHard:
    memory.available: "200Mi"
    nodefs.available: "10%"
    nodefs.inodesFree: "5%"
  systemReserved:
    memory: "1Gi"
  maxPods: 110
  failSwapOn: false
  # 增加文件限制
  fileMaximum: 65536
  # 增加打开文件描述符限制
  maxOpenFiles: 65536
- |
  kind: KubeProxyConfiguration
  conntrack:
    maxPerCore: 0
    min: 131072
    tcpEstablishedTimeout: 24h0m0s
EOF
fi

echo "已创建增强的kind集群配置文件"

# 询问是否创建kind集群
read -p "是否创建kind集群? (y/n): " create_cluster
if [ "$create_cluster" = "y" ] || [ "$create_cluster" = "Y" ]; then
  # 删除已存在的集群
  echo "检查并删除已存在的集群..."
  if kind get clusters | grep -q "multi-node-cluster"; then
    echo "发现已存在的multi-node-cluster集群，正在删除..."
    kind delete cluster --name multi-node-cluster
    echo "已删除旧集群"
  else
    echo "未发现已存在的multi-node-cluster集群"
  fi
  
  echo "正在创建新的kind集群..."
  kind create cluster --config kind-config.yaml
  
  echo "集群创建完成，验证节点状态:"
  kubectl get nodes
  
  # 为集群节点容器配置增加限制
  echo "为Kind集群节点增加文件描述符和inotify限制..."
  NODE_CONTAINERS=$(docker ps --filter "name=kind" --format "{{.ID}}")
  for CONTAINER_ID in $NODE_CONTAINERS; do
    CONTAINER_NAME=$(docker inspect --format '{{.Name}}' $CONTAINER_ID | sed 's/\///')
    echo "设置容器 $CONTAINER_NAME ($CONTAINER_ID) 的系统限制..."
    docker exec $CONTAINER_ID sh -c "sysctl -w fs.inotify.max_user_watches=524288"
    docker exec $CONTAINER_ID sh -c "sysctl -w fs.inotify.max_user_instances=512"
    docker exec $CONTAINER_ID sh -c "sysctl -w fs.file-max=65536"
  done
  
  # 配置Harbor访问
  if [ "$MOUNT_HARBOR_CERTS" = "true" ]; then
    echo "配置Kind集群Harbor证书..."
    for CONTAINER_ID in $NODE_CONTAINERS; do
      CONTAINER_NAME=$(docker inspect --format '{{.Name}}' $CONTAINER_ID | sed 's/\///')
      echo "配置容器 $CONTAINER_NAME 的Harbor证书..."
      
      # 更新CA证书
      docker exec $CONTAINER_ID sh -c "update-ca-certificates"
      
      # 重启containerd以使证书生效
      docker exec $CONTAINER_ID sh -c "systemctl restart containerd"
    done
    echo "Harbor证书配置完成"
  fi
  
  # 创建Harbor访问密钥
  echo "创建Harbor访问密钥..."
  kubectl create secret docker-registry harbor-secret \
    --docker-server=************* \
    --docker-username=admin \
    --docker-password=Harbor12345 \
    --docker-email=<EMAIL> \
    --namespace=default || echo "密钥可能已存在"
  
  echo "Harbor配置完成，可以使用以下命令测试拉取镜像："
  echo "kubectl run test-pod --image=*************/library/hello-world:latest --image-pull-policy=Always"
fi

# 创建Pod资源限制的补丁文件，以备后用
cat > pod-limits-patch.yaml << 'EOF'
spec:
  template:
    spec:
      containers:
      - name: workflow-controller  # 根据实际情况调整容器名称
        resources:
          limits:
            cpu: "1"
            memory: "1Gi"
          requests:
            cpu: "100m"
            memory: "256Mi"
        securityContext:
          capabilities:
            add:
            - SYS_RESOURCE
EOF

echo "========================================================"
echo "集群设置完成。如果Pod仍然出现'too many open files'错误，使用以下命令应用补丁："
echo "kubectl -n <namespace> patch deployment <deployment-name> --patch \"$(cat pod-limits-patch.yaml)\""
echo "========================================================"

echo "脚本执行完毕" 