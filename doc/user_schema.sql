-- 用户表结构
CREATE TABLE `user` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `username` VARCHAR(255) NOT NULL UNIQUE COMMENT '用户名',
  `dispname` VARCHAR(255) NOT NULL COMMENT '显示名',
  `phone` VARCHAR(20) NOT NULL COMMENT '手机号',
  `email` VARCHAR(255) UNIQUE COMMENT '邮箱',
  `role` INT COMMENT '角色',
  `c_t` BIGINT NOT NULL COMMENT '创建时间',
  `u_t` BIGINT NOT NULL COMMENT '更新时间',
  `status` TINYINT DEFAULT 1 COMMENT '状态',
  `current_app` VARCHAR(255) NOT NULL COMMENT '当前应用',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';
