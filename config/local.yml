# 服务器配置
server:
  http:
    addr: "0.0.0.0"
    port: 8001
    timeout: 30s

# 环境配置
env: dev  # dev | stage | prod

# 数据库配置
data:
  mysql:
    deploy: meicai_rw:MeiCai@123@tcp(10.196.7.4:3306)/deploy?charset=utf8mb4&parseTime=True&loc=Local
    cmdb: meicai_rw:MeiCai@123@tcp(10.196.7.4:3306)/cmdb?charset=utf8mb4&parseTime=True&loc=Local

# 数据库连接池配置
mysql:
  max_idle_conns: 10       # 最大空闲连接数
  max_open_conns: 100      # 最大打开连接数
  conn_max_lifetime: 3600s # 连接最大存活时间

log:
  level: info  # debug | info | warn | error
  format: console  # console | json
  encoding: console           # json or console
  log_file_name: "./storage/server.log"
  max_backups: 30              # 日志文件最多保存多少个备份
  max_age: 7                   # 文件最多保存多少天
  max_size: 1024               # 每个日志文件保存的最大尺寸 单位：M
  compress: true               # 是否压缩

# RPC 服务配置
rpc:
  srv_url: "http://opsapi.stage.yunshanmeicai.com"

# SSO 配置
sso:
  token: test_user_token
  principal: devops_principal
  sso_addr: http://auth-api.sprucetec.com/user/tokeninfo
  system_key: auth
  login_url: http://sso.test.yunshanmeicai.com/user/login?system_key=
  timeout: 5000

# HTTP 配置
http:
  port: 8001

# 主机配置
host_url: "dashboard.test.yunshanmeicai.com:8001"

# Argo Workflow配置
workflow:
  namespace: "argo"
  sync_interval: 30s  # 工作流状态同步间隔
  image_registry: "registry.example.com"  # 镜像仓库地址 

# GitLab配置
gitlab:
  base_url: "https://git.sprucetec.com/api/v4"
  token: "7jVixkffs4BViNntyxjw"